
/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */

const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const path = require('path');

// Monorepo setup
const projectRoot = path.resolve(__dirname, '../..');

const config = getDefaultConfig(__dirname);

// 1. Watch all files in the monorepo
config.watchFolders = [projectRoot];
// 2. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(__dirname, 'node_modules'),
];
// 3. Force Metro to resolve (sub)dependencies only from the top-level node_modules
config.resolver.disableHierarchicalLookup = true;

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
