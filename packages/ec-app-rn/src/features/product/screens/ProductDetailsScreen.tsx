import React, { useEffect, useState, useRef } from 'react';
import { LayoutChangeEvent, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';

import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Share,
  Image,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
// import FastImage from 'react-native-fast-image'; // Keep if preferred, else use Image
import Icon from 'react-native-vector-icons/Ionicons'; // Changed to Ionicons for consistency
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { RootStackParamList } from '../../../navigation';
import { useThemeColor } from '../../../shared/hooks/useThemeColor';
// Mock Supabase functions for now, or adapt existing ones
// import { getProductById, addToCart, isProductInFavorites, addToFavorites, removeFromFavorites } from '../lib/supabase';

type ProductDetailsRouteProp = RouteProp<RootStackParamList, 'ProductDetails'>;
type ProductDetailsNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

const { width, height } = Dimensions.get('window');

const mockProduct = {
  id: '1',
  name: '真无线蓝牙耳机 Pro 主动降噪 空间音频 40小时续航',
  price: 1299,
  originalPrice: 1599,
  discount: '8.1折',
  description: '主动降噪 | 空间音频 | 40小时续航',
  images: [
    'https://i.imgur.com/4l3gU22.png', // Placeholder headphone image
    'https://i.imgur.com/JPlS2Vz.png', // Placeholder watch image
    'https://i.imgur.com/gTj9s2Y.png', // Placeholder phone image
  ],
  colors: ['经典黑', '珍珠白', '石墨灰'],
  versions: ['标准版', 'Pro版'],
  specs: [
    { label: '品牌', value: 'Apple' },
    { label: '型号', value: 'AirPods Pro 2' },
    { label: '颜色', value: '白色' },
    { label: '连接方式', value: '蓝牙5.0' },
    { label: '续航时间', value: '40小时' },
    { label: '功能特点', value: '主动降噪, 空间音频, 防水防汗' },
  ],
  reviews: {
    averageRating: 4.9,
    totalReviews: 12345,
    tags: ['音质好 (1.2k)', '降噪效果明显 (980)', '佩戴舒适 (750)', '连接稳定 (600)'],
    items: [
      {
        id: 'r1',
        userAvatar: 'https://i.imgur.com/MQHYB.jpeg',
        userName: '中**',
        rating: 5,
        text: '音质非常好，降噪效果明显，基础款的优选。续航能力优秀，小巧便携，整体非常满意！',
        date: '2023-05-20',
        images: ['https://i.imgur.com/4l3gU22.png'],
      },
    ],
  },
  relatedProducts: [
    { id: 'p1', name: '头戴式无线耳机', price: '899', image: 'https://i.imgur.com/JPlS2Vz.png' },
    { id: 'p2', name: 'TWS无线蓝牙耳机', price: '149', image: 'https://i.imgur.com/gTj9s2Y.png' },
    { id: 'p3', name: '智能手表 SE', price: '2199', image: 'https://i.imgur.com/4l3gU22.png' },
  ],
};

const HEADER_TABS = ['商品', '评价', '详情', '推荐'] as const;
const HEADER_VIEW_OFFSET = 50; // Adjust this to the actual height of your sticky header tabs
type HeaderTab = typeof HEADER_TABS[number];

export default function ProductDetailsScreen() {
  const route = useRoute<ProductDetailsRouteProp>();
  const navigation = useNavigation<ProductDetailsNavigationProp>();
  const { id } = route.params;

  const [product, setProduct] = useState<any>(mockProduct); // Using mock for now
  const [loading, setLoading] = useState(false); // Assume loaded for mock
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedColor, setSelectedColor] = useState(mockProduct.colors[0]);
  const [selectedVersion, setSelectedVersion] = useState(mockProduct.versions[0]);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [activeHeaderTab, setActiveHeaderTab] = useState<HeaderTab>('商品');
  const [sectionLayouts, setSectionLayouts] = useState<Record<HeaderTab, { y: number; height: number }>>({
    '商品': { y: 0, height: 0 },
    '评价': { y: 0, height: 0 },
    '详情': { y: 0, height: 0 },
    '推荐': { y: 0, height: 0 },
  });
  const isScrollingProgrammaticallyRef = useRef(false);

  const scrollViewRef = useRef<ScrollView>(null);
  const sectionRefs = {
    '商品': useRef<View>(null),
    '评价': useRef<View>(null),
    '详情': useRef<View>(null),
    '推荐': useRef<View>(null),
  };

  // Theme colors (simplified for brevity, adapt useThemeColor if needed)
  const colors = {
    background: '#FFFFFF',
    text: '#333333',
    primary: '#FF3B30',
    secondaryText: '#666666',
    borderColor: '#EEEEEE',
    activeTab: '#FF3B30',
    inactiveTab: '#333333',
    lightText: '#999999',
  };

  // useEffect(() => { /* Fetch actual product data if not using mock */ }, [id]);

  const handleQuantityChange = (delta: number) => {
    setQuantity((prev) => Math.max(1, prev + delta));
  };

  const onShare = async () => {
    try {
      await Share.share({
        message: `Check out this product: ${product?.name}`,
        url: `yourapp://product/${id}`, // Replace with your app's deep link structure
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  const onScroll = (event: any) => {
    if (isScrollingProgrammaticallyRef.current) {
      return;
    }
    const scrollY = event.nativeEvent.contentOffset.y;
    let currentTab: HeaderTab = HEADER_TABS[0]; // Default to the first tab

    // Find the last section whose top is at or above the current scroll position (adjusted for header)
    for (let i = HEADER_TABS.length - 1; i >= 0; i--) {
      const tab = HEADER_TABS[i];
      const layout = sectionLayouts[tab];
      // Check if layout.y is a valid number (has been measured)
      if (layout && typeof layout.y === 'number') {
        // Add a small buffer (e.g., 1 pixel) for more robust detection
        // A section is considered active if its top edge (layout.y) is at or above the scroll view's top edge,
        // considering the HEADER_VIEW_OFFSET.
        if (scrollY >= layout.y - HEADER_VIEW_OFFSET - 1) {
          currentTab = tab;
          break;
        }
      }
    }

    if (activeHeaderTab !== currentTab) {
      setActiveHeaderTab(currentTab);
    }
  };

  const scrollToSection = (tab: HeaderTab) => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    setActiveHeaderTab(tab); // Set active tab immediately for responsive UI

    const layout = sectionLayouts[tab];
    if (layout && typeof layout.y === 'number' && scrollViewRef.current) {
      isScrollingProgrammaticallyRef.current = true;
      scrollViewRef.current.scrollTo({ y: layout.y - HEADER_VIEW_OFFSET, animated: true });
      // Reset the flag after scroll animation. Timeout is a common way.
      // Adjust duration based on typical scroll animation time.
      setTimeout(() => {
        isScrollingProgrammaticallyRef.current = false;
      }, 800); // 800ms should be enough for most animations
    } else {
      // Fallback if layout not measured yet (e.g., on initial load very quickly)
      // This can also happen if a section has zero height or is not rendered.
      console.warn(`Layout for section ${tab} not available yet. Attempting direct measure.`);
      const targetRef = sectionRefs[tab];
      if (targetRef.current && scrollViewRef.current && scrollViewRef.current.getInnerViewNode) {
        try {
          targetRef.current.measureLayout(
            scrollViewRef.current.getInnerViewNode(),
            (x, yCoord) => {
              isScrollingProgrammaticallyRef.current = true;
              scrollViewRef.current?.scrollTo({ y: yCoord - HEADER_VIEW_OFFSET, animated: true });
              setTimeout(() => {
                isScrollingProgrammaticallyRef.current = false;
              }, 800);
            },
            () => console.error(`Fallback measureLayout failed for ${tab}`)
          );
        } catch (e) {
          console.error(`Error during fallback measureLayout for ${tab}:`, e);
        }
      } else {
        console.error(`Cannot scroll to section ${tab}: ref or ScrollView not ready.`);
      }
    }
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.background }]}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerButton}>
        <Icon name="chevron-back-outline" size={24} color={colors.text} />
      </TouchableOpacity>
      <View style={styles.headerTabsContainer}>
        {HEADER_TABS.map((tab) => (
          <TouchableOpacity key={tab} onPress={() => scrollToSection(tab)} style={styles.headerTab}>
            <Text style={[
              styles.headerTabText,
              { color: activeHeaderTab === tab ? colors.activeTab : colors.inactiveTab },
              activeHeaderTab === tab && styles.headerTabTextActive
            ]}>
              {tab}
            </Text>
            {activeHeaderTab === tab && <View style={[styles.activeTabIndicator, {backgroundColor: colors.activeTab}]} />}
          </TouchableOpacity>
        ))}
      </View>
      <TouchableOpacity onPress={onShare} style={styles.headerButton}>
        <Icon name="share-social-outline" size={22} color={colors.text} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton}>
        <Icon name="ellipsis-horizontal-outline" size={22} color={colors.text} />
      </TouchableOpacity>
    </View>
  );

  const renderImageCarousel = () => (
    <View style={styles.imageCarouselContainer}>
      <FlatList<string>
        data={product.images}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item: string, index: number) => `img-${item}-${index}`}
        renderItem={({ item }: { item: string }) => (
          <Image source={{ uri: item }} style={styles.mainImage} resizeMode="contain" />
        )}
        onMomentumScrollEnd={(event: NativeSyntheticEvent<NativeScrollEvent>) => {
          const newIndex = Math.round(event.nativeEvent.contentOffset.x / width);
          if (activeImageIndex !== newIndex) {
            setActiveImageIndex(newIndex);
          }
        }}
      />
      <View style={styles.imageIndicatorContainer}>
        {product.images.map((_image: string, index: number) => (
          <View
            key={`dot-${index}`}
            style={[
              styles.imageIndicatorDot,
              { backgroundColor: activeImageIndex === index ? colors.primary : colors.lightText },
            ]}
          />
        ))}
      </View>
    </View>
  );

  const renderProductInfo = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.priceContainer}> 
        <Text style={[styles.currentPrice, { color: colors.primary }]}>¥{product.price}</Text>
        {product.originalPrice && <Text style={styles.originalPrice}>¥{product.originalPrice}</Text>}
        {product.discount && <View style={styles.discountTag}><Text style={styles.discountText}>{product.discount}</Text></View>}
      </View>
      <Text style={styles.productName}>{product.name}</Text>
      <Text style={styles.productShortDesc}>{product.description}</Text>
    </View>
  );

  const renderSelectionRow = (label: string, value: string, onPress?: () => void) => (
    <TouchableOpacity style={styles.selectionRow} onPress={onPress} disabled={!onPress}>
      <Text style={styles.selectionLabel}>{label}</Text>
      <Text style={styles.selectionValue}>{value}</Text>
      <Icon name="chevron-forward-outline" size={18} color={colors.lightText} />
    </TouchableOpacity>
  );
  
  const renderProductSelection = () => (
    <View style={styles.sectionContainer}>
      {renderSelectionRow('领券', '满1000减100, 满2000减250', () => console.log('Open coupons'))}
      {renderSelectionRow('促销', '购买立减50元', () => console.log('Open promotions'))}
      <View style={styles.divider} />
      {renderSelectionRow('配送至', '上海市浦东新区 张江高科技园区', () => console.log('Change address'))}
      {renderSelectionRow('已选', `${selectedColor}, ${selectedVersion}, ${quantity}件`, () => console.log('Open SKU selector'))}
    </View>
  );

  const renderProductSpecs = () => (
    <View ref={sectionRefs['详情']} style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>商品详情</Text>
      {product.specs.map((spec: {label: string, value: string}) => (
        <View key={spec.label} style={styles.specItem}>
          <Text style={styles.specLabel}>{spec.label}</Text>
          <Text style={styles.specValue}>{spec.value}</Text>
        </View>
      ))}
    </View>
  );

  const renderReviews = () => (
    <View ref={sectionRefs['评价']} style={styles.sectionContainer}>
      <View style={styles.reviewHeader}>
        <Text style={styles.sectionTitle}>用户评价 ({product.reviews.averageRating} ★)</Text>
        <TouchableOpacity>
          <Text style={{color: colors.primary}}>{product.reviews.totalReviews}条评论 {'>'}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.reviewTagsContainer}>
        {product.reviews.tags.map((tag: string) => (
          <View key={tag} style={styles.reviewTag}><Text style={styles.reviewTagText}>{tag}</Text></View>
        ))}
      </View>
      {/* Simplified review item - expand later */}
      {product.reviews.items.map((review: any) => (
         <View key={review.id} style={styles.reviewItem}>
            <Image source={{uri: review.userAvatar}} style={styles.reviewAvatar} />
            <View style={styles.reviewContent}>
                <Text style={styles.reviewUser}>{review.userName}</Text>
                <Text style={styles.reviewText} numberOfLines={2}>{review.text}</Text>
            </View>
         </View>
      ))}
    </View>
  );

  const renderRelatedProducts = () => (
    <View ref={sectionRefs['推荐']} style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>相关推荐</Text>
      <FlatList 
        data={product.relatedProducts}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.id}
        renderItem={({item}: {item: {id: string, name: string, price: string, image: string}}) => (
            <TouchableOpacity style={styles.relatedItemCard}>
                <Image source={{uri: item.image}} style={styles.relatedItemImage} />
                <Text style={styles.relatedItemName} numberOfLines={1}>{item.name}</Text>
                <Text style={[styles.relatedItemPrice, {color: colors.primary}]}>¥{item.price}</Text>
            </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderBottomBar = () => (
    <View style={[styles.bottomBar, { backgroundColor: colors.background }]}>
      <TouchableOpacity style={styles.bottomBarIconContainer}>
        <Icon name="storefront-outline" size={22} color={colors.text} />
        <Text style={styles.bottomBarIconText}>店铺</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.bottomBarIconContainer}>
        <Icon name="chatbubble-ellipses-outline" size={22} color={colors.text} />
        <Text style={styles.bottomBarIconText}>客服</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.bottomBarIconContainer} onPress={() => setIsFavorite(!isFavorite)}>
        <Icon name={isFavorite ? "heart" : "heart-outline"} size={22} color={isFavorite ? colors.primary : colors.text} />
        <Text style={[styles.bottomBarIconText, {color: isFavorite ? colors.primary : colors.text}]}>收藏</Text>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.bottomBarButton, styles.addToCartButton]}>
        <Text style={styles.bottomBarButtonText}>加入购物车</Text>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.bottomBarButton, styles.buyNowButton, {backgroundColor: colors.primary}]}>
        <Text style={[styles.bottomBarButtonText, {color: '#fff'}]}>立即购买</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return <ActivityIndicator size="large" style={{ flex: 1, justifyContent: 'center' }} />;
  }

  if (!product) {
    return <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}><Text>Product not found.</Text></View>;
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
      {renderHeader()}
      <ScrollView 
        ref={scrollViewRef}
        style={styles.scrollView}
        onScroll={onScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        <View
          ref={sectionRefs['商品']}
          onLayout={(event: LayoutChangeEvent) => {
            const { y, height } = event.nativeEvent.layout;
            setSectionLayouts(prev => ({ ...prev, '商品': { y, height } }));
          }}
        >
          {renderImageCarousel()}
          {renderProductInfo()}
          {renderProductSelection()}
        </View>

        <View
          ref={sectionRefs['评价']}
          onLayout={(event: LayoutChangeEvent) => {
            const { y, height } = event.nativeEvent.layout;
            setSectionLayouts(prev => ({ ...prev, '评价': { y, height } }));
          }}
        >
          {renderReviews()}
        </View>

        <View
          ref={sectionRefs['详情']}
          onLayout={(event: LayoutChangeEvent) => {
            const { y, height } = event.nativeEvent.layout;
            setSectionLayouts(prev => ({ ...prev, '详情': { y, height } }));
          }}
        >
          {renderProductSpecs()}
        </View>

        <View
          ref={sectionRefs['推荐']}
          onLayout={(event: LayoutChangeEvent) => {
            const { y, height } = event.nativeEvent.layout;
            setSectionLayouts(prev => ({ ...prev, '推荐': { y, height } }));
          }}
        >
          {renderRelatedProducts()}
        </View>
        <View style={{ height: 60 }} /> {/* Spacer for bottom bar */}
      </ScrollView>
      {renderBottomBar()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1 },
  scrollView: { flex: 1 }, 
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderColor: '#eee',
    height: 50,
    zIndex: 10, // Ensure header is on top
  },
  headerButton: { padding: 5 },
  headerTabsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTab: {
    paddingHorizontal: 10,
    alignItems: 'center',
    height: '100%',
    justifyContent: 'center',
  },
  headerTabText: {
    fontSize: 15,
    fontWeight: '500',
  },
  headerTabTextActive: {
    fontWeight: 'bold',
  },
  activeTabIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 2.5,
    width: '70%', // Adjust width of indicator
  },
  imageCarouselContainer: {
    height: width, // Square images for carousel
    backgroundColor: '#f0f0f0',
  },
  mainImage: {
    width: width,
    height: width,
  },
  imageIndicatorContainer: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  imageIndicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 3,
  },
  sectionContainer: {
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  currentPrice: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
    marginBottom: 2, // Align baseline with currentPrice
  },
  discountTag: {
    backgroundColor: '#FFEBEB',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
    marginBottom: 3, // Align baseline
  },
  discountText: {
    color: '#FF3B30',
    fontSize: 12,
    fontWeight: 'bold',
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    lineHeight: 24, // Improve readability
  },
  productShortDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20, // Improve readability
  },
  selectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  selectionLabel: {
    fontSize: 14,
    color: '#666',
    width: 60,
  },
  selectionValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    textAlign: 'left',
    marginLeft: 10,
  },
  divider: { height: 1, backgroundColor: '#f0f0f0', marginVertical: 8 },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  specItem: {
    flexDirection: 'row',
    paddingVertical: 6,
  },
  specLabel: {
    width: 80,
    color: '#666',
    fontSize: 13,
  },
  specValue: {
    flex: 1,
    color: '#333',
    fontSize: 13,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  reviewTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  reviewTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  reviewTagText: { fontSize: 12, color: '#666' }, 
  reviewItem: { flexDirection: 'row', alignItems: 'flex-start', marginBottom: 15, paddingBottom: 10, borderBottomWidth: 1, borderBottomColor: '#f9f9f9' },
  reviewAvatar: { width: 30, height: 30, borderRadius: 15, marginRight: 10, marginTop: 2 },
  reviewContent: { flex: 1 },
  reviewUser: { fontSize: 13, fontWeight: 'bold', marginBottom: 3},
  reviewText: { fontSize: 13, color: '#444', lineHeight: 18},
  relatedItemCard: {
    width: width / 3 - 15, // Adjusted for better spacing
    marginRight: 10,
    // alignItems: 'center', // Removed for left-align text
  },
  relatedItemImage: {
    width: '100%',
    height: width / 3 - 15,
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    marginBottom: 5,
  },
  relatedItemName: {
    fontSize: 12,
    // textAlign: 'center', // Removed for left-align
    color: '#333',
    marginBottom: 2,
  },
  relatedItemPrice: {
    fontSize: 13,
    fontWeight: 'bold',
  },
  bottomBar: {
    flexDirection: 'row',
    height: 55,
    borderTopWidth: 1,
    borderColor: '#eee',
    alignItems: 'center',
    paddingHorizontal: 5,
    backgroundColor: '#fff', // Ensure background for visibility
  },
  bottomBarIconContainer: {
    flex: 0.6, // Adjusted flex for smaller icons
    alignItems: 'center',
    justifyContent: 'center',
    // paddingHorizontal: 5, // Reduced padding
  },
  bottomBarIconText: {
    fontSize: 10,
    marginTop: 2,
    color: '#666',
  },
  bottomBarButton: {
    flex: 1, // Adjusted flex for buttons
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  addToCartButton: {
    backgroundColor: '#FFB800', // Example color from prototype (yellowish)
  },
  buyNowButton: {
    // backgroundColor will be set by colors.primary (reddish)
  },
  bottomBarButtonText: {
    color: '#333', // Default text color for Add to Cart
    fontSize: 14,
    fontWeight: 'bold',
  },
});
