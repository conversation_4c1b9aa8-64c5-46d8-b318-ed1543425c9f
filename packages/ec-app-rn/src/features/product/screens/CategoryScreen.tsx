import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';

// Type definitions for our mock data
interface Brand {
  id: string;
  name: string;
  image: string;
}

interface SubCategoryItem {
  id: string;
  name: string;
  image: string;
}

interface SubCategory {
  title: string;
  items: SubCategoryItem[];
}

interface CategoryContent {
  banner: string;
  brands: Brand[];
  subCategories: SubCategory[];
}

// Use a mapped type for categoriesData to get strong typing on keys
type CategoryName = '推荐' | '手机数码' | '电脑办公' | '家用电器' | '服装鞋包' | '美妆个护' | '食品生鲜' | '家居家装' | '运动户外' | '医药保健';

const categoriesData: Record<CategoryName, CategoryContent> = {
  '推荐': {
    banner: 'https://i.imgur.com/UPhPb2D.png', // Sale banner
    brands: [
      { id: '1', name: 'Apple', image: 'https://i.imgur.com/4l3gU22.png' },
      { id: '2', name: 'Samsung', image: 'https://i.imgur.com/JPlS2Vz.png' },
      { id: '3', name: 'Xiaomi', image: 'https://i.imgur.com/tG3aK8A.png' },
    ],
    subCategories: [
      {
        title: '手机通讯',
        items: [
          { id: '1', name: '手机', image: 'https://i.imgur.com/gTj9s2Y.png' },
          { id: '2', name: '手机壳', image: 'https://i.imgur.com/5a2zGQS.png' },
          { id: '3', name: '充电器', image: 'https://i.imgur.com/O4MwaeG.png' },
        ],
      },
    ],
  },
  '手机数码': {
    banner: 'https://i.imgur.com/UPhPb2D.png',
    brands: [
        { id: '1', name: 'Apple', image: 'https://i.imgur.com/4l3gU22.png' },
        { id: '2', name: 'Samsung', image: 'https://i.imgur.com/JPlS2Vz.png' },
        { id: '3', name: 'Xiaomi', image: 'https://i.imgur.com/tG3aK8A.png' },
        { id: '4', name: 'Huawei', image: 'https://i.imgur.com/sC0zt3I.png' },
    ],
    subCategories: [
      {
        title: '手机通讯',
        items: [
          { id: '1', name: '手机', image: 'https://i.imgur.com/gTj9s2Y.png' },
          { id: '2', name: '手机壳', image: 'https://i.imgur.com/5a2zGQS.png' },
          { id: '3', name: '充电器', image: 'https://i.imgur.com/O4MwaeG.png' },
          { id: '4', name: '数据线', image: 'https://i.imgur.com/sC0zt3I.png' },
          { id: '5', name: '耳机', image: 'https://i.imgur.com/UPhPb2D.png' },
        ],
      },
      {
        title: '电脑办公',
        items: [
          { id: '1', name: '笔记本', image: 'https://i.imgur.com/4l3gU22.png' },
          { id: '2', name: '平板电脑', image: 'https://i.imgur.com/JPlS2Vz.png' },
          { id: '3', name: '键盘', image: 'https://i.imgur.com/tG3aK8A.png' },
          { id: '4', name: '鼠标', image: 'https://i.imgur.com/gTj9s2Y.png' },
        ],
      },
    ],
  },
  '电脑办公': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '家用电器': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '服装鞋包': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '美妆个护': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '食品生鲜': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '家居家装': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '运动户外': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
  '医药保健': { banner: 'https://i.imgur.com/UPhPb2D.png', brands: [], subCategories: [] },
};

const mainCategories: CategoryName[] = Object.keys(categoriesData) as CategoryName[];

export default function CategoryScreen() {
  const [selectedCategory, setSelectedCategory] = useState<CategoryName>(mainCategories[0]);

  const currentCategoryData = categoriesData[selectedCategory];

  const renderSideBarItem = (item: CategoryName) => (
    <TouchableOpacity
      key={item}
      style={[styles.sidebarItem, selectedCategory === item && styles.sidebarItemSelected]}
      onPress={() => setSelectedCategory(item)}
    >
      <Text style={[styles.sidebarItemText, selectedCategory === item && styles.sidebarItemTextSelected]}>
        {item}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search-outline" size={20} color="#888" style={styles.searchIcon} />
          <TextInput placeholder="搜索商品" style={styles.searchInput} />
        </View>
      </View>
      <View style={styles.mainContent}>
        <View style={styles.sidebar}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {mainCategories.map(renderSideBarItem)}
          </ScrollView>
        </View>
        <View style={styles.contentArea}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <Image source={{ uri: currentCategoryData.banner }} style={styles.bannerImage} />
            
            {currentCategoryData.brands.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>热门品牌</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {currentCategoryData.brands.map((brand: Brand) => (
                    <View key={brand.id} style={styles.brandItem}>
                      <Image source={{ uri: brand.image }} style={styles.brandImage} />
                      <Text style={styles.brandName}>{brand.name}</Text>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}

            {currentCategoryData.subCategories.map((subCategory: SubCategory) => (
              <View key={subCategory.title} style={styles.section}>
                <Text style={styles.sectionTitle}>{subCategory.title}</Text>
                <View style={styles.subCategoryGrid}>
                  {subCategory.items.map((item: SubCategoryItem) => (
                    <View key={item.id} style={styles.subCategoryItem}>
                      <Image source={{ uri: item.image }} style={styles.subCategoryImage} />
                      <Text style={styles.subCategoryName}>{item.name}</Text>
                    </View>
                  ))}
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  searchContainer: {
    padding: 10,
    backgroundColor: '#fff',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingHorizontal: 15,
    height: 40,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 100,
    backgroundColor: '#f7f7f7',
  },
  sidebarItem: {
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sidebarItemSelected: {
    backgroundColor: '#fff',
    borderLeftWidth: 3,
    borderLeftColor: '#4A90E2',
  },
  sidebarItemText: {
    fontSize: 16,
    color: '#333',
  },
  sidebarItemTextSelected: {
    fontWeight: 'bold',
    color: '#4A90E2',
  },
  contentArea: {
    flex: 1,
    padding: 10,
  },
  bannerImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  brandItem: {
    alignItems: 'center',
    marginRight: 15,
  },
  brandImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
  },
  brandName: {
    marginTop: 5,
    fontSize: 12,
  },
  subCategoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  subCategoryItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 15,
  },
  subCategoryImage: {
    width: 70,
    height: 70,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  subCategoryName: {
    marginTop: 5,
    fontSize: 12,
    textAlign: 'center',
  },
});
