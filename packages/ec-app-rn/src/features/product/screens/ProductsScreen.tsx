import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Dimensions, ActivityIndicator, Image, SafeAreaView, StatusBar, Platform } from 'react-native';
import { useNavigation, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/Ionicons';
// Import directly from the absolute path
import { RootStackParamList } from '../../../navigation';
import { supabase } from '../../../lib/supabase';

const { width } = Dimensions.get('window');

// Define the Product type based on your Supabase table
// Define the Product type based on what we expect from Supabase
// We'll refine this after seeing the actual data structure
export interface Product {
  id: string; // Or number, depending on your DB schema
  name?: string; // Making these optional until we confirm the schema
  title?: string; // Alternative field name for product name
  description?: string; // Alternative to desc
  desc?: string;
  price?: number;
  original_price?: number;
  // sold_count doesn't exist in your DB
  rating?: number;
  badge?: string;
  image_url?: string;
  image?: string; // Alternative field name for image
  category_id?: string; // Possible field name for category
  category?: string;
  created_at?: string;
}

type ProductsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Products'>;
type ProductsScreenRouteProp = RouteProp<RootStackParamList, 'Products'>;

interface Props {
  navigation: ProductsScreenNavigationProp;
  route: ProductsScreenRouteProp;
}

const tabs = ['综合', '销量', '价格', '筛选'] as const;
type TabKey = typeof tabs[number];

export default function ProductsScreen({ route }: Props) {
  const navigation = useNavigation<ProductsScreenNavigationProp>();
  const { category: categoryParam } = route.params || {}; // category from navigation params

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabKey>('综合');
  const [priceAsc, setPriceAsc] = useState<boolean>(true);

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      let query = supabase.from('products').select('*');

      // Temporarily disable category filtering until we know the correct column name
      // if (categoryParam) {
      //   query = query.eq('category', categoryParam);
      // }

      // Apply sorting based on activeTab
      if (activeTab === '价格') {
        // Price sorting should work if 'price' column exists
        query = query.order('price', { ascending: priceAsc });
      } else {
        // Default sort for other tabs - using id as a fallback
        // We'll update this once we know the actual column names
        query = query.order('id', { ascending: false });
      }
      
      // Debug: Log the query we're about to execute
      console.log(`Executing query with tab: ${activeTab}, priceAsc: ${priceAsc}`);
      
      // '筛选' tab might require more complex logic or a separate UI, not handled in basic sort here

      const { data, error: dbError } = await query;

      if (dbError) {
        throw dbError;
      }
      
      // Debug: Log the first product to see its structure
      if (data && data.length > 0) {
        console.log('First product structure:', JSON.stringify(data[0], null, 2));
        // This will show all column names in your products table
      }
      
      setProducts(data || []);
    } catch (e: any) {
      setError(e.message || 'Failed to fetch products.');
      setProducts([]); // Clear products on error
    } finally {
      setLoading(false);
    }
  }, [categoryParam, activeTab, priceAsc]);

  // Function to check table structure
  const checkTableStructure = async () => {
    try {
      console.log('Checking products table structure...');
      // Get a single product to examine structure
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .limit(1);
      
      if (error) {
        console.error('Error fetching product structure:', error.message);
      } else if (data && data.length > 0) {
        console.log('Product table structure:', Object.keys(data[0]));
        console.log('Sample product:', JSON.stringify(data[0], null, 2));
      } else {
        console.log('No products found in the table');
      }
    } catch (e) {
      console.error('Exception checking table structure:', e);
    }
  };

  useEffect(() => {
    checkTableStructure(); // Check table structure on component mount
    fetchProducts();
  }, [fetchProducts]);

  const onTabPress = (key: TabKey) => {
    if (key === '价格') {
      setPriceAsc(!priceAsc);
      // Data will refetch due to useEffect dependency on priceAsc
    } else if (key === '筛选') {
      // Implement filter logic or open filter modal
      console.log('Filter tab pressed');
      // For now, just set active tab, no data refetch unless filter criteria change
      setActiveTab(key); 
      return; // Prevent immediate refetch if no filter criteria changed yet
    }
    setActiveTab(key);
    // Data will refetch due to useEffect dependency on activeTab
  };

  const renderTab = (key: TabKey) => (
    <TouchableOpacity
      key={key}
      style={styles.tabItem}
      onPress={() => onTabPress(key)}
    >
      <Text style={[styles.tabText, activeTab === key && styles.tabTextActive]}>{key}</Text>
      {key === '价格' && (
        <Icon
          name={priceAsc ? 'caret-up-outline' : 'caret-down-outline'}
          size={14}
          color={activeTab === '价格' ? '#007AFF' : '#666'}
          style={{ marginLeft: 2 }}
        />
      )}
      {key === '筛选' && (
        <Icon
          name="filter-outline"
          size={14}
          color={activeTab === '筛选' ? '#007AFF' : '#666'} // Color updates based on activeTab
          style={{ marginLeft: 2 }}
        />
      )}
    </TouchableOpacity>
  );

  const renderItem = ({ item }: { item: Product }) => {
    // Handle different field names for product name
    const displayName = item.name || item.title || 'Unnamed Product';
    
    // Use a placeholder URL instead of a local image
    const imageSource = { uri: item.image_url || item.image || 'https://via.placeholder.com/150' };
    
    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() => navigation.navigate('ProductDetails', { id: item.id })}
      >
        <Image
          source={imageSource}
          style={styles.cardImage}
          resizeMode="cover"
        />
        {item.badge && <View style={styles.badge}><Text style={styles.badgeText}>{item.badge}</Text></View>}
        <View style={styles.cardInfo}>
          <View style={styles.cardTitleRow}>
            <Text style={styles.cardTitle} numberOfLines={1}>{displayName}</Text>
          </View>
          <Text style={styles.cardDesc} numberOfLines={1}>{item.desc || item.description || ''}</Text>
          <View style={styles.priceRow}>
            <Text style={styles.price}>¥{item.price ? item.price.toFixed(2) : '0.00'}</Text>
            {item.original_price && (
              <Text style={styles.originalPrice}>¥{item.original_price.toFixed(2)}</Text>
            )}
          </View>
          <View style={styles.metaRow}>
            {/* Removed sold_count since it doesn't exist in your DB */}
            {typeof item.rating === 'number' && (
              <>
                <Icon name="star" size={12} color="#FFD700" style={{ marginHorizontal: 2 }} />
                <Text style={styles.metaText}>{item.rating.toFixed(1)}</Text>
              </>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading && products.length === 0) { // Show full screen loader only on initial load
    return (
      <SafeAreaView style={[styles.safe, styles.centered]}>
        <ActivityIndicator size="large" color="#007AFF" />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.safe, styles.centered]}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity onPress={fetchProducts} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safe}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backBtn}>
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{categoryParam || '商品列表'}</Text>
        <View style={{ width: 24 }} /> {/* For spacing, to keep title centered */}
      </View>

      <View style={styles.tabsRow}>{tabs.map(renderTab)}</View>

      {loading && products.length > 0 && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      )}
      
      <FlatList
        data={products}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.centeredEmptyContainer}>
              <Text style={styles.emptyText}>没有找到相关商品</Text>
            </View>
          ) : null
        }
        ListFooterComponent={products.length > 0 ? 
          <Text style={styles.footerText}>· {loading ? '加载中...' : '没有更多商品了'} ·</Text> : null
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  centered: { justifyContent: 'center', alignItems: 'center' },
  centeredEmptyContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 50 },
  emptyText: { fontSize: 16, color: '#666' },
  errorText: { fontSize: 16, color: 'red', textAlign: 'center', marginBottom: 10 },
  retryButton: { backgroundColor: '#007AFF', paddingVertical: 10, paddingHorizontal: 20, borderRadius: 5 },
  retryButtonText: { color: '#fff', fontSize: 16 },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#eee',
  },
  backBtn: { padding: 4 },
  headerTitle: { flex: 1, textAlign: 'center', fontSize: 16, fontWeight: 'bold', color: '#333' },
  tabsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#eee',
  },
  tabItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: 10 },
  tabText: { fontSize: 14, color: '#666' },
  tabTextActive: { color: '#007AFF', fontWeight: 'bold' },
  listContainer: { paddingHorizontal: 10, paddingBottom: 10 },
  card: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardImage: { width: 90, height: 90, borderRadius: 6, backgroundColor: '#f0f0f0' },
  cardInfo: { flex: 1, marginLeft: 10, justifyContent: 'space-between' },
  cardTitleRow: { flexDirection: 'row', alignItems: 'center' },
  badge: {
    backgroundColor: '#FF3B30',
    borderTopLeftRadius: 4,
    borderBottomRightRadius: 4,
    paddingHorizontal: 4,
    marginRight: 4,
  },
  badgeText: { color: '#fff', fontSize: 10 },
  cardTitle: { fontSize: 14, fontWeight: 'bold', flex: 1, color: '#333' },
  cardDesc: { fontSize: 12, color: '#666', marginVertical: 2 },
  priceRow: { flexDirection: 'row', alignItems: 'flex-end' },
  price: { fontSize: 16, color: '#E53935', fontWeight: 'bold' },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 6,
  },
  metaRow: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  metaText: { fontSize: 11, color: '#999' },
  footerText: { textAlign: 'center', paddingVertical: 15, color: '#999', fontSize: 12 },
  loadingOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 100, // Adjust as needed, below header/tabs
    alignItems: 'center',
    zIndex: 10, // Ensure it's above other content if needed
  },
});
