import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/Ionicons';
import { RootStackParamList } from '../../../navigation'; // Adjust path as needed
import { useThemeColor } from '../../../shared/hooks/useThemeColor'; // Assuming you have this hook

const { width } = Dimensions.get('window');

type CheckoutScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Checkout'>;

const mockAddress = {
  name: '张三',
  phone: '138****5678',
  address: '上海市浦东新区张江高科技园区博云路2号楼5层',
};

const mockDeliveryTime = '今日 16:00-18:00';

const mockOrderItems = [
  {
    storeName: 'Apple 官方旗舰店',
    items: [
      {
        id: '1',
        name: '真无线蓝牙耳机 Pro',
        variant: '白色 | 标准版',
        price: 1299,
        quantity: 1,
        image: 'https://i.imgur.com/JPlS2Vz.png', // Replace with actual image
      },
      {
        id: '2',
        name: '20W USB-C 电源适配器',
        variant: '白色',
        price: 149,
        quantity: 1,
        image: 'https://i.imgur.com/gTj9s2Y.png', // Replace with actual image
      },
    ],
    shippingFee: 8,
    totalStorePayment: 1456, // (1299*1 + 149*1) + 8 (includes shipping for this store if calculated separately)
  },
  {
    storeName: 'Samsung 官方旗舰店',
    items: [
      {
        id: '3',
        name: 'Galaxy Watch 5 Pro',
        variant: '黑色 | 45mm',
        price: 2399,
        quantity: 1,
        image: 'https://i.imgur.com/4l3gU22.png', // Replace with actual image
      },
    ],
    shippingFee: 12,
    discount: 200, // 满2000减200
    totalStorePayment: 2211, // (2399*1) + 12 - 200
  },
];

const paymentMethods = [
  { id: 'alipay', name: '支付宝', icon: 'card-outline' }, // Placeholder, use actual AliPay icon
  { id: 'wechatpay', name: '微信支付', icon: 'logo-wechat' },
  { id: 'card', name: '银行卡支付', icon: 'card-outline' },
];

const CheckoutScreen: React.FC = () => {
  const navigation = useNavigation<CheckoutScreenNavigationProp>();
  const colors = {
    primary: useThemeColor({}, 'primary'),
    text: useThemeColor({}, 'text'),
    textSecondary: useThemeColor({}, 'textSecondary'),
    background: useThemeColor({}, 'background'),
    card: useThemeColor({}, 'card'),
    border: useThemeColor({}, 'border'),
    accent: '#FF3B30', // Example accent color for pay button
  };

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('alipay');

  const totalProductAmount = mockOrderItems.reduce((sum, store) => 
    sum + store.items.reduce((itemSum, item) => itemSum + item.price * item.quantity, 0)
  , 0);
  const totalShippingFee = mockOrderItems.reduce((sum, store) => sum + store.shippingFee, 0);
  const totalDiscount = mockOrderItems.reduce((sum, store) => sum + (store.discount || 0), 0);
  const finalPaymentAmount = totalProductAmount + totalShippingFee - totalDiscount;

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.card }]}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerButton}>
        <Icon name="chevron-back-outline" size={24} color={colors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: colors.text }]}>确认订单</Text>
      <View style={styles.headerButton} />{/* For spacing */}
    </View>
  );

  const renderAddressSection = () => (
    <TouchableOpacity style={[styles.section, styles.addressSection, { backgroundColor: colors.card }]} onPress={() => console.log('Change Address')}> 
      <Icon name="location-outline" size={20} color={colors.primary} style={styles.sectionIcon} />
      <View style={styles.addressDetails}>
        <Text style={[styles.addressName, { color: colors.text }]}>{mockAddress.name} {mockAddress.phone}</Text>
        <Text style={[styles.addressText, { color: colors.textSecondary }]}>{mockAddress.address}</Text>
      </View>
      <Icon name="chevron-forward-outline" size={20} color={colors.textSecondary} />
    </TouchableOpacity>
  );

  const renderDeliveryTimeSection = () => (
    <TouchableOpacity style={[styles.section, { backgroundColor: colors.card }]} onPress={() => console.log('Change Delivery Time')}> 
      <Icon name="time-outline" size={20} color={colors.primary} style={styles.sectionIcon} />
      <Text style={[styles.sectionTitle, { color: colors.text, flex: 1 }]}>送达时间</Text>
      <Text style={[styles.deliveryTimeText, { color: colors.text }]}>{mockDeliveryTime}</Text>
      <Icon name="chevron-forward-outline" size={20} color={colors.textSecondary} />
    </TouchableOpacity>
  );

  const renderOrderItem = (item: any, storeIndex: number, itemIndex: number) => (
    <View key={`store-${storeIndex}-item-${itemIndex}`} style={styles.orderItemContainer}>
      <Image source={{ uri: item.image }} style={styles.itemImage} />
      <View style={styles.itemDetails}>
        <Text style={[styles.itemName, { color: colors.text }]}>{item.name}</Text>
        <Text style={[styles.itemVariant, { color: colors.textSecondary }]}>{item.variant}</Text>
        <Text style={[styles.itemPrice, { color: colors.text }]}>¥{item.price}</Text>
      </View>
      <Text style={[styles.itemQuantity, { color: colors.textSecondary }]}>x{item.quantity}</Text>
    </View>
  );

  const renderStoreSection = (store: any, storeIndex: number) => (
    <View key={`store-${storeIndex}`} style={[styles.storeSection, { backgroundColor: colors.card }]}>
      <View style={styles.storeHeader}>
        <Icon name="storefront-outline" size={18} color={colors.text} style={{marginRight: 8}}/>
        <Text style={[styles.storeName, { color: colors.text }]}>{store.storeName}</Text>
      </View>
      {store.items.map((item: any, itemIndex: number) => renderOrderItem(item, storeIndex, itemIndex))}
      <View style={styles.storeFooterRow}>
        <Text style={[styles.footerLabel, { color: colors.textSecondary }]}>配送费</Text>
        <Text style={[styles.footerValue, { color: colors.text }]}>¥{store.shippingFee}</Text>
      </View>
      {store.discount > 0 && (
        <View style={styles.storeFooterRow}>
          <Text style={[styles.discountLabel, { color: colors.accent }]}>满减</Text>
          <Text style={[styles.discountText, { color: colors.accent }]}>满{/* Determine from data */}2000减{store.discount}</Text>
          <Text style={[styles.discountAmount, { color: colors.accent }]}>-¥{store.discount}</Text>
        </View>
      )}
       <View style={[styles.storePaymentRow, {borderColor: colors.border}]}>
        <Text style={[styles.footerLabel, { color: colors.textSecondary }]}>实付款:</Text>
        <Text style={[styles.totalStorePaymentText, { color: colors.accent }]}>¥{store.totalStorePayment}</Text>
      </View>
    </View>
  );

  const renderPaymentMethodSection = () => (
    <View style={[styles.section, { backgroundColor: colors.card, marginTop: 8, paddingBottom: 0 }]}>
      <Text style={[styles.sectionTitle, { color: colors.text, marginBottom: 10 }]}>支付方式</Text>
      {paymentMethods.map(method => (
        <TouchableOpacity 
          key={method.id} 
          style={[styles.paymentMethodRow, {borderColor: colors.border}]} 
          onPress={() => setSelectedPaymentMethod(method.id)}
        >
          <Icon name={method.icon as any} size={22} color={method.id === 'alipay' ? '#00A0E9' : method.id === 'wechatpay' ? '#07C160' : colors.text} style={styles.paymentIcon} />
          <Text style={[styles.paymentMethodName, { color: colors.text }]}>{method.name}</Text>
          {selectedPaymentMethod === method.id ? (
            <Icon name="checkmark-circle" size={22} color={colors.primary} />
          ) : (
            <Icon name="ellipse-outline" size={22} color={colors.textSecondary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderInvoiceSection = () => (
    <TouchableOpacity style={[styles.section, { backgroundColor: colors.card, marginTop: 8 }]} onPress={() => console.log('Edit Invoice')}> 
      <Text style={[styles.sectionTitle, { color: colors.text, flex: 1 }]}>发票信息</Text>
      <Text style={[styles.invoiceDetails, { color: colors.text }]}>电子发票 | 个人</Text>
      <Icon name="chevron-forward-outline" size={20} color={colors.textSecondary} />
    </TouchableOpacity>
  );

 const renderSummarySection = () => (
    <View style={[styles.section, { backgroundColor: colors.card, marginTop: 8 }]}>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.text }]}>商品金额</Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>¥{totalProductAmount.toFixed(2)}</Text>
      </View>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.text }]}>优惠金额</Text>
        <Text style={[styles.summaryValue, { color: colors.accent }]}>-¥{totalDiscount.toFixed(2)}</Text>
      </View>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.text }]}>运费</Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>¥{totalShippingFee.toFixed(2)}</Text>
      </View>
    </View>
  );

  const renderBottomBar = () => (
    <View style={[styles.bottomBar, { backgroundColor: colors.card, borderTopColor: colors.border }]}>
      <View style={styles.totalAmountContainer}>
        <Text style={[styles.totalText, { color: colors.text }]}>合计：</Text>
        <Text style={[styles.totalAmount, { color: colors.accent }]}>¥{finalPaymentAmount.toFixed(2)}</Text>
      </View>
      <TouchableOpacity 
        style={[styles.payButton, { backgroundColor: colors.accent }]} 
        onPress={() => navigation.replace('OrderSuccess', { orderId: 'SIMULATED_ORDER_123' })}
      >
        <Text style={styles.payButtonText}>立即支付</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}
      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        {renderAddressSection()}
        {renderDeliveryTimeSection()}
        {mockOrderItems.map((store, index) => renderStoreSection(store, index))}
        {renderPaymentMethodSection()}
        {renderInvoiceSection()}
        {renderSummarySection()}
        <View style={{ height: 20 }} />{/* Spacer for bottom bar */}
      </ScrollView>
      {renderBottomBar()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0', // Use theme color later
  },
  headerButton: {
    padding: 5,
    width: 34, // Ensure consistent touch area
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollContentContainer: {
    paddingBottom: 70, // Space for the bottom bar
  },
  section: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  addressSection: {
    borderTopWidth: 8, // Special border for address section as per prototype
    borderTopColor: '#f5f5f5', // Use theme color later
    marginTop: 0,
  },
  sectionIcon: {
    marginRight: 10,
  },
  addressDetails: {
    flex: 1,
  },
  addressName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 13,
    lineHeight: 18,
  },
  deliveryTimeText: {
    fontSize: 14,
    marginRight: 5,
  },
  storeSection: {
    marginTop: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  storeName: {
    fontSize: 15,
    fontWeight: 'bold',
  },
  orderItemContainer: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  itemImage: {
    width: 70,
    height: 70,
    borderRadius: 5,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  itemName: {
    fontSize: 14,
    marginBottom: 2,
  },
  itemVariant: {
    fontSize: 12,
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  itemQuantity: {
    fontSize: 13,
    alignSelf: 'flex-start',
    marginLeft: 10,
  },
  storeFooterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#f9f9f9', // Use theme color later
  },
  footerLabel: {
    fontSize: 14,
  },
  footerValue: {
    fontSize: 14,
  },
  discountLabel: {
    fontSize: 13,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: '#FFEBEB', // Example color
    borderRadius: 4,
    marginRight: 5,
  },
  discountText: {
    fontSize: 13,
    flex: 1, // Allow text to take space
  },
  discountAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  storePaymentRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingVertical: 10,
    marginTop: 5,
    borderTopWidth: 1,
  },
  totalStorePaymentText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: 'bold',
  },
  paymentMethodRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  paymentMethodRowLast: {
    borderBottomWidth: 0,
  },
  paymentIcon: {
    marginRight: 12,
    width: 24, // for alignment
  },
  paymentMethodName: {
    flex: 1,
    fontSize: 15,
  },
  invoiceDetails: {
    fontSize: 14,
    marginRight: 5,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60, // Adjust as needed
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    borderTopWidth: 1,
  },
  totalAmountContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalText: {
    fontSize: 14,
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  payButton: {
    paddingHorizontal: 30,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  payButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CheckoutScreen;
