import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import FastImage from 'react-native-fast-image';
import Icon from 'react-native-vector-icons/FontAwesome';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { RootStackParamList } from '../../../navigation';
import { useThemeColor } from '../../../shared/hooks/useThemeColor';
import { getUserOrders } from '../../../lib/supabase'; // Assuming you have a getOrders function

type OrdersScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Haptic feedback options
const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

// Mock data for orders
const MOCK_ORDERS = [
  {
    id: 'order-123',
    order_date: new Date().toISOString(),
    total_amount: 159.97,
    status: 'Delivered',
    items: [
      {
        id: 'item-1',
        product_name: 'Premium T-Shirt',
        quantity: 1,
        price: 29.99,
        image_url: 'https://via.placeholder.com/100/FF5733/FFFFFF?text=T-Shirt',
      },
      {
        id: 'item-2',
        product_name: 'Designer Jeans',
        quantity: 1,
        price: 89.99,
        image_url: 'https://via.placeholder.com/100/3357FF/FFFFFF?text=Jeans',
      },
    ],
  },
  {
    id: 'order-456',
    order_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    total_amount: 119.99,
    status: 'Shipped',
    items: [
      {
        id: 'item-3',
        product_name: 'Running Shoes',
        quantity: 1,
        price: 119.99,
        image_url: 'https://via.placeholder.com/100/33FF57/FFFFFF?text=Shoes',
      },
    ],
  },
  {
    id: 'order-789',
    order_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    total_amount: 199.99,
    status: 'Processing',
    items: [
      {
        id: 'item-4',
        product_name: 'Casual Watch',
        quantity: 1,
        price: 199.99,
        image_url: 'https://via.placeholder.com/100/FF9F33/FFFFFF?text=Watch',
      },
    ],
  },
];

export default function OrdersScreen() {
  const navigation = useNavigation<OrdersScreenNavigationProp>();
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('All'); // Tabs: All, Processing, Shipped, Delivered, Cancelled

  // Theme colors
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const secondaryTextColor = useThemeColor({}, 'textSecondary');
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  const primaryColor = useThemeColor({}, 'primary');
  const accentColor = useThemeColor({}, 'accent');

  useEffect(() => {
    loadOrders();
  }, [selectedTab]);

  const loadOrders = async () => {
    setLoading(true);
    try {
      // For demo purposes, we'll use a mock user ID
      const userId = 'mock-user-id'; 
      const { orders: fetchedOrders, error } = await getUserOrders(userId, selectedTab);
      if (error) {
        throw error;
      }
      if (fetchedOrders && fetchedOrders.length > 0) {
        setOrders(fetchedOrders);
      } else {
        // Use mock data if no orders are returned or for specific tabs
        const filteredMockOrders = selectedTab === 'All' 
          ? MOCK_ORDERS 
          : MOCK_ORDERS.filter(order => order.status === selectedTab);
        setOrders(filteredMockOrders);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      // Fallback to mock data
      const filteredMockOrders = selectedTab === 'All' 
        ? MOCK_ORDERS 
        : MOCK_ORDERS.filter(order => order.status === selectedTab);
      setOrders(filteredMockOrders);
    } finally {
      setLoading(false);
    }
  };

  const handleOrderPress = (orderId: string) => {
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    // Navigate to OrderDetailsScreen (to be created)
    // navigation.navigate('OrderDetails', { orderId });
    Alert.alert('Order Details', `Navigate to details for order: ${orderId}`);
  };

  const renderOrderItem = ({ item: order }: { item: any }) => (
    <TouchableOpacity
      style={[styles.orderCard, { backgroundColor: cardColor, borderColor }]}
      onPress={() => handleOrderPress(order.id)}
    >
      <View style={styles.orderHeader}>
        <Text style={[styles.orderId, { color: textColor }]}>订单号: {order.id}</Text>
        <Text style={[styles.orderStatus, { color: getStatusColor(order.status) }]}>
          {order.status}
        </Text>
      </View>

      {order.items.map((productItem: any) => (
        <View key={productItem.id} style={styles.productItemContainer}>
          <FastImage
            source={{ uri: productItem.image_url || 'https://via.placeholder.com/80' }}
            style={styles.productImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <View style={styles.productInfo}>
            <Text style={[styles.productName, { color: textColor }]} numberOfLines={1}>
              {productItem.product_name}
            </Text>
            <Text style={[styles.productQuantity, { color: secondaryTextColor }]}>
              数量: {productItem.quantity}
            </Text>
          </View>
          <Text style={[styles.productPrice, { color: textColor }]}>
            ¥{productItem.price?.toFixed(2)}
          </Text>
        </View>
      ))}

      <View style={styles.orderFooter}>
        <Text style={[styles.orderDate, { color: secondaryTextColor }]}>
          {new Date(order.order_date).toLocaleDateString()}
        </Text>
        <Text style={[styles.orderTotal, { color: textColor }]}>
          总计: <Text style={{ color: primaryColor, fontWeight: 'bold' }}>¥{order.total_amount?.toFixed(2)}</Text>
        </Text>
      </View>

      <View style={styles.orderActions}>
        <TouchableOpacity style={[styles.actionButton, { borderColor }]}>
          <Text style={[styles.actionButtonText, { color: textColor }]}>查看详情</Text>
        </TouchableOpacity>
        {order.status === 'Processing' && (
          <TouchableOpacity style={[styles.actionButton, { borderColor: accentColor }]}>
            <Text style={[styles.actionButtonText, { color: accentColor }]}>取消订单</Text>
          </TouchableOpacity>
        )}
        {order.status === 'Delivered' && (
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: primaryColor, borderColor: primaryColor }]}>
            <Text style={[styles.actionButtonText, { color: '#FFFFFF' }]}>再次购买</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyOrders = () => (
    <View style={styles.emptyContainer}>
      <Icon name="file-text-o" size={60} color={secondaryTextColor} />
      <Text style={[styles.emptyText, { color: textColor }]}>暂无订单</Text>
      <Text style={[styles.emptySubtext, { color: secondaryTextColor }]}>
        您还没有任何订单记录。
      </Text>
      <TouchableOpacity
        style={[styles.browseButton, { backgroundColor: primaryColor }]}
        onPress={() => navigation.navigate('Main', { screen: 'Home' })}
      >
        <Text style={styles.browseButtonText}>去购物</Text>
      </TouchableOpacity>
    </View>
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return '#4CAF50'; // Green
      case 'Shipped':
        return '#2196F3'; // Blue
      case 'Processing':
        return '#FF9800'; // Orange
      case 'Cancelled':
        return '#F44336'; // Red
      default:
        return textColor;
    }
  };

  const TABS = ['All', 'Processing', 'Shipped', 'Delivered', 'Cancelled'];

  const renderTab = (tabName: string) => (
    <TouchableOpacity
      key={tabName}
      style={[
        styles.tabItem,
        selectedTab === tabName && { borderBottomColor: primaryColor, borderBottomWidth: 2 },
      ]}
      onPress={() => {
        ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
        setSelectedTab(tabName);
      }}
    >
      <Text
        style={[
          styles.tabText,
          { color: selectedTab === tabName ? primaryColor : secondaryTextColor },
        ]}
      >
        {tabName}
      </Text>
    </TouchableOpacity>
  );

  if (loading && orders.length === 0) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor }]}>
        <ActivityIndicator size="large" color={primaryColor} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <Text style={[styles.title, { color: textColor }]}>我的订单</Text>

      <View style={[styles.tabBar, { borderColor }]}>
        {TABS.map(renderTab)}
      </View>

      {loading && orders.length > 0 && (
        <ActivityIndicator style={styles.listLoadingIndicator} size="small" color={primaryColor} />
      )}

      {orders.length > 0 ? (
        <FlatList
          data={orders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          onRefresh={loadOrders}
          refreshing={loading}
        />
      ) : (
        !loading && renderEmptyOrders()
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listLoadingIndicator: {
    marginVertical: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    marginBottom: 10,
    marginHorizontal: 15,
  },
  tabItem: {
    paddingVertical: 10,
    paddingHorizontal: 5, 
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContent: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  orderCard: {
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 15,
    padding: 15,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  orderId: {
    fontSize: 14,
    fontWeight: '500',
  },
  orderStatus: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
    overflow: 'hidden', // For borderRadius to work on Text on Android
  },
  productItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE', // Light border for product items
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 4,
    marginRight: 10,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    marginBottom: 4,
  },
  productQuantity: {
    fontSize: 12,
  },
  productPrice: {
    fontSize: 14,
    fontWeight: '500',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  orderDate: {
    fontSize: 12,
  },
  orderTotal: {
    fontSize: 14,
  },
  orderActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 15,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginLeft: 10,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
    marginTop: 50, // Adjust as needed
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 30,
  },
  browseButton: {
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  browseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
