import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { useNavigation, RouteProp, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/Ionicons';
import { RootStackParamList } from '../../../navigation'; // Adjust path as needed
import { useThemeColor } from '../../../shared/hooks/useThemeColor'; // Assuming you have this hook

const { width } = Dimensions.get('window');

type OrderSuccessScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'OrderSuccess'>;
type OrderSuccessScreenRouteProp = RouteProp<RootStackParamList, 'OrderSuccess'>;

// Mock data - in a real app, this would likely be fetched based on orderId or passed from Checkout
const mockSuccessfulOrder = {
  orderNumber: '202506091423789',
  orderDate: '2025-06-09',
  orderTime: '14:23:45',
  paymentMethod: '支付宝',
  paymentAmount: 3567.00,
  shippingAddress: {
    recipientName: '张三',
    phone: '138****5678',
    address: '上海市浦东新区张江高科技园区博云路2号楼5层',
    deliveryMethod: '快递配送',
    estimatedDeliveryDate: '6月10日 16:00-18:00',
  },
  stores: [
    {
      storeName: 'Apple 官方旗舰店',
      items: [
        {
          id: '1',
          name: '真无线蓝牙耳机 Pro',
          variant: '白色 | 标准版',
          price: 1299,
          quantity: 1,
          image: 'https://i.imgur.com/JPlS2Vz.png',
        },
        {
          id: '2',
          name: '20W USB-C 电源适配器',
          variant: '白色',
          price: 149,
          quantity: 1,
          image: 'https://i.imgur.com/gTj9s2Y.png',
        },
      ],
      subtotal: 1448,
      discount: -100,
      shippingFee: 8,
      storeTotal: 1356,
    },
    {
      storeName: 'Samsung 官方旗舰店',
      items: [
        {
          id: '3',
          name: 'Galaxy Watch 5 Pro',
          variant: '黑色 | 45mm',
          price: 2399,
          quantity: 1,
          image: 'https://i.imgur.com/4l3gU22.png',
        },
      ],
      subtotal: 2399,
      discount: -200,
      shippingFee: 12,
      storeTotal: 2211,
    },
  ],
  orderTotal: 3567.00,
};

const mockRecommendations = [
  { id: 'rec1', name: '头戴式无线耳机', price: 899, image: 'https://i.imgur.com/nJ3OqCj.png' },
  { id: 'rec2', name: '智能手机', price: 4999, image: 'https://i.imgur.com/sC0zt3I.png' }, // Placeholder image
  { id: 'rec3', name: '20000mAh 充电宝', price: 199, image: 'https://i.imgur.com/tG3aK8A.png' },
];

const OrderSuccessScreen: React.FC = () => {
  const navigation = useNavigation<OrderSuccessScreenNavigationProp>();
  const route = useRoute<OrderSuccessScreenRouteProp>();
  const { orderId } = route.params; // Use the actual orderId if needed to fetch data

  const colors = {
    primary: useThemeColor({}, 'primary'),
    text: useThemeColor({}, 'text'),
    textSecondary: useThemeColor({}, 'textSecondary'),
    background: useThemeColor({}, 'background'),
    card: useThemeColor({}, 'card'),
    border: useThemeColor({}, 'border'),
    success: '#28A745', // Green for success messages/icons
    accent: useThemeColor({}, 'accent') || '#FF3B30', // Accent from theme or fallback
  };

  const orderData = mockSuccessfulOrder; // Use mock data for now

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
      <TouchableOpacity onPress={() => navigation.popToTop()} style={styles.headerButton}>
        {/* No back button typically on success, but can add if needed */}
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: colors.text }]}>支付成功</Text>
      <View style={styles.headerButton} />{/* For spacing */}
    </View>
  );

  const renderSuccessMessage = () => (
    <View style={styles.successMessageContainer}>
      <Icon name="checkmark-circle" size={60} color={colors.success} />
      <Text style={[styles.successTitle, { color: colors.text }]}>支付成功</Text>
      <Text style={[styles.successSubtitle, { color: colors.textSecondary }]}>感谢您的购买，您的订单已确认</Text>
    </View>
  );

  const renderOrderSummary = () => (
    <View style={[styles.section, { backgroundColor: colors.card }]}>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>订单编号</Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>{orderData.orderNumber}</Text>
      </View>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>下单时间</Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>{orderData.orderDate} {orderData.orderTime}</Text>
      </View>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>支付方式</Text>
        <Text style={[styles.summaryValue, { color: colors.text }]}>{orderData.paymentMethod}</Text>
      </View>
      <View style={styles.summaryRow}>
        <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>支付金额</Text>
        <Text style={[styles.summaryValue, { color: colors.accent, fontWeight: 'bold' }]}>¥{orderData.paymentAmount.toFixed(2)}</Text>
      </View>
    </View>
  );

  const renderShippingInfo = () => (
    <View style={[styles.section, { backgroundColor: colors.card }]}>
      <Text style={[styles.sectionTitle, { color: colors.text, marginBottom: 10 }]}>配送信息</Text>
      <View style={styles.infoRow}>
        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>收货人</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>{orderData.shippingAddress.recipientName} {orderData.shippingAddress.phone}</Text>
      </View>
      <View style={styles.infoRow}>
        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>收货地址</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>{orderData.shippingAddress.address}</Text>
      </View>
      <View style={styles.infoRow}>
        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>配送方式</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>{orderData.shippingAddress.deliveryMethod}</Text>
      </View>
      <View style={styles.infoRow}>
        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>预计送达</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>{orderData.shippingAddress.estimatedDeliveryDate}</Text>
      </View>
    </View>
  );

  const renderOrderItem = (item: any, itemIndex: number) => (
    <View key={`item-${itemIndex}`} style={styles.orderItemContainer}>
      <Image source={{ uri: item.image }} style={styles.itemImage} />
      <View style={styles.itemDetails}>
        <Text style={[styles.itemName, { color: colors.text }]}>{item.name}</Text>
        <Text style={[styles.itemVariant, { color: colors.textSecondary }]}>{item.variant}</Text>
      </View>
      <View style={styles.itemPriceQuantity}>
        <Text style={[styles.itemPrice, { color: colors.text }]}>¥{item.price.toFixed(2)}</Text>
        <Text style={[styles.itemQuantity, { color: colors.textSecondary }]}>x{item.quantity}</Text>
      </View>
    </View>
  );

  const renderStoreItems = (store: any, storeIndex: number) => (
    <View key={`store-${storeIndex}`} style={[styles.section, { backgroundColor: colors.card, marginTop: 0, paddingTop:15, borderTopWidth: storeIndex > 0 ? 8 : 0, borderTopColor: colors.background }]}>
      <View style={styles.storeHeader}>
        <Icon name="storefront-outline" size={18} color={colors.text} style={{marginRight: 8}}/>
        <Text style={[styles.storeName, { color: colors.text }]}>{store.storeName}</Text>
      </View>
      {store.items.map(renderOrderItem)}
      <View style={styles.storeSubtotalRow}>
        <Text style={[styles.subtotalLabel, { color: colors.textSecondary }]}>商品金额</Text>
        <Text style={[styles.subtotalValue, { color: colors.text }]}>¥{store.subtotal.toFixed(2)}</Text>
      </View>
      <View style={styles.storeSubtotalRow}>
        <Text style={[styles.subtotalLabel, { color: colors.textSecondary }]}>优惠</Text>
        <Text style={[styles.subtotalValue, { color: colors.accent }]}>-¥{(-store.discount).toFixed(2)}</Text>
      </View>
      <View style={styles.storeSubtotalRow}>
        <Text style={[styles.subtotalLabel, { color: colors.textSecondary }]}>运费</Text>
        <Text style={[styles.subtotalValue, { color: colors.text }]}>¥{store.shippingFee.toFixed(2)}</Text>
      </View>
      <View style={[styles.storeTotalRow, {borderTopColor: colors.border}]}>
        <Text style={[styles.storeTotalLabel, { color: colors.text }]}>小计</Text>
        <Text style={[styles.storeTotalValue, { color: colors.accent }]}>¥{store.storeTotal.toFixed(2)}</Text>
      </View>
    </View>
  );
  
  const renderOrderDetailsSection = () => (
    <View style={{marginTop: 8}}>
      <View style={[styles.sectionHeaderBar, {backgroundColor: colors.card}]}>
        <Text style={[styles.sectionTitle, { color: colors.text}]}>订单详情</Text>
      </View>
      {orderData.stores.map(renderStoreItems)}
    </View>
  );

  const renderOrderTotal = () => (
    <View style={[styles.section, styles.orderTotalSection, { backgroundColor: colors.card, borderTopColor: colors.border }]}>
      <Text style={[styles.orderTotalLabel, { color: colors.text }]}>订单总计</Text>
      <Text style={[styles.orderTotalValue, { color: colors.accent }]}>¥{orderData.orderTotal.toFixed(2)}</Text>
    </View>
  );

  const renderActionButtons = () => (
    <View style={[styles.actionButtonsContainer, {borderTopColor: colors.border, backgroundColor: colors.card}]}>
      <TouchableOpacity 
        style={[styles.actionButton, {borderColor: colors.primary}]} 
        onPress={() => navigation.navigate('Orders')}
      >
        <Text style={[styles.actionButtonText, { color: colors.primary }]}>查看订单</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.actionButton, styles.primaryButton, { backgroundColor: colors.primary }]} 
        onPress={() => navigation.popToTop()} // Or navigate to 'Home' or 'Main'
      >
        <Text style={[styles.actionButtonText, styles.primaryButtonText]}>返回首页</Text>
      </TouchableOpacity>
    </View>
  );

  const renderRecommendations = () => (
    <View style={styles.recommendSection}>
      <Text style={[styles.recommendTitle, { color: colors.text }]}>猜你喜欢</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.recommendGrid}>
        {mockRecommendations.map(item => (
          <TouchableOpacity key={item.id} style={[styles.recommendItem, { backgroundColor: colors.card }]} onPress={() => navigation.push('ProductDetails', { id: item.id })}>
            <Image source={{ uri: item.image }} style={styles.recommendImage} />
            <Text style={[styles.recommendName, { color: colors.text }]} numberOfLines={2}>{item.name}</Text>
            <Text style={[styles.recommendPrice, { color: colors.accent }]}>¥{item.price}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {renderHeader()}
      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        {renderSuccessMessage()}
        {renderOrderSummary()}
        {renderShippingInfo()}
        {renderOrderDetailsSection()}
        {renderOrderTotal()}
        {renderRecommendations()}
        <View style={{height: 80}} />{/* Spacer for bottom buttons */}
      </ScrollView>
      {renderActionButtons()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 5,
    width: 34, 
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollContentContainer: {
    paddingBottom: 20, // Space for recommendations or other content
  },
  successMessageContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  successTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 15,
  },
  successSubtitle: {
    fontSize: 14,
    marginTop: 8,
  },
  section: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginTop: 8,
  },
  sectionHeaderBar: {
    paddingHorizontal: 15,
    paddingVertical: 12, 
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  infoLabel: {
    fontSize: 14,
    width: 80, // Fixed width for labels
  },
  infoValue: {
    fontSize: 14,
    flex: 1,
    textAlign: 'right',
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  storeName: {
    fontSize: 15,
    fontWeight: 'bold',
  },
  orderItemContainer: {
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'center',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 5,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
  },
  itemVariant: {
    fontSize: 12,
    marginTop: 2,
  },
  itemPriceQuantity: {
    alignItems: 'flex-end',
  },
  itemPrice: {
    fontSize: 14,
  },
  itemQuantity: {
    fontSize: 12,
    marginTop: 2,
  },
  storeSubtotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 3,
  },
  subtotalLabel: {
    fontSize: 13,
  },
  subtotalValue: {
    fontSize: 13,
  },
  storeTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    marginTop: 8,
    borderTopWidth: 1,
  },
  storeTotalLabel: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  storeTotalValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  orderTotalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    marginTop: 0, // Connected to the last store section
    paddingVertical: 15,
  },
  orderTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  orderTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionButtonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderTopWidth: 1,
    paddingBottom: 20, // For safe area
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  primaryButton: {
    borderWidth: 0,
  },
  actionButtonText: {
    fontSize: 15,
    fontWeight: 'bold',
  },
  primaryButtonText: {
    color: '#fff',
  },
  recommendSection: {
    marginTop: 15,
    paddingBottom: 15, // Add some padding at the very bottom of scroll view
  },
  recommendTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    paddingHorizontal: 15,
  },
  recommendGrid: {
    paddingHorizontal: 10, // Give a bit of space on the sides of the scroll
  },
  recommendItem: {
    width: width * 0.35, // Adjust width as needed
    borderRadius: 8,
    padding: 10,
    marginHorizontal: 5,
    alignItems: 'center', // Center content
  },
  recommendImage: {
    width: '100%',
    height: width * 0.3, // Adjust height
    borderRadius: 8,
    marginBottom: 8,
  },
  recommendName: {
    fontSize: 12,
    textAlign: 'center',
    height: 30, // Ensure consistent height for 2 lines
  },
  recommendPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default OrderSuccessScreen;
