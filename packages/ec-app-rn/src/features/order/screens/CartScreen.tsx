import React, { useState, useMemo } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation'; // Adjust path as needed

// Type Definitions
interface CartItem {
  id: string;
  name: string;
  specs: string;
  price: number;
  quantity: number;
  image: string;
  selected: boolean;
}

interface Store {
  id: string;
  name: string;
  items: CartItem[];
  promotion?: string;
  selected: boolean;
}

interface RecommendedItem {
  id: string;
  name: string;
  price: number;
  image: string;
}

// Mock Data
const initialCartData: Store[] = [
  {
    id: 'store1',
    name: 'Apple 官方旗舰店',
    items: [
      {
        id: 'item1',
        name: '真无线蓝牙耳机 Pro',
        specs: '白色 | 标准版',
        price: 1299,
        quantity: 1,
        image: 'https://i.imgur.com/I6aG3hV.png',
        selected: true,
      },
      {
        id: 'item2',
        name: '20W USB-C 电源适配器',
        specs: '白色',
        price: 149,
        quantity: 1,
        image: 'https://i.imgur.com/R22FmoF.png',
        selected: true,
      },
    ],
    promotion: '已满1000元, 已减100元',
    selected: true,
  },
  {
    id: 'store2',
    name: 'Samsung 官方旗舰店',
    items: [
      {
        id: 'item3',
        name: 'Galaxy Watch 5 Pro',
        specs: '黑色 | 45mm',
        price: 2399,
        quantity: 1,
        image: 'https://i.imgur.com/5a2zGQS.png',
        selected: true,
      },
    ],
    selected: true,
  },
];

const recommendedData: RecommendedItem[] = [
  {
    id: 'rec1',
    name: '头戴式无线耳机',
    price: 899,
    image: 'https://i.imgur.com/nJ3OqCj.png',
  },
  { id: 'rec2', name: '智能手机', price: 4999, image: 'https://i.imgur.com/sC0zt3I.png' },
  {
    id: 'rec3',
    name: '20000mAh 充电宝',
    price: 199,
    image: 'https://i.imgur.com/tG3aK8A.png',
  },
];

const CheckBox = ({ checked, onPress }: { checked: boolean; onPress: () => void }) => (
  <TouchableOpacity onPress={onPress}>
    <Icon
      name={checked ? 'checkmark-circle' : 'ellipse-outline'}
      size={24}
      color={checked ? '#4A90E2' : '#ccc'}
    />
  </TouchableOpacity>
);

type CartScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

export default function CartScreen() {
  const navigation = useNavigation<CartScreenNavigationProp>();
  const [cartData, setCartData] = useState(initialCartData);

  const handleQuantityChange = (storeId: string, itemId: string, delta: number) => {
    setCartData(prevData =>
      prevData.map(store => {
        if (store.id === storeId) {
          return {
            ...store,
            items: store.items.map(item => {
              if (item.id === itemId) {
                const newQuantity = item.quantity + delta;
                return { ...item, quantity: newQuantity > 0 ? newQuantity : 1 };
              }
              return item;
            }),
          };
        }
        return store;
      })
    );
  };

  const toggleItemSelection = (storeId: string, itemId: string) => {
    setCartData(prevData =>
      prevData.map(store => {
        if (store.id === storeId) {
          const newItems = store.items.map(item =>
            item.id === itemId ? { ...item, selected: !item.selected } : item
          );
          const allItemsSelected = newItems.every(item => item.selected);
          return { ...store, items: newItems, selected: allItemsSelected };
        }
        return store;
      })
    );
  };

  const toggleStoreSelection = (storeId: string) => {
    setCartData(prevData =>
      prevData.map(store => {
        if (store.id === storeId) {
          const newSelectedState = !store.selected;
          return {
            ...store,
            selected: newSelectedState,
            items: store.items.map(item => ({ ...item, selected: newSelectedState })),
          };
        }
        return store;
      })
    );
  };

  const toggleSelectAll = (isAllSelected: boolean) => {
    setCartData(prevData =>
      prevData.map(store => ({
        ...store,
        selected: !isAllSelected,
        items: store.items.map(item => ({ ...item, selected: !isAllSelected })),
      }))
    );
  };

  const { totalPrice, totalDiscount, selectedCount, isAllSelected } = useMemo(() => {
    let price = 0;
    let discount = 0;
    let count = 0;
    let allItemsCount = 0;
    let allSelectedCount = 0;

    cartData.forEach(store => {
      allItemsCount += store.items.length;
      if (store.promotion) {
        const storeTotal = store.items.reduce((sum, item) => {
          if (item.selected) return sum + item.price * item.quantity;
          return sum;
        }, 0);
        if (storeTotal >= 1000) {
          discount += 100;
        }
      }
      store.items.forEach(item => {
        if (item.selected) {
          price += item.price * item.quantity;
          count += item.quantity;
          allSelectedCount++;
        }
      });
    });

    return {
      totalPrice: price,
      totalDiscount: discount,
      selectedCount: count,
      isAllSelected: allItemsCount > 0 && allItemsCount === allSelectedCount,
    };
  }, [cartData]);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>购物车</Text>
        <TouchableOpacity>
          <Text style={styles.headerAction}>管理</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {cartData.map(store => (
          <View key={store.id} style={styles.storeContainer}>
            <View style={styles.storeHeader}>
              <CheckBox checked={store.selected} onPress={() => toggleStoreSelection(store.id)} />
              <Icon name="storefront-outline" size={20} style={styles.storeIcon} />
              <Text style={styles.storeName}>{store.name}</Text>
              <Icon name="chevron-forward-outline" size={16} color="#ccc" />
            </View>
            {store.items.map(item => (
              <View key={item.id} style={styles.itemContainer}>
                <CheckBox
                  checked={item.selected}
                  onPress={() => toggleItemSelection(store.id, item.id)}
                />
                <Image source={{ uri: item.image }} style={styles.itemImage} />
                <View style={styles.itemDetails}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={styles.itemSpecs}>{item.specs}</Text>
                  <Text style={styles.itemPrice}>¥{item.price}</Text>
                </View>
                <View style={styles.quantityControl}>
                  <TouchableOpacity onPress={() => handleQuantityChange(store.id, item.id, -1)}>
                    <Icon name="remove-circle-outline" size={24} color="#ccc" />
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{item.quantity}</Text>
                  <TouchableOpacity onPress={() => handleQuantityChange(store.id, item.id, 1)}>
                    <Icon name="add-circle" size={24} color="#4A90E2" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
            {store.promotion && (
              <View style={styles.promotionContainer}>
                <Text style={styles.promotionTag}>满减</Text>
                <Text style={styles.promotionText}>{store.promotion}</Text>
              </View>
            )}
          </View>
        ))}

        <View style={styles.recommendSection}>
          <Text style={styles.recommendTitle}>猜你喜欢</Text>
          <View style={styles.recommendGrid}>
            {recommendedData.map(item => (
              <View key={item.id} style={styles.recommendItem}>
                <Image source={{ uri: item.image }} style={styles.recommendImage} />
                <Text style={styles.recommendName} numberOfLines={2}>
                  {item.name}
                </Text>
                <Text style={styles.recommendPrice}>¥{item.price}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.footerLeft}>
          <CheckBox checked={isAllSelected} onPress={() => toggleSelectAll(isAllSelected)} />
          <Text style={styles.selectAllText}>全选</Text>
        </View>
        <View style={styles.footerCenter}>
          <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
            <Text style={styles.totalText}>合计:</Text>
            <Text style={styles.totalPrice}>¥{totalPrice - totalDiscount}</Text>
          </View>
          <Text style={styles.discountText}>已优惠: ¥{totalDiscount}</Text>
        </View>
        <TouchableOpacity style={styles.checkoutButton} onPress={() => navigation.navigate('Checkout')}>
          <Text style={styles.checkoutButtonText}>结算({selectedCount})</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerAction: {
    fontSize: 16,
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  storeContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 15,
    marginBottom: 0,
    padding: 15,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  storeIcon: {
    marginHorizontal: 8,
  },
  storeName: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginHorizontal: 10,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemSpecs: {
    fontSize: 12,
    color: '#888',
    marginVertical: 4,
  },
  itemPrice: {
    fontSize: 16,
    color: '#e94e77',
    fontWeight: 'bold',
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityText: {
    marginHorizontal: 10,
    fontSize: 16,
    fontWeight: '500',
  },
  promotionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingLeft: 34, // Align with items
  },
  promotionTag: {
    backgroundColor: '#e94e77',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  promotionText: {
    fontSize: 12,
    color: '#e94e77',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    marginLeft: 8,
    fontSize: 16,
  },
  footerCenter: {
    alignItems: 'flex-end',
  },
  totalText: {
    fontSize: 16,
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e94e77',
    marginLeft: 4,
  },
  discountText: {
    fontSize: 12,
    color: '#888',
  },
  checkoutButton: {
    backgroundColor: '#e94e77',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 25,
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  recommendSection: {
    marginTop: 15,
    paddingHorizontal: 15,
  },
  recommendTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  recommendGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  recommendItem: {
    width: '32%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    marginBottom: 10,
  },
  recommendImage: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    marginBottom: 8,
  },
  recommendName: {
    fontSize: 12,
  },
  recommendPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#e94e77',
    marginTop: 4,
  },
});
