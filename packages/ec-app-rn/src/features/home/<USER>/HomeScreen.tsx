import React from 'react';
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Dimensions,
  TextInput,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/Ionicons';
import { RootStackParamList } from '../navigation';
import { SafeAreaView } from 'react-native-safe-area-context';

// Get screen dimensions
const { width } = Dimensions.get('window');

// Mock Data based on the prototype
const carouselData = [
  { id: '1', image: 'https://i.imgur.com/gTj9s2Y.png' },
  { id: '2', image: 'https://i.imgur.com/gTj9s2Y.png' },
  { id: '3', image: 'https://i.imgur.com/gTj9s2Y.png' },
];

const categoryData = [
  { id: '1', name: '准时抢购', icon: 'flash-outline', color: '#4A90E2' },
  { id: '2', name: '新品首发', icon: 'gift-outline', color: '#4A90E2' },
  { id: '3', name: '折扣专区', icon: 'pricetag-outline', color: '#4A90E2' },
  { id: '4', name: '热门榜单', icon: 'flame-outline', color: '#4A90E2' },
  { id: '5', name: '全部分类', icon: 'grid-outline', color: '#4A90E2' },
];

const instantDeliveryData = [
  { id: '1', name: '健康沙拉', price: '28.8', image: 'https://i.imgur.com/5a2zGQS.png' },
  { id: '2', name: '拿铁咖啡', price: '19.9', image: 'https://i.imgur.com/O4MwaeG.png' },
  { id: '3', name: '意式披萨', price: '59.9', image: 'https://i.imgur.com/sC0zt3I.png' },
];

const guessYouLikeData = [
  {
    id: '1',
    name: '无线蓝牙耳机',
    description: '主动降噪 | 40小时续航',
    price: '899',
    originalPrice: '1299',
    image: 'https://i.imgur.com/sC3a4Gg.png',
    tag: '推荐',
  },
  {
    id: '2',
    name: '轻便运动跑鞋',
    description: '透气网面 | 减震鞋底',
    price: '459',
    originalPrice: '699',
    image: 'https://i.imgur.com/LPlBf6g.png',
    tag: '热门',
  },
];

const featuredProductsData = [
  {
    id: '1',
    name: '智能手表 Pro',
    price: '1299',
    originalPrice: '1599',
    image: 'https://i.imgur.com/pBvDjYJ.png',
  },
  {
    id: '2',
    name: '10000mAh 快充...',
    price: '129',
    originalPrice: '199',
    image: 'https://i.imgur.com/tXzJ9Fw.png',
  },
  {
    id: '3',
    name: '专业跑步鞋',
    price: '499',
    originalPrice: '799',
    image: 'https://i.imgur.com/vhy9Jt5.png',
  },
  {
    id: '4',
    name: '无线蓝牙音箱',
    price: '299',
    originalPrice: '399',
    image: 'https://i.imgur.com/jQyqJ7E.png',
  },
];

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Reusable Section Header Component
const SectionHeader = ({ title, onMorePress }: { title: string; onMorePress?: () => void }) => (
  <View style={styles.sectionHeader}>
    <Text style={styles.sectionTitle}>{title}</Text>
    {onMorePress && (
      <TouchableOpacity onPress={onMorePress} style={styles.moreButton}>
        <Text style={styles.moreText}>更多</Text>
        <Icon name="chevron-forward-outline" size={16} color="#999" />
      </TouchableOpacity>
    )}
  </View>
);

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const renderProductGridItem = ({ item }: { item: any }) => (
    <TouchableOpacity style={styles.productGridItem}>
      <Image source={{ uri: item.image }} style={styles.productGridImage} />
      <Text style={styles.productGridName} numberOfLines={2}>{item.name}</Text>
      <View style={styles.priceContainer}>
        <Text style={styles.productGridPrice}>¥{item.price}</Text>
        <Text style={styles.productGridOriginalPrice}>¥{item.originalPrice}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <View style={styles.container}>
        {/* Custom Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.locationButton}>
            <Icon name="location-outline" size={20} color="#333" />
            <Text style={styles.locationText}>上海市</Text>
            <Icon name="chevron-down-outline" size={16} color="#333" />
          </TouchableOpacity>
          <View style={styles.headerIcons}>
            <TouchableOpacity>
              <Icon name="notifications-outline" size={24} color="#333" />
            </TouchableOpacity>
            <TouchableOpacity style={{ marginLeft: 15 }}>
              <Icon name="chatbubble-ellipses-outline" size={24} color="#333" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Icon name="search-outline" size={20} color="#999" />
            <TextInput placeholder="搜索商品" style={styles.searchInput} />
            <TouchableOpacity>
              <Icon name="camera-outline" size={22} color="#999" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Image Carousel */}
          <View style={styles.carouselContainer}>
            <FlatList
              data={carouselData}
              renderItem={({ item }) => (
                <Image source={{ uri: item.image }} style={styles.carouselImage} />
              )}
              keyExtractor={(item) => item.id}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
            />
          </View>

          {/* Category Icons */}
          <View style={styles.categoryContainer}>
            {categoryData.map((item) => (
              <TouchableOpacity key={item.id} style={styles.categoryItem}>
                <View style={[styles.categoryIconCircle, { backgroundColor: '#E9F5FF' }]}>
                  <Icon name={item.icon} size={24} color={item.color} />
                </View>
                <Text style={styles.categoryText}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Instant E-commerce */}
          <View style={styles.instantContainer}>
            <View style={styles.instantHeader}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Icon name="flash" size={22} color="#007AFF" />
                <Text style={styles.instantTitle}>即时电商</Text>
              </View>
              <TouchableOpacity style={styles.moreButton} onPress={() => navigation.navigate('Instant')}>
                <Text style={styles.moreText}>更多</Text>
                <Icon name="chevron-forward-outline" size={16} color="#999" />
              </TouchableOpacity>
            </View>
            <TouchableOpacity style={styles.instantCard} onPress={() => navigation.navigate('Instant')}>
              <Text style={styles.instantCardTitle}>30分钟送达</Text>
              <Text style={styles.instantCardSubtitle}>附近商品立即配送</Text>
            </TouchableOpacity>
            <FlatList
              data={instantDeliveryData}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.instantItem}>
                  <Image source={{ uri: item.image }} style={styles.instantItemImage} />
                  <Text style={styles.instantItemName}>{item.name}</Text>
                  <Text style={styles.instantItemPrice}>¥{item.price}</Text>
                </TouchableOpacity>
              )}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingLeft: 15, paddingTop: 10 }}
            />
          </View>

          {/* Guess You Like */}
          <View style={styles.section}>
            <SectionHeader title="❤️ 猜你喜欢" onMorePress={() => navigation.navigate('Products', { category: '猜你喜欢' })} />
            {guessYouLikeData.map((item) => (
              <TouchableOpacity key={item.id} style={styles.guessItem}>
                <Image source={{ uri: item.image }} style={styles.guessItemImage} />
                <View style={styles.guessItemDetails}>
                  <Text style={styles.guessItemName}>{item.name}</Text>
                  <Text style={styles.guessItemDesc}>{item.description}</Text>
                  <View style={styles.priceContainer}>
                    <Text style={styles.guessItemPrice}>¥{item.price}</Text>
                    <Text style={styles.guessItemOriginalPrice}>¥{item.originalPrice}</Text>
                  </View>
                </View>
                <View style={[styles.tag, { backgroundColor: item.tag === '热门' ? '#FF6A6A' : '#FF8C00' }]}>
                  <Text style={styles.tagText}>{item.tag}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Featured Products */}
          <View style={styles.section}>
            <SectionHeader title="🛒 精选商品" onMorePress={() => {}} />
            <FlatList
              data={featuredProductsData}
              renderItem={renderProductGridItem}
              keyExtractor={(item) => item.id}
              numColumns={2}
              scrollEnabled={false}
              columnWrapperStyle={{ justifyContent: 'space-between' }}
            />
          </View>

          <View style={{ height: 30 }} />
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#F4F4F4',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: '#fff',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 5,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  searchContainer: {
    paddingHorizontal: 15,
    paddingBottom: 10,
    backgroundColor: '#fff',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F4F4F4',
    borderRadius: 20,
    paddingHorizontal: 15,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
  },
  carouselContainer: {
    height: width * 0.4,
  },
  carouselImage: {
    width: width - 30,
    height: width * 0.4,
    borderRadius: 10,
    marginHorizontal: 15,
  },
  categoryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
    backgroundColor: '#fff',
    marginHorizontal: 15,
    borderRadius: 10,
    marginTop: -30, // Overlap with carousel
    zIndex: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  categoryItem: {
    alignItems: 'center',
  },
  categoryIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryText: {
    marginTop: 8,
    fontSize: 12,
    color: '#333',
  },
  instantContainer: {
    backgroundColor: '#fff',
    margin: 15,
    borderRadius: 10,
    paddingVertical: 15,
  },
  instantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  instantTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  moreButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moreText: {
    fontSize: 14,
    color: '#999',
  },
  instantCard: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    margin: 15,
    padding: 15,
  },
  instantCardTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  instantCardSubtitle: {
    color: '#fff',
    fontSize: 14,
    marginTop: 4,
  },
  instantItem: {
    width: 110,
    marginRight: 10,
  },
  instantItemImage: {
    width: 110,
    height: 110,
    borderRadius: 8,
  },
  instantItemName: {
    fontSize: 14,
    marginTop: 5,
  },
  instantItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#E53935',
    marginTop: 3,
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 15,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  guessItem: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  guessItemImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
  },
  guessItemDetails: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'center',
  },
  guessItemName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  guessItemDesc: {
    fontSize: 13,
    color: '#666',
    marginVertical: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  guessItemPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#E53935',
  },
  guessItemOriginalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  tag: {
    position: 'absolute',
    top: 0,
    left: 0,
    borderTopLeftRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  productGridItem: {
    width: (width - 45) / 2,
    marginBottom: 15,
  },
  productGridImage: {
    width: '100%',
    height: (width - 45) / 2,
    borderRadius: 8,
    backgroundColor: '#eee',
  },
  productGridName: {
    fontSize: 14,
    marginTop: 8,
    height: 40, // for 2 lines
  },
  productGridPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E53935',
  },
  productGridOriginalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 5,
  },
});
