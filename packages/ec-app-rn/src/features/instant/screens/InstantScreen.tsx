import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  TextInput,
  FlatList,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';

const DELIVERY_BANNER_IMAGE = 'https://i.imgur.com/O0sK3mb.png'; // Placeholder
const PANCAKES_IMAGE = 'https://i.imgur.com/AdA2G9g.png'; // Placeholder for pancakes
const FOOD_TABLE_IMAGE = 'https://i.imgur.com/NlW9W7G.png'; // Placeholder for food table
const CUPCAKES_IMAGE = 'https://i.imgur.com/RzF5bYJ.png'; // Placeholder for cupcakes
const SALAD_BOWL_IMAGE = 'https://i.imgur.com/LPlBf6g.png'; // Re-use salad image

const categories = [
  { id: '1', name: '美食', icon: 'fast-food-outline' as const },
  { id: '2', name: '生鲜', icon: 'basket-outline' as const },
  { id: '3', name: '咖啡', icon: 'cafe-outline' as const },
  { id: '4', name: '药品', icon: 'medkit-outline' as const },
  { id: '5', name: '甜点', icon: 'ice-cream-outline' as const },
];

const merchants = [
  {
    id: '1',
    name: '有机生鲜超市',
    image: PANCAKES_IMAGE,
    rating: 4.8,
    reviews: 328,
    deliveryTime: '15-25分钟',
    deliveryFee: '¥5',
    distance: '0.8km',
  },
  {
    id: '2',
    name: '全时便利店',
    image: FOOD_TABLE_IMAGE,
    rating: 4.2,
    reviews: 156,
    deliveryTime: '10分钟',
    deliveryFee: '¥3',
    distance: '0.3km',
  },
];

const popularProducts = [
  {
    id: '1',
    name: '拿铁咖啡',
    subtitle: '星巴克咖啡',
    price: '32',
    image: CUPCAKES_IMAGE,
  },
  {
    id: '2',
    name: '健康沙拉',
    subtitle: '有机生鲜超市',
    price: '42',
    image: SALAD_BOWL_IMAGE,
  },
  {
    id: '3',
    name: '新鲜三文鱼',
    subtitle: '每日空运',
    price: '88',
    image: 'https://i.imgur.com/sC3a4Gg.png',
  },
];

const StarRating = ({ rating, size = 14 }: { rating: number; size?: number }) => {
  const totalStars = 5;
  return (
    <View style={{ flexDirection: 'row' }}>
      {[...Array(totalStars)].map((_, index) => {
        const starName = index < rating ? 'star' : 'star-outline';
        return <Icon key={index} name={starName} size={size} color="#FFCC00" />;
      })}
    </View>
  );
};

export default function InstantScreen() {
  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {/* Header: Location & Delivery Time */}
        <View style={styles.headerContainer}>
          <TouchableOpacity style={styles.locationContainer}>
            <Icon name="location-sharp" size={20} color="#007AFF" />
            <Text style={styles.locationText} numberOfLines={1}>
              上海市浦东新区张江高科技园区
            </Text>
            <Icon name="chevron-down-outline" size={16} color="#666" />
          </TouchableOpacity>
          <View style={styles.deliveryTimeContainer}>
            <Icon name="time-outline" size={20} color="#FF3B30" />
            <Text style={styles.deliveryTimeText}>30分钟内送达</Text>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Icon name="search-outline" size={20} color="#8E8E93" style={styles.searchIcon} />
          <TextInput placeholder="搜索附近商品" style={styles.searchInput} />
        </View>

        {/* Banner */}
        <View style={styles.bannerContainer}>
          <Image source={{ uri: DELIVERY_BANNER_IMAGE }} style={styles.bannerImage} />
          <View style={styles.bannerOverlay}>
            <Text style={styles.bannerTitle}>闪电配送</Text>
            <Text style={styles.bannerSubtitle}>下单后30分钟内送达</Text>
            <TouchableOpacity style={styles.bannerButton}>
              <Icon name="flash-sharp" size={16} color="#fff" />
              <Text style={styles.bannerButtonText}>立即配送</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          {categories.map((category) => (
            <TouchableOpacity key={category.id} style={styles.categoryItem}>
              <View style={styles.categoryIconContainer}>
                <Icon name={category.icon} size={28} color="#007AFF" />
              </View>
              <Text style={styles.categoryText}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Nearby Merchants */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>附近商家</Text>
          {merchants.map((merchant) => (
            <TouchableOpacity key={merchant.id} style={styles.merchantCard}>
              <Image source={{ uri: merchant.image }} style={styles.merchantImage} />
              <View style={styles.merchantDistanceContainer}>
                <Text style={styles.merchantDistanceText}>{merchant.distance}</Text>
              </View>
              <View style={styles.merchantInfo}>
                <Text style={styles.merchantName}>{merchant.name}</Text>
                <View style={styles.ratingContainer}>
                  <StarRating rating={merchant.rating} />
                  <Text style={styles.ratingText}>
                    {merchant.rating.toFixed(1)} ({merchant.reviews})
                  </Text>
                </View>
                <View style={styles.deliveryDetailsContainer}>
                  <Icon name="time-outline" size={14} color="#666" />
                  <Text style={styles.merchantMetaText}>{merchant.deliveryTime}</Text>
                  <View style={styles.metaSeparator} />
                  <Icon name="bicycle-outline" size={14} color="#666" />
                  <Text style={styles.merchantMetaText}>配送费{merchant.deliveryFee}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Popular Products */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>热门商品</Text>
          <FlatList
            data={popularProducts}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity style={styles.productCard}>
                <Image source={{ uri: item.image }} style={styles.productImage} />
                <Text style={styles.productName} numberOfLines={1}>{item.name}</Text>
                <Text style={styles.productSubtitle} numberOfLines={1}>{item.subtitle}</Text>
                <View style={styles.priceAddContainer}>
                  <Text style={styles.productPrice}>¥{item.price}</Text>
                  <TouchableOpacity style={styles.addButton}>
                    <Icon name="add-sharp" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            )}
            contentContainerStyle={{ paddingLeft: 15, paddingRight: 5 }} // Added paddingRight for last item
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  headerContainer: {
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: '#fff',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    marginRight: 4,
    flex: 1, // Allow text to take available space and truncate
  },
  deliveryTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTimeText: {
    fontSize: 13,
    color: '#FF3B30',
    marginLeft: 6,
    fontWeight: '500',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 15,
    marginTop: 10,
    marginBottom: 15,
    paddingHorizontal: 10,
    paddingVertical: Platform.OS === 'ios' ? 10 : 8, // Adjusted padding
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    color: '#333',
    paddingVertical: 0, // Remove default padding for Android TextInput
  },
  bannerContainer: {
    height: 150,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#e0e0e0', // Placeholder background for image loading
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  bannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    padding: 15,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  bannerSubtitle: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 12,
  },
  bannerButton: {
    flexDirection: 'row',
    backgroundColor: '#FFCC00', // Changed to yellow as per prototype
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  bannerButtonText: {
    color: '#333', // Darker text for yellow button
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  categoriesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    marginTop: 15,
  },
  categoryItem: {
    alignItems: 'center',
    width: 60, 
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#EFEFF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  categoryText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  sectionContainer: {
    marginTop: 15,
    backgroundColor: '#fff',
    paddingTop: 15, // Ensure title has padding
    paddingBottom: 0, // Remove bottom padding if merchants add their own
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  merchantCard: {
    flexDirection: 'row',
    alignItems: 'flex-start', // Align items to the top for better layout
    paddingHorizontal: 15,
    paddingBottom: 15,
    marginBottom: 15,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  merchantImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: '#f0f0f0', // Placeholder background
  },
  merchantDistanceContainer: {
    position: 'absolute',
    top: 5,
    left: 20, 
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 4,
  },
  merchantDistanceText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  merchantInfo: {
    flex: 1,
  },
  merchantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  ratingText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 6,
  },
  deliveryDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap', // Allow wrapping if content is too long
  },
  merchantMetaText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  metaSeparator: {
    width: 1,
    height: 10,
    backgroundColor: '#DCDCDC',
    marginHorizontal: 8,
  },
  productCard: {
    width: 150,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    marginBottom: 5, // Add some bottom margin for shadow visibility
  },
  productImage: {
    width: '100%',
    height: 100,
    borderRadius: 6,
    marginBottom: 8,
    backgroundColor: '#f0f0f0', // Placeholder background
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  productSubtitle: {
    fontSize: 11,
    color: '#777',
    marginBottom: 6,
  },
  priceAddContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto', 
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF3B30',
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
