import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../navigation';
import Icon from 'react-native-vector-icons/Ionicons';

const SETTINGS_SECTIONS = [
  {
    title: '账号与安全',
    items: [
      { label: '个人资料', icon: 'person-circle', color: '#4A90E2' },
      { label: '账号安全', icon: 'lock-closed', color: '#F5A623' },
      { label: '隐私设置', icon: 'shield-checkmark', color: '#50E3C2' },
    ],
  },
  {
    title: '支付与配送',
    items: [
      { label: '支付管理', icon: 'card', color: '#E94E77' },
      { label: '收货地址', icon: 'location', color: '#7ED321' },
    ],
  },
  {
    title: '应用设置',
    items: [
      { label: '通知设置', icon: 'notifications', color: '#4A90E2' },
      { label: '语言', icon: 'globe-outline', color: '#9013FE', value: '简体中文' },
      { label: '深色模式', icon: 'moon', color: '#4A4A4A', type: 'switch' },
      { label: '清除缓存', icon: 'trash', color: '#BD10E0', value: '23.5MB' },
      { label: '组件列表', icon: 'grid', color: '#4A90E2', navigate: 'ComponentList' },
    ],
  },
  {
    title: '关于',
    items: [
      { label: '关于我们', icon: 'information-circle', color: '#4A90E2' },
      { label: '给我们评分', icon: 'star', color: '#F5A623' },
      { label: '联系客服', icon: 'headset', color: '#50E3C2' },
      { label: '用户协议与隐私政策', icon: 'document-text', color: '#9B9B9B' },
      { label: '版本信息', icon: 'code-slash', color: '#4A4A4A', value: 'v2.5.3' },
    ],
  },
];

type SettingsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

export default function SettingsScreen() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const navigation = useNavigation<SettingsScreenNavigationProp>();

  const renderSettingItem = (item: any) => {
    const handlePress = () => {
      if (item.navigate) {
        navigation.navigate(item.navigate);
      }
    };
    
    return (
      <TouchableOpacity 
        key={item.label} 
        style={styles.itemContainer}
        onPress={handlePress}
      >
        <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
          <Icon name={item.icon} size={20} color="#fff" />
        </View>
        <Text style={styles.itemLabel}>{item.label}</Text>
        <View style={styles.itemValueContainer}>
          {item.value && <Text style={styles.itemValue}>{item.value}</Text>}
          {item.type === 'switch' ? (
            <Switch
              value={isDarkMode}
              onValueChange={setIsDarkMode}
              trackColor={{ false: '#E9E9EA', true: '#34C759' }}
              thumbColor={'#FFFFFF'}
            />
          ) : (
            <Icon name="chevron-forward-outline" size={20} color="#C7C7CC" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>设置</Text>
        <View style={styles.placeholder} />
      </View>
      <ScrollView style={styles.container}>
        {SETTINGS_SECTIONS.map(section => (
          <View key={section.title} style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionItems}>
              {section.items.map(renderSettingItem)}
            </View>
          </View>
        ))}
        <TouchableOpacity style={styles.logoutButton}>
          <Text style={styles.logoutButtonText}>退出登录</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  sectionContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#6D6D72',
    marginLeft: 15,
    marginBottom: 8,
  },
  sectionItems: {
    backgroundColor: '#fff',
    borderTopWidth: 0.5,
    borderBottomWidth: 0.5,
    borderColor: '#C8C7CC',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 0.5,
    borderColor: '#C8C7CC',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  itemLabel: {
    flex: 1,
    fontSize: 17,
  },
  itemValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemValue: {
    fontSize: 17,
    color: '#8E8E93',
    marginRight: 8,
  },
  logoutButton: {
    margin: 20,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: '#C8C7CC',
  },
  logoutButtonText: {
    color: '#FF3B30',
    fontSize: 17,
    fontWeight: 'bold',
  },
});
