import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import QuantitySelector from '../../../shared/components/QuantitySelector';

export default function QuantitySelectorDemoScreen() {
  const navigation = useNavigation();
  const [quantities, setQuantities] = useState({
    default: 1,
    primary: 2,
    outline: 3,
    small: 1,
    medium: 2,
    large: 3,
    disabled: 1,
    noInput: 1,
    minMax: 5,
    step: 1,
  });

  const handleQuantityChange = (key: string, value: number) => {
    setQuantities(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>加减购按钮组件</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础用法</Text>
          <Text style={styles.description}>
            加减购按钮组件用于商品数量的增减操作，支持手动输入数量。
          </Text>
          
          <View style={styles.demoContainer}>
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>默认样式</Text>
              <QuantitySelector 
                initialValue={quantities.default}
                onChange={(value) => handleQuantityChange('default', value)}
              />
              <Text style={styles.demoValue}>数量: {quantities.default}</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>主题色样式</Text>
              <QuantitySelector 
                initialValue={quantities.primary}
                onChange={(value) => handleQuantityChange('primary', value)}
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.primary}</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>边框样式</Text>
              <QuantitySelector 
                initialValue={quantities.outline}
                onChange={(value) => handleQuantityChange('outline', value)}
                theme="outline"
              />
              <Text style={styles.demoValue}>数量: {quantities.outline}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>尺寸大小</Text>
          <Text style={styles.description}>
            组件支持小、中、大三种尺寸，适应不同场景需求。
          </Text>
          
          <View style={styles.demoContainer}>
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>小尺寸</Text>
              <QuantitySelector 
                initialValue={quantities.small}
                onChange={(value) => handleQuantityChange('small', value)}
                size="small"
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.small}</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>中尺寸</Text>
              <QuantitySelector 
                initialValue={quantities.medium}
                onChange={(value) => handleQuantityChange('medium', value)}
                size="medium"
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.medium}</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>大尺寸</Text>
              <QuantitySelector 
                initialValue={quantities.large}
                onChange={(value) => handleQuantityChange('large', value)}
                size="large"
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.large}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>特殊状态</Text>
          <Text style={styles.description}>
            组件支持禁用状态和只读状态，以及设置最小值、最大值和步长。
          </Text>
          
          <View style={styles.demoContainer}>
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>禁用状态</Text>
              <QuantitySelector 
                initialValue={quantities.disabled}
                onChange={(value) => handleQuantityChange('disabled', value)}
                disabled={true}
              />
              <Text style={styles.demoValue}>数量: {quantities.disabled}</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>不可输入</Text>
              <QuantitySelector 
                initialValue={quantities.noInput}
                onChange={(value) => handleQuantityChange('noInput', value)}
                allowManualInput={false}
                theme="outline"
              />
              <Text style={styles.demoValue}>数量: {quantities.noInput}</Text>
            </View>
          </View>
          
          <View style={styles.demoContainer}>
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>最小值/最大值</Text>
              <QuantitySelector 
                initialValue={quantities.minMax}
                onChange={(value) => handleQuantityChange('minMax', value)}
                minValue={2}
                maxValue={10}
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.minMax} (范围: 2-10)</Text>
            </View>
            
            <View style={styles.demoItem}>
              <Text style={styles.demoTitle}>步长设置</Text>
              <QuantitySelector 
                initialValue={quantities.step}
                onChange={(value) => handleQuantityChange('step', value)}
                step={2}
                theme="primary"
              />
              <Text style={styles.demoValue}>数量: {quantities.step} (步长: 2)</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>使用示例</Text>
          <Text style={styles.description}>
            以下是在商品卡片中使用加减购按钮的示例。
          </Text>
          
          <View style={styles.productCard}>
            <View style={styles.productImageContainer}>
              <View style={styles.productImage} />
            </View>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>智能手表 Ultra</Text>
              <Text style={styles.productPrice}>¥2199</Text>
              <View style={styles.productActions}>
                <QuantitySelector 
                  size="small"
                  theme="outline"
                  initialValue={1}
                />
                <TouchableOpacity style={styles.addToCartButton}>
                  <Text style={styles.addToCartText}>加入购物车</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          
          <View style={styles.codeContainer}>
            <Text style={styles.codeTitle}>组件用法</Text>
            <Text style={styles.codeText}>
{`import QuantitySelector from '../components/QuantitySelector';

// 基础用法
<QuantitySelector 
  initialValue={1}
  onChange={(value) => console.log(value)}
/>

// 设置主题和尺寸
<QuantitySelector 
  initialValue={1}
  theme="primary"
  size="large"
  onChange={(value) => console.log(value)}
/>

// 设置最小值、最大值和步长
<QuantitySelector 
  initialValue={5}
  minValue={1}
  maxValue={10}
  step={2}
  onChange={(value) => console.log(value)}
/>`}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  section: {
    padding: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 20,
  },
  demoContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  demoItem: {
    marginBottom: 20,
    alignItems: 'center',
  },
  demoTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 15,
    color: '#333',
  },
  demoValue: {
    fontSize: 14,
    color: '#666',
    marginTop: 10,
  },
  productCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    flexDirection: 'row',
  },
  productImageContainer: {
    width: 100,
    height: 100,
    marginRight: 15,
    borderRadius: 8,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  productInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF4D4F',
    marginBottom: 10,
  },
  productActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  addToCartButton: {
    backgroundColor: '#FF4D4F',
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  addToCartText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  codeContainer: {
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  codeTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    color: '#333',
  },
  codeText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});
