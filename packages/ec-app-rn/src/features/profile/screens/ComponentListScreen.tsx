import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../navigation';
import Icon from 'react-native-vector-icons/Ionicons';

// Mock data for component categories
interface ComponentItem {
  name: string;
  icon: string;
  color: string;
}

interface ComponentSection {
  title: string;
  items: ComponentItem[];
}

const COMPONENT_CATEGORIES: ComponentSection[] = [
  {
    title: '基础组件',
    items: [
      { name: '按钮', icon: 'radio-button-on', color: '#4A90E2' },
      { name: '卡片', icon: 'card', color: '#E94E77' },
      { name: '标签', icon: 'pricetag', color: '#F5A623' },
      { name: '开关', icon: 'toggle', color: '#50E3C2' },
      { name: '复选框', icon: 'checkbox', color: '#7ED321' },
      { name: '加购按钮', icon: 'cart', color: '#FF4D4F' },
    ],
  },
  {
    title: '展示组件',
    items: [
      { name: '轮播图', icon: 'images', color: '#E94E77' },
      { name: '列表', icon: 'list', color: '#7ED321' },
      { name: '评分', icon: 'star', color: '#F5A623' },
      { name: '徽章', icon: 'ellipse', color: '#FF4D4F' },
      { name: '进度条', icon: 'bar-chart', color: '#4A90E2' },
      { name: '商品卡片', icon: 'cube', color: '#BD10E0' },
    ],
  },
  {
    title: '反馈组件',
    items: [
      { name: '对话框', icon: 'chatbox', color: '#4A90E2' },
      { name: '弹出框', icon: 'alert-circle', color: '#BD10E0' },
      { name: '加载中', icon: 'reload', color: '#F5A623' },
      { name: '通知', icon: 'notifications', color: '#50E3C2' },
      { name: '轻提示', icon: 'alert', color: '#FF9500' },
    ],
  },
  {
    title: '导航组件',
    items: [
      { name: '菜单栏', icon: 'menu', color: '#F5A623' },
      { name: '步骤条', icon: 'git-network', color: '#50E3C2' },
      { name: '导航栏', icon: 'navigate', color: '#1890FF' },
    ],
  },
];

type ComponentListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

export default function ComponentListScreen() {
  const navigation = useNavigation<ComponentListScreenNavigationProp>();

  const renderComponentItem = (item: ComponentItem) => {
    const handlePress = (item: ComponentItem) => {
      // Navigate based on the item name
      switch (item.name) {
        case '对话框':
          navigation.navigate('DialogDemo');
          break;
        case '弹出框':
          navigation.navigate('PopupDemo');
          break;
        case '标签':
          navigation.navigate('TagDemo');
          break;
        case '轮播图':
          navigation.navigate('CarouselExample');
          break;
        case '评分':
          navigation.navigate('RatingExample');
          break;
        case '徽章':
          navigation.navigate('BadgeExample');
          break;
        case '按钮':
          navigation.navigate('ButtonExample');
          break;
        case '开关':
          navigation.navigate('SwitchExample');
          break;
        case '进度条':
          navigation.navigate('ProgressExample');
          break;
        case '步骤条':
          navigation.navigate('StepperExample');
          break;
        case '复选框':
          navigation.navigate('CheckboxExample');
          break;
        case '加载中':
          navigation.navigate('LoadingExample');
          break;
        case '通知':
          navigation.navigate('NotificationExample');
          break;
        case '轻提示':
          navigation.navigate('ToastExample');
          break;
        case '列表':
          navigation.navigate('ListExample');
          break;
        case '商品卡片':
          navigation.navigate('ProductCardExample');
          break;
        case '加购按钮':
          navigation.navigate('AddToCartButtonExample');
          break;
        case '导航栏':
          navigation.navigate('NavigationBarExample');
          break;
        case '菜单栏':
          navigation.navigate('MenuBarExample');
          break;
        default:
          // For other components, show a toast message
          Alert.alert('提示', `您点击了${item.name}组件，该组件正在开发中`);
      }
    };

    return (
      <TouchableOpacity
        key={item.name}
        style={styles.itemContainer}
        onPress={() => handlePress(item)}
      >
        <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
          <Icon name={item.icon} size={20} color="#fff" />
        </View>
        <Text style={styles.itemLabel}>{item.name}</Text>
        <View style={styles.itemValueContainer}>
          <Icon name="chevron-forward-outline" size={20} color="#C7C7CC" />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>组件列表</Text>
        <View style={styles.placeholder} />
      </View>
      <ScrollView style={styles.container}>
        {COMPONENT_CATEGORIES.map(category => (
          <View key={category.title} style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>{category.title}</Text>
            <View style={styles.sectionItems}>
              {category.items.map(renderComponentItem)}
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  sectionContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#6D6D72',
    marginLeft: 15,
    marginBottom: 8,
  },
  sectionItems: {
    backgroundColor: '#fff',
    borderTopWidth: 0.5,
    borderBottomWidth: 0.5,
    borderColor: '#C8C7CC',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 0.5,
    borderColor: '#C8C7CC',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  itemLabel: {
    flex: 1,
    fontSize: 17,
  },
  itemValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
