import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation'; // Adjust if your RootStackParamList is elsewhere
import Icon from 'react-native-vector-icons/Ionicons';

// Mock Data
const user = {
  name: '张三',
  id: '8675309',
  level: 4,
  avatar: 'https://i.imgur.com/6XfUaYg.png',
};

const stats = [
  { value: '32', label: '收藏' },
  { value: '128', label: '关注' },
  { value: '1024', label: '足迹' },
  { value: '96', label: '粉丝' },
];

const orders = [
  { name: '待付款', icon: 'wallet-outline', count: 2 },
  { name: '待发货', icon: 'cube-outline', count: 1 },
  { name: '待收货', icon: 'rocket-outline', count: 0 },
  { name: '待评价', icon: 'chatbox-ellipses-outline', count: 0 },
  { name: '退款/售后', icon: 'refresh-outline', count: 0 },
];

const wallet = [
  { value: '¥520', label: '余额' },
  { value: '3200', label: '积分' },
  { value: '12', label: '优惠券' },
  { value: '8', label: '礼品卡' },
];

const services = [
  { name: '我的收藏', icon: 'heart-outline' },
  { name: '关注店铺', icon: 'business-outline' },
  { name: '浏览历史', icon: 'time-outline' },
  { name: '收货地址', icon: 'location-outline' },
  { name: '客服中心', icon: 'headset-outline' },
  { name: '邀请有礼', icon: 'gift-outline' },
  { name: '领券中心', icon: 'ticket-outline' },
  { name: '帮助中心', icon: 'help-circle-outline' },
];

const recommendations = [
  { id: '1', name: '真无线蓝牙耳机 Pro', price: '1299', image: 'https://i.imgur.com/nJ3OqCj.png' },
  { id: '2', name: '智能手表 Ultra', price: '2199', image: 'https://i.imgur.com/5a2zGQS.png' },
  { id: '3', name: '头戴式无线耳机', price: '899', image: 'https://i.imgur.com/I6aG3hV.png' },
  { id: '4', name: '20000mAh 快充移动电源', price: '199', image: 'https://i.imgur.com/tG3aK8A.png' },
];

const { width } = Dimensions.get('window');
const recommendItemWidth = (width - 45) / 2;

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

export default function ProfileScreen() {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <ScrollView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.navigate('Settings')}>
            <Icon name="settings-outline" size={24} color="#333" />
          </TouchableOpacity>
          <View style={styles.headerIcons}>
            <TouchableOpacity>
              <Icon name="notifications-outline" size={24} color="#333" />
            </TouchableOpacity>
            <TouchableOpacity style={{ marginLeft: 15 }}>
              <Icon name="chatbubble-ellipses-outline" size={24} color="#333" />
            </TouchableOpacity>
          </View>
        </View>

        {/* User Info */}
        <View style={styles.userInfoContainer}>
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
          <View style={styles.userInfoText}>
            <Text style={styles.userName}>{user.name}</Text>
            <Text style={styles.userId}>ID: {user.id}</Text>
            <View style={styles.levelContainer}>
              <Text style={styles.levelText}>Lv.{user.level}</Text>
              <View style={styles.progressBar}>
                <View style={styles.progress} />
              </View>
            </View>
          </View>
          <Icon name="chevron-forward-outline" size={20} color="#ccc" />
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statItem}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        {/* Card: My Orders */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>我的订单</Text>
            <TouchableOpacity>
              <Text style={styles.cardLink}>全部订单 &gt;</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContentRow}>
            {orders.map((order, index) => (
              <TouchableOpacity key={index} style={styles.iconMenuItem}>
                {order.count > 0 && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{order.count}</Text>
                  </View>
                )}
                <Icon name={order.icon} size={28} color="#555" />
                <Text style={styles.iconMenuLabel}>{order.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Card: My Wallet */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>我的钱包</Text>
            <TouchableOpacity>
              <Text style={styles.cardLink}>查看更多 &gt;</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContentRow}>
            {wallet.map((item, index) => (
              <TouchableOpacity key={index} style={styles.statItem}>
                <Text style={styles.walletValue}>{item.value}</Text>
                <Text style={styles.statLabel}>{item.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Card: My Services */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>我的服务</Text>
          </View>
          <View style={styles.servicesGrid}>
            {services.map((service, index) => (
              <TouchableOpacity key={index} style={styles.serviceItem}>
                <Icon name={service.icon} size={28} color="#555" />
                <Text style={styles.iconMenuLabel}>{service.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recommendations */}
        <View style={styles.recommendContainer}>
          <Text style={styles.cardTitle}>为你推荐</Text>
          <View style={styles.recommendGrid}>
            {recommendations.map(item => (
              <TouchableOpacity key={item.id} style={styles.recommendItem}>
                <Image source={{ uri: item.image }} style={styles.recommendImage} />
                <Text style={styles.recommendName} numberOfLines={2}>{item.name}</Text>
                <Text style={styles.recommendPrice}>¥{item.price}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: '#fff',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  userInfoText: {
    flex: 1,
    marginLeft: 15,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  userId: {
    fontSize: 14,
    color: '#888',
    marginTop: 4,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  levelText: {
    backgroundColor: '#FFF5E1',
    color: '#E5A743',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  progressBar: {
    height: 6,
    width: 100,
    backgroundColor: '#eee',
    borderRadius: 3,
  },
  progress: {
    height: '100%',
    width: '60%', // Mock progress
    backgroundColor: '#E5A743',
    borderRadius: 3,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    marginBottom: 10,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 10,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  cardLink: {
    fontSize: 12,
    color: '#888',
  },
  cardContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  iconMenuItem: {
    alignItems: 'center',
    flex: 1,
  },
  iconMenuLabel: {
    fontSize: 12,
    color: '#555',
    marginTop: 6,
  },
  badge: {
    position: 'absolute',
    top: -5,
    right: 5,
    backgroundColor: '#FF4D4F',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  walletValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#E54D42',
  },
  servicesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  serviceItem: {
    width: '25%',
    alignItems: 'center',
    marginBottom: 15,
  },
  recommendContainer: {
    paddingHorizontal: 15,
    marginTop: 5,
    marginBottom: 15,
  },
  recommendGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  recommendItem: {
    width: recommendItemWidth,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    overflow: 'hidden',
  },
  recommendImage: {
    width: '100%',
    height: recommendItemWidth,
  },
  recommendName: {
    fontSize: 14,
    color: '#333',
    margin: 8,
  },
  recommendPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E54D42',
    marginHorizontal: 8,
    marginBottom: 8,
  },
});
