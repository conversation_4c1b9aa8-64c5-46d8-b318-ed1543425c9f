import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

const { width } = Dimensions.get('window');
const cardWidth = width - 40;

// Sample product cards to display
const PRODUCT_CARDS = [
  {
    id: '1',
    name: '真无线蓝牙耳机 Pro',
    price: '¥1299',
    originalPrice: '¥1499',
    discount: '8.7折',
    image: 'https://i.imgur.com/nJ3OqCj.png',
    rating: 4.8,
    reviewCount: 2563,
    tags: ['新品', '热卖'],
  },
  {
    id: '2',
    name: '智能手表 Ultra',
    price: '¥2199',
    originalPrice: '¥2499',
    discount: '8.8折',
    image: 'https://i.imgur.com/5a2zGQS.png',
    rating: 4.9,
    reviewCount: 1872,
    tags: ['限时特惠'],
  },
  {
    id: '3',
    name: '头戴式无线耳机',
    price: '¥899',
    originalPrice: '¥1099',
    discount: '8.2折',
    image: 'https://i.imgur.com/I6aG3hV.png',
    rating: 4.7,
    reviewCount: 3241,
    tags: ['热卖'],
  },
];

export default function CardDetailScreen() {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>商品卡片组件</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础商品卡片</Text>
          <Text style={styles.description}>
            商品卡片是电商应用中最常用的组件之一，用于展示商品信息，包括图片、名称、价格等。
          </Text>
          
          {PRODUCT_CARDS.map((card, index) => (
            <View key={card.id} style={styles.cardContainer}>
              <Text style={styles.cardTitle}>样式 {index + 1}</Text>
              
              {/* Card Style 1 - Horizontal card */}
              {index === 0 && (
                <View style={styles.horizontalCard}>
                  <Image source={{ uri: card.image }} style={styles.horizontalCardImage} />
                  <View style={styles.horizontalCardContent}>
                    <View style={styles.tagContainer}>
                      {card.tags.map(tag => (
                        <View key={tag} style={styles.tag}>
                          <Text style={styles.tagText}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                    <Text style={styles.productName} numberOfLines={2}>{card.name}</Text>
                    <View style={styles.priceContainer}>
                      <Text style={styles.price}>{card.price}</Text>
                      <Text style={styles.originalPrice}>{card.originalPrice}</Text>
                      <Text style={styles.discount}>{card.discount}</Text>
                    </View>
                    <View style={styles.ratingContainer}>
                      <Icon name="star" size={14} color="#FFB800" />
                      <Text style={styles.rating}>{card.rating}</Text>
                      <Text style={styles.reviewCount}>{card.reviewCount}条评价</Text>
                    </View>
                    <TouchableOpacity style={styles.addToCartButton}>
                      <Icon name="cart-outline" size={20} color="#fff" />
                    </TouchableOpacity>
                  </View>
                </View>
              )}
              
              {/* Card Style 2 - Vertical card with large image */}
              {index === 1 && (
                <View style={styles.verticalCard}>
                  <Image source={{ uri: card.image }} style={styles.verticalCardImage} />
                  <View style={styles.verticalCardContent}>
                    <Text style={styles.productName} numberOfLines={2}>{card.name}</Text>
                    <View style={styles.priceContainer}>
                      <Text style={styles.price}>{card.price}</Text>
                      <Text style={styles.originalPrice}>{card.originalPrice}</Text>
                    </View>
                    <View style={styles.tagContainer}>
                      {card.tags.map(tag => (
                        <View key={tag} style={styles.tag}>
                          <Text style={styles.tagText}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                    <View style={styles.buttonContainer}>
                      <TouchableOpacity style={styles.buyButton}>
                        <Text style={styles.buyButtonText}>立即购买</Text>
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.cartButton}>
                        <Icon name="cart-outline" size={20} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              )}
              
              {/* Card Style 3 - Grid card */}
              {index === 2 && (
                <View style={styles.gridCard}>
                  <Image source={{ uri: card.image }} style={styles.gridCardImage} />
                  <View style={styles.gridCardContent}>
                    <Text style={styles.productName} numberOfLines={2}>{card.name}</Text>
                    <View style={styles.priceRow}>
                      <Text style={styles.price}>{card.price}</Text>
                      <TouchableOpacity style={styles.favoriteButton}>
                        <Icon name="heart-outline" size={20} color="#FF4D4F" />
                      </TouchableOpacity>
                    </View>
                    <View style={styles.ratingContainer}>
                      <Icon name="star" size={14} color="#FFB800" />
                      <Text style={styles.rating}>{card.rating}</Text>
                      <Text style={styles.reviewCount}>{card.reviewCount}条评价</Text>
                    </View>
                  </View>
                </View>
              )}
              
              <View style={styles.codeContainer}>
                <Text style={styles.codeTitle}>组件代码</Text>
                <Text style={styles.codeText}>
                  {index === 0 ? 
                    '<HorizontalProductCard\n  image={product.image}\n  name={product.name}\n  price={product.price}\n  originalPrice={product.originalPrice}\n  discount={product.discount}\n  rating={product.rating}\n  reviewCount={product.reviewCount}\n  tags={product.tags}\n/>' :
                    index === 1 ?
                    '<VerticalProductCard\n  image={product.image}\n  name={product.name}\n  price={product.price}\n  originalPrice={product.originalPrice}\n  tags={product.tags}\n/>' :
                    '<GridProductCard\n  image={product.image}\n  name={product.name}\n  price={product.price}\n  rating={product.rating}\n  reviewCount={product.reviewCount}\n/>'
                  }
                </Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 20,
  },
  cardContainer: {
    marginBottom: 30,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  horizontalCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 15,
  },
  horizontalCardImage: {
    width: 120,
    height: 120,
  },
  horizontalCardContent: {
    flex: 1,
    padding: 10,
    position: 'relative',
  },
  verticalCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 15,
  },
  verticalCardImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  verticalCardContent: {
    padding: 10,
  },
  gridCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 15,
    width: cardWidth,
  },
  gridCardImage: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  gridCardContent: {
    padding: 10,
  },
  tagContainer: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  tag: {
    backgroundColor: '#FFF0F0',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 5,
  },
  tagText: {
    color: '#FF4D4F',
    fontSize: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF4D4F',
  },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 5,
  },
  discount: {
    fontSize: 12,
    color: '#FF4D4F',
    marginLeft: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 12,
    color: '#333',
    marginLeft: 2,
  },
  reviewCount: {
    fontSize: 12,
    color: '#999',
    marginLeft: 5,
  },
  addToCartButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: '#FF4D4F',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 10,
  },
  buyButton: {
    flex: 1,
    backgroundColor: '#FF4D4F',
    borderRadius: 4,
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  buyButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  cartButton: {
    backgroundColor: '#FF8F1F',
    borderRadius: 4,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    padding: 5,
  },
  codeContainer: {
    backgroundColor: '#F5F5F5',
    padding: 10,
    borderRadius: 8,
    marginTop: 15,
  },
  codeTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#333',
  },
  codeText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});
