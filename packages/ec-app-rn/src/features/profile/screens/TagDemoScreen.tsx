import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Tag, TagGroup, CheckableTag } from '../../../shared/components/Tag';
import Icon from 'react-native-vector-icons/Ionicons';

const TagDemoScreen: React.FC = () => {
  const [selectedTags, setSelectedTags] = useState<string[]>(['React']);
  
  const handleTagChange = (tag: string, checked: boolean) => {
    const nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter(t => t !== tag);
    setSelectedTags(nextSelectedTags);
  };
  
  const categories = ['React', 'Vue', 'Angular', 'Svelte', 'Next.js', 'Nuxt.js'];
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.sectionTitle}>基本标签</Text>
        <TagGroup>
          <Tag text="标签" />
          <Tag text="可点击标签" onPress={() => console.log('Tag pressed')} />
          <Tag text="可关闭标签" closable onClose={() => console.log('Tag closed')} />
          <Tag text="禁用标签" disabled />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>标签类型</Text>
        <TagGroup>
          <Tag text="默认" type="default" />
          <Tag text="主要" type="primary" />
          <Tag text="成功" type="success" />
          <Tag text="警告" type="warning" />
          <Tag text="危险" type="danger" />
          <Tag text="信息" type="info" />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>轮廓标签</Text>
        <TagGroup>
          <Tag text="默认" type="default" outlined />
          <Tag text="主要" type="primary" outlined />
          <Tag text="成功" type="success" outlined />
          <Tag text="警告" type="warning" outlined />
          <Tag text="危险" type="danger" outlined />
          <Tag text="信息" type="info" outlined />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>标签尺寸</Text>
        <TagGroup>
          <Tag text="小号标签" size="small" />
          <Tag text="中号标签" size="medium" />
          <Tag text="大号标签" size="large" />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>标签形状</Text>
        <TagGroup>
          <Tag text="方形" shape="square" />
          <Tag text="圆角" shape="rounded" />
          <Tag text="胶囊形" shape="pill" />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>带图标的标签</Text>
        <TagGroup>
          <Tag text="收藏" iconName="star" type="warning" />
          <Tag text="设置" iconName="settings" type="info" />
          <Tag text="喜欢" iconName="heart" type="danger" />
          <Tag text="成功" iconName="checkmark-circle" type="success" />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>标签组布局</Text>
        <Text style={styles.subTitle}>水平布局</Text>
        <TagGroup direction="horizontal" spacing={8}>
          <Tag text="标签1" type="primary" />
          <Tag text="标签2" type="success" />
          <Tag text="标签3" type="warning" />
        </TagGroup>
        
        <Text style={styles.subTitle}>垂直布局</Text>
        <TagGroup direction="vertical" spacing={8}>
          <Tag text="标签1" type="primary" />
          <Tag text="标签2" type="success" />
          <Tag text="标签3" type="warning" />
        </TagGroup>
        
        <Text style={styles.subTitle}>自动换行</Text>
        <TagGroup direction="wrap" spacing={8}>
          <Tag text="标签1" type="primary" />
          <Tag text="标签2" type="success" />
          <Tag text="标签3" type="warning" />
          <Tag text="标签4" type="danger" />
          <Tag text="标签5" type="info" />
          <Tag text="标签6" type="default" />
          <Tag text="标签7" type="primary" />
          <Tag text="标签8" type="success" />
        </TagGroup>
        
        <Text style={styles.sectionTitle}>可选择标签</Text>
        <TagGroup>
          {categories.map(category => (
            <CheckableTag
              key={category}
              text={category}
              checked={selectedTags.includes(category)}
              onChange={checked => handleTagChange(category, checked)}
            />
          ))}
        </TagGroup>
        
        <Text style={styles.sectionTitle}>自定义颜色的可选择标签</Text>
        <TagGroup>
          <CheckableTag
            text="自定义颜色"
            checked={true}
            onChange={() => {}}
            colors={{
              checkedBg: '#722ED1',
              checkedText: '#FFFFFF',
            }}
          />
          <CheckableTag
            text="另一个自定义"
            checked={true}
            onChange={() => {}}
            colors={{
              checkedBg: '#13C2C2',
              checkedText: '#FFFFFF',
            }}
          />
          <CheckableTag
            text="禁用状态"
            checked={true}
            onChange={() => {}}
            disabled
          />
        </TagGroup>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 12,
    color: '#333333',
  },
  subTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 8,
    color: '#666666',
  },
});

export default TagDemoScreen;
