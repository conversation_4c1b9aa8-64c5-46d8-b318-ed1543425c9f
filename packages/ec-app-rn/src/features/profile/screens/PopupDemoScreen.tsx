import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

// Import our Popup components
import Popup from '../../../shared/components/Popup';
import { usePopupState } from '../../../shared/components/Popup';
import { PopupProvider, usePopup } from '../../../shared/components/Popup';

// Demo section component
const DemoSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <View style={styles.demoSection}>
    <Text style={styles.demoSectionTitle}>{title}</Text>
    <View style={styles.demoContent}>
      {children}
    </View>
  </View>
);

// Bottom Popup Demo
const BottomPopupDemo = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();
  
  return (
    <View>
      <TouchableOpacity 
        style={styles.demoButton} 
        onPress={showPopup}
      >
        <Text style={styles.demoButtonText}>底部弹出框</Text>
      </TouchableOpacity>
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        title="底部弹出框"
        position="bottom"
        animationType="slide"
      >
        <View style={styles.popupContent}>
          <Text style={styles.popupText}>
            这是一个从底部弹出的弹出框。常用于显示操作菜单、筛选条件或其他需要用户选择的内容。
          </Text>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={hidePopup}
          >
            <Text style={styles.closeButtonText}>关闭</Text>
          </TouchableOpacity>
        </View>
      </Popup>
    </View>
  );
};

// Top Popup Demo
const TopPopupDemo = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#FF9800' }]} 
        onPress={showPopup}
      >
        <Text style={styles.demoButtonText}>顶部弹出框</Text>
      </TouchableOpacity>
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        title="顶部弹出框"
        position="top"
        animationType="slide"
      >
        <View style={styles.popupContent}>
          <Text style={styles.popupText}>
            这是一个从顶部弹出的弹出框。适合显示通知、警告或临时信息。
          </Text>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={hidePopup}
          >
            <Text style={styles.closeButtonText}>关闭</Text>
          </TouchableOpacity>
        </View>
      </Popup>
    </View>
  );
};

// Center Popup Demo
const CenterPopupDemo = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#9C27B0' }]} 
        onPress={showPopup}
      >
        <Text style={styles.demoButtonText}>中心弹出框</Text>
      </TouchableOpacity>
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        position="center"
        animationType="fade"
      >
        <View style={styles.popupContent}>
          <Text style={styles.popupTitle}>中心弹出框</Text>
          <Text style={styles.popupText}>
            这是一个从中心弹出的弹出框。适合显示重要信息或需要用户确认的内容。
          </Text>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={hidePopup}
          >
            <Text style={styles.closeButtonText}>关闭</Text>
          </TouchableOpacity>
        </View>
      </Popup>
    </View>
  );
};

// Action Sheet Demo
const ActionSheetDemo = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();
  
  const actions = [
    { icon: 'share-social', label: '分享', color: '#2196F3' },
    { icon: 'heart', label: '收藏', color: '#F44336' },
    { icon: 'download', label: '保存', color: '#4CAF50' },
    { icon: 'copy', label: '复制链接', color: '#FF9800' },
  ];

  const handleAction = (action: string) => {
    console.log(`Selected action: ${action}`);
    hidePopup();
  };

  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#4CAF50' }]} 
        onPress={showPopup}
      >
        <Text style={styles.demoButtonText}>操作菜单</Text>
      </TouchableOpacity>
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        position="bottom"
        animationType="slide"
        customHeader={
          <View style={styles.customHeader}>
            <View style={styles.dragHandle} />
            <Text style={styles.popupHeaderTitle}>选择操作</Text>
          </View>
        }
      >
        <View style={styles.actionList}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.actionItem}
              onPress={() => handleAction(action.label)}
            >
              <View style={[styles.iconContainer, { backgroundColor: action.color }]}>
                <Icon name={action.icon} size={22} color="#FFF" />
              </View>
              <Text style={styles.actionLabel}>{action.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={hidePopup}
        >
          <Text style={styles.cancelButtonText}>取消</Text>
        </TouchableOpacity>
      </Popup>
    </View>
  );
};

// Context API Demo
const ContextApiDemo = () => {
  const PopupTrigger = () => {
    const { showPopup } = usePopup();
  
    return (
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#2196F3' }]} 
        onPress={() => {
          showPopup({
            position: 'center',
            children: (
              <View style={styles.popupContent}>
                <Text style={styles.popupTitle}>Context API</Text>
                <Text style={styles.popupText}>
                  使用Context API可以在应用的任何地方轻松调用弹出框，无需传递props。
                </Text>
              </View>
            ),
          });
        }}
      >
        <Text style={styles.demoButtonText}>Context API 示例</Text>
      </TouchableOpacity>
    );
  };

  return (
    <PopupProvider>
      <PopupTrigger />
    </PopupProvider>
  );
};

export default function PopupDemoScreen() {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>弹出框组件</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView style={styles.container}>
        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>弹出框组件</Text>
          <Text style={styles.descriptionText}>
            弹出框是一种模态窗口，可以从屏幕的不同位置弹出，用于显示临时内容、操作菜单或需要用户交互的界面。
            我们的弹出框组件支持多种位置和动画效果，适用于各种场景。
          </Text>
        </View>
        
        <DemoSection title="基础弹出框">
          <View style={styles.row}>
            <BottomPopupDemo />
            <TopPopupDemo />
          </View>
        </DemoSection>
        
        <DemoSection title="中心弹出框">
          <CenterPopupDemo />
        </DemoSection>
        
        <DemoSection title="操作菜单">
          <ActionSheetDemo />
        </DemoSection>
        
        <DemoSection title="Context API">
          <ContextApiDemo />
        </DemoSection>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  description: {
    padding: 15,
    backgroundColor: '#fff',
    marginTop: 20,
    marginHorizontal: 15,
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#666',
  },
  demoSection: {
    marginTop: 20,
    marginHorizontal: 15,
  },
  demoSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  demoContent: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  demoButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 5,
    marginHorizontal: 5,
    flex: 1,
    minWidth: '45%',
  },
  demoButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  popupContent: {
    padding: 16,
    alignItems: 'center',
  },
  popupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  popupText: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  closeButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  customHeader: {
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dragHandle: {
    width: 40,
    height: 5,
    backgroundColor: '#E0E0E0',
    borderRadius: 2.5,
    marginBottom: 10,
  },
  popupHeaderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  actionList: {
    maxHeight: 300,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionLabel: {
    fontSize: 16,
    color: '#333',
  },
  cancelButton: {
    marginTop: 10,
    paddingVertical: 16,
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
});
