import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';

// Type Definitions
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  tags: string[];
  image: string;
}

interface RecommendationSection {
  id: string;
  title: string;
  matchRate: number;
  products: Product[];
}

interface RecentlyViewedItem {
  id: string;
  image: string;
}

// Mock Data
const filterTags = ['为你推荐', '数码', '服饰', '美食'];

const recommendationData: RecommendationSection[] = [
  {
    id: '1',
    title: '数码爱好者精选',
    matchRate: 98,
    products: [
      {
        id: 'p1',
        name: '智能手表 Ultra',
        description: 'GPS | 心率监测 | 长续航',
        price: 2199,
        originalPrice: 2499,
        tags: ['热门', '新品'],
        image: 'https://i.imgur.com/5a2zGQS.png',
      },
      {
        id: 'p2',
        name: '无线蓝牙耳机',
        description: '降噪 | Hi-Fi音质 | 24小时续航',
        price: 1299,
        originalPrice: 1599,
        tags: ['推荐'],
        image: 'https://i.imgur.com/O4MwaeG.png',
      },
    ],
  },
  {
    id: '2',
    title: '运动健身推荐',
    matchRate: 95,
    products: [
      {
        id: 'p3',
        name: '专业跑步鞋',
        description: '轻量缓震 | 透气网面 | 防滑耐磨',
        price: 599,
        originalPrice: 799,
        tags: ['畅销'],
        image: 'https://i.imgur.com/sC0zt3I.png',
      },
      {
        id: 'p4',
        name: '智能运动手环',
        description: '心率监测 | 睡眠分析 | 14天续航',
        price: 199,
        originalPrice: 240,
        tags: ['限时'],
        image: 'https://i.imgur.com/gTj9s2Y.png',
      },
    ],
  },
];

const recentlyViewedData: RecentlyViewedItem[] = [
  { id: 'rv1', image: 'https://i.imgur.com/4l3gU22.png' },
  { id: 'rv2', image: 'https://i.imgur.com/JPlS2Vz.png' },
  { id: 'rv3', image: 'https://i.imgur.com/tG3aK8A.png' },
];

const ProductCard = ({ product }: { product: Product }) => (
  <View style={styles.productCard}>
    <Image source={{ uri: product.image }} style={styles.productImage} />
    <View style={styles.productInfo}>
      <Text style={styles.productName}>{product.name}</Text>
      <Text style={styles.productDescription}>{product.description}</Text>
      <View style={styles.priceContainer}>
        <Text style={styles.productPrice}>¥{product.price}</Text>
        {product.originalPrice && (
          <Text style={styles.originalPrice}>¥{product.originalPrice}</Text>
        )}
      </View>
      <View style={styles.tagsContainer}>
        {product.tags.map(tag => (
          <View key={tag} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>
    </View>
    <TouchableOpacity style={styles.favoriteIcon}>
      <Icon name="heart-outline" size={22} color="#888" />
    </TouchableOpacity>
  </View>
);

export default function InterestScreen() {
  const [activeTag, setActiveTag] = useState(filterTags[0]);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>兴趣推荐</Text>
        <TouchableOpacity>
          <Icon name="options-outline" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.filtersContainer}>
          {filterTags.map(tag => (
            <TouchableOpacity
              key={tag}
              style={[styles.filterChip, activeTag === tag && styles.activeFilterChip]}
              onPress={() => setActiveTag(tag)}
            >
              <Text style={[styles.filterChipText, activeTag === tag && styles.activeFilterChipText]}>
                {tag}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <ImageBackground
          source={{ uri: 'https://i.imgur.com/UPhPb2D.png' }}
          style={styles.interestBanner}
          imageStyle={{ borderRadius: 12 }}
        >
          <Text style={styles.bannerTitle}>基于你的兴趣</Text>
          <Text style={styles.bannerSubtitle}>我们为你精选了这些产品</Text>
        </ImageBackground>

        {recommendationData.map(section => (
          <View key={section.id} style={styles.recommendationSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
              <Text style={styles.matchRate}>{section.matchRate}% 匹配</Text>
            </View>
            {section.products.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </View>
        ))}

        <View style={styles.recentlyViewedSection}>
          <Text style={styles.sectionTitle}>最近浏览</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {recentlyViewedData.map(item => (
              <Image key={item.id} source={{ uri: item.image }} style={styles.recentlyViewedItem} />
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f7f7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
  },
  filterChip: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  activeFilterChip: {
    backgroundColor: '#4A90E2',
  },
  filterChipText: {
    fontSize: 14,
    color: '#333',
  },
  activeFilterChipText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  interestBanner: {
    height: 120,
    margin: 15,
    justifyContent: 'center',
    padding: 20,
  },
  bannerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  bannerSubtitle: {
    fontSize: 14,
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  recommendationSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  matchRate: {
    fontSize: 14,
    color: '#E94E77',
    fontWeight: 'bold',
  },
  productCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  productInfo: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'space-around',
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productDescription: {
    fontSize: 12,
    color: '#888',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  productPrice: {
    fontSize: 18,
    color: '#E94E77',
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 12,
    color: '#888',
    textDecorationLine: 'line-through',
    marginLeft: 10,
  },
  tagsContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  tag: {
    backgroundColor: '#FFE5EC',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 5,
  },
  tagText: {
    fontSize: 10,
    color: '#E94E77',
  },
  favoriteIcon: {
    position: 'absolute',
    top: 10,
    right: 0,
  },
  recentlyViewedSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 15,
    marginBottom: 15,
  },
  recentlyViewedItem: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
});
