import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';

// Import our Dialog components
import Dialog from '../../../shared/components/Dialog';
import { useDialogState } from '../../../shared/components/Dialog';
import { DialogProvider, useDialog } from '../../../shared/components/Dialog';

// Demo section component
const DemoSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <View style={styles.demoSection}>
    <Text style={styles.demoSectionTitle}>{title}</Text>
    <View style={styles.demoContent}>
      {children}
    </View>
  </View>
);

// Basic Dialog Demo
const BasicDialogDemo = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  
  return (
    <View>
      <TouchableOpacity 
        style={styles.demoButton} 
        onPress={showDialog}
      >
        <Text style={styles.demoButtonText}>基础对话框</Text>
      </TouchableOpacity>
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="基础对话框"
        message="这是一个基础对话框示例，包含确认和取消按钮。"
        onConfirm={() => {
          console.log('确认按钮被点击');
          hideDialog();
        }}
      />
    </View>
  );
};

// Success Dialog Demo
const SuccessDialogDemo = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#4CAF50' }]} 
        onPress={showDialog}
      >
        <Text style={styles.demoButtonText}>成功对话框</Text>
      </TouchableOpacity>
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="操作成功"
        message="您的操作已成功完成！"
        type="success"
        confirmText="知道了"
        onConfirm={() => {
          console.log('Success dialog confirmed');
          hideDialog();
        }}
      />
    </View>
  );
};

// Warning Dialog Demo
const WarningDialogDemo = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#FF9800' }]} 
        onPress={showDialog}
      >
        <Text style={styles.demoButtonText}>警告对话框</Text>
      </TouchableOpacity>
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="警告"
        message="此操作可能导致数据丢失，确定要继续吗？"
        type="warning"
        confirmText="继续"
        cancelText="取消"
        onConfirm={() => {
          console.log('Warning dialog confirmed');
          hideDialog();
        }}
      />
    </View>
  );
};

// Error Dialog Demo
const ErrorDialogDemo = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#F44336' }]} 
        onPress={showDialog}
      >
        <Text style={styles.demoButtonText}>错误对话框</Text>
      </TouchableOpacity>
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="操作失败"
        message="抱歉，操作过程中出现了错误，请稍后重试。"
        type="error"
        confirmText="重试"
        cancelText="取消"
        onConfirm={() => {
          console.log('Error dialog retry clicked');
          hideDialog();
        }}
      />
    </View>
  );
};

// Custom Content Dialog Demo
const CustomContentDialogDemo = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  
  return (
    <View>
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#9C27B0' }]} 
        onPress={showDialog}
      >
        <Text style={styles.demoButtonText}>自定义内容对话框</Text>
      </TouchableOpacity>
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="自定义内容"
        confirmText="确认"
        cancelText="取消"
        onConfirm={() => {
          console.log('Custom dialog confirmed');
          hideDialog();
        }}
      >
        <View style={styles.customContent}>
          <Text style={styles.customContentText}>
            这是一个自定义内容的对话框示例。您可以在这里放置任何React Native组件，
            如表单、图片、列表等。
          </Text>
          <View style={styles.customContentDivider} />
          <Text style={styles.customContentEmphasis}>
            灵活性是这个对话框组件的核心特点！
          </Text>
        </View>
      </Dialog>
    </View>
  );
};

// Context API Demo
const ContextApiDemo = () => {
  const DialogTrigger = () => {
    const { showDialog } = useDialog();
  
    return (
      <TouchableOpacity 
        style={[styles.demoButton, { backgroundColor: '#2196F3' }]} 
        onPress={() => {
          showDialog({
            title: 'Context API',
            message: '使用Context API可以在应用的任何地方轻松调用对话框，无需传递props。',
            type: 'info',
            confirmText: '明白了',
          });
        }}
      >
        <Text style={styles.demoButtonText}>Context API 示例</Text>
      </TouchableOpacity>
    );
  };

  return (
    <DialogProvider>
      <DialogTrigger />
    </DialogProvider>
  );
};

export default function DialogDemoScreen() {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Icon name="chevron-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>对话框组件</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView style={styles.container}>
        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>对话框组件</Text>
          <Text style={styles.descriptionText}>
            对话框是一种模态窗口，用于向用户显示重要信息、请求用户输入或确认操作。
            我们的对话框组件支持多种类型和自定义内容，适用于各种场景。
          </Text>
        </View>
        
        <DemoSection title="基础对话框">
          <BasicDialogDemo />
        </DemoSection>
        
        <DemoSection title="状态对话框">
          <View style={styles.row}>
            <SuccessDialogDemo />
            <WarningDialogDemo />
            <ErrorDialogDemo />
          </View>
        </DemoSection>
        
        <DemoSection title="自定义内容">
          <CustomContentDialogDemo />
        </DemoSection>
        
        <DemoSection title="Context API">
          <ContextApiDemo />
        </DemoSection>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F2F2F6',
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F6',
    padding: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DCDCDC',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  placeholder: {
    width: 40,
  },
  description: {
    padding: 15,
    backgroundColor: '#fff',
    marginTop: 20,
    marginHorizontal: 15,
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#666',
  },
  demoSection: {
    marginTop: 20,
    marginHorizontal: 15,
  },
  demoSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  demoContent: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  demoButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 5,
    marginHorizontal: 5,
    flex: 1,
    minWidth: '45%',
  },
  demoButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  customContent: {
    padding: 10,
  },
  customContentText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#333',
  },
  customContentDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 10,
  },
  customContentEmphasis: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
    textAlign: 'center',
  },
});
