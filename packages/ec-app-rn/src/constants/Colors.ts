/**
 * E-commerce app color scheme based on the prototype
 */

// Main brand colors
const primaryColor = '#007AFF'; // iOS blue
const secondaryColor = '#FF3B30'; // iOS red for prices and important actions
const accentColor = '#34C759'; // iOS green for success states

export const Colors = {
  light: {
    // Text colors
    text: '#333333',
    textSecondary: '#666666',
    textTertiary: '#999999',
    textMuted: '#999999', // Using textTertiary as a base for muted
    textMutedOpac: '#66666680', // textSecondary with 50% opacity
    
    // Background colors
    background: '#FFFFFF',
    backgroundSecondary: '#F5F5F5',
    backgroundSoft: '#F9F9F9', // A very light soft background
    card: '#FFFFFF',
    imageBackground: '#e0e0e0',
    
    // UI elements
    border: '#EEEEEE',
    separator: '#F0F0F0',
    
    // Brand colors
    primary: primaryColor,
    secondary: secondaryColor,
    primaryTransparent: '#007AFF33', // Primary color with ~20% opacity
    accent: accentColor,
    
    // Status colors
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30',
    info: '#5AC8FA',
    
    // Tab bar
    tabIconDefault: '#999999',
    tabIconSelected: primaryColor,
    tabBackground: '#FFFFFF',
    tabBorder: '#EEEEEE',
    
    // Badges
    badgeBackground: '#FF3B30',
    badgeText: '#FFFFFF',
    
    // Tags
    tagHot: '#FF3B30',
    tagNew: '#FF9500',
    tagDiscount: '#FF3B30',
    
    // Buttons
    buttonPrimary: primaryColor,
    buttonSecondary: '#FFFFFF',
    buttonDisabled: '#CCCCCC',
    buttonText: '#FFFFFF',
  },
  dark: {
    // Text colors
    text: '#FFFFFF',
    textSecondary: '#AAAAAA',
    textTertiary: '#888888',
    textMuted: '#888888', // Using textTertiary as a base for muted
    textMutedOpac: '#AAAAAA80', // textSecondary with 50% opacity
    
    // Background colors
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    backgroundSoft: '#2C2C2E', // A common dark mode soft background
    card: '#242424',
    imageBackground: '#303030',
    
    // UI elements
    border: '#333333',
    separator: '#333333',
    
    // Brand colors
    primary: primaryColor,
    secondary: secondaryColor,
    primaryTransparent: '#007AFF33', // Primary color with ~20% opacity
    accent: accentColor,
    
    // Status colors
    success: '#30D158',
    warning: '#FF9F0A',
    error: '#FF453A',
    info: '#64D2FF',
    
    // Tab bar
    tabIconDefault: '#888888',
    tabIconSelected: primaryColor,
    tabBackground: '#121212',
    tabBorder: '#333333',
    
    // Badges
    badgeBackground: '#FF453A',
    badgeText: '#FFFFFF',
    
    // Tags
    tagHot: '#FF453A',
    tagNew: '#FF9F0A',
    tagDiscount: '#FF453A',
    
    // Buttons
    buttonPrimary: primaryColor,
    buttonSecondary: '#333333',
    buttonDisabled: '#444444',
    buttonText: '#FFFFFF',
  },
};
