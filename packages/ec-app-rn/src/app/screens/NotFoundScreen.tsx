import React from 'react';
import { View, Text, StyleSheet, Button } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation'; // Adjust path as needed

type NotFoundScreenNavigationProp = StackNavigationProp<RootStackParamList, 'NotFound'>;

const NotFoundScreen: React.FC = () => {
  const navigation = useNavigation<NotFoundScreenNavigationProp>();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>404 - Screen Not Found</Text>
      <Button title="Go to Home" onPress={() => navigation.replace('Main')} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
});

export default NotFoundScreen;
