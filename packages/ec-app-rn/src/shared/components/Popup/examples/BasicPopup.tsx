import React from 'react';
import { View, Button, Text, StyleSheet } from 'react-native';
import Popup from '../Popup';
import { usePopupState } from '../usePopupState';

const BasicPopupExample = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Button title="显示底部弹出框" onPress={showPopup} />
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        title="底部弹出框"
        position="bottom"
        animationType="slide"
      >
        <View style={styles.content}>
          <Text style={styles.text}>
            这是一个从底部弹出的弹出框。常用于显示操作菜单、筛选条件或其他需要用户选择的内容。
          </Text>
          <Button title="关闭" onPress={hidePopup} />
        </View>
      </Popup>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    padding: 16,
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
});

export default BasicPopupExample;
