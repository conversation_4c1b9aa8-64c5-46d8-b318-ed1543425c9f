import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import Popup from '../Popup';
import { usePopupState } from '../usePopupState';
import Icon from 'react-native-vector-icons/Ionicons';

const ActionSheetPopupExample = () => {
  const { isVisible, showPopup, hidePopup } = usePopupState();

  const actions = [
    { icon: 'share-social', label: '分享', color: '#2196F3' },
    { icon: 'heart', label: '收藏', color: '#F44336' },
    { icon: 'download', label: '保存', color: '#4CAF50' },
    { icon: 'copy', label: '复制链接', color: '#FF9800' },
    { icon: 'alert-circle', label: '举报', color: '#9C27B0' },
  ];

  const handleAction = (action: string) => {
    console.log(`Selected action: ${action}`);
    hidePopup();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.demoButton} 
        onPress={showPopup}
      >
        <Text style={styles.demoButtonText}>显示操作菜单</Text>
      </TouchableOpacity>
      
      <Popup
        visible={isVisible}
        onClose={hidePopup}
        position="bottom"
        animationType="slide"
        customHeader={
          <View style={styles.customHeader}>
            <View style={styles.dragHandle} />
            <Text style={styles.headerTitle}>选择操作</Text>
          </View>
        }
      >
        <ScrollView style={styles.actionList}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.actionItem}
              onPress={() => handleAction(action.label)}
            >
              <View style={[styles.iconContainer, { backgroundColor: action.color }]}>
                <Icon name={action.icon} size={22} color="#FFF" />
              </View>
              <Text style={styles.actionLabel}>{action.label}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={hidePopup}
        >
          <Text style={styles.cancelButtonText}>取消</Text>
        </TouchableOpacity>
      </Popup>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  demoButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  demoButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
  customHeader: {
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dragHandle: {
    width: 40,
    height: 5,
    backgroundColor: '#E0E0E0',
    borderRadius: 2.5,
    marginBottom: 10,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  actionList: {
    maxHeight: 300,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionLabel: {
    fontSize: 16,
    color: '#333',
  },
  cancelButton: {
    marginTop: 10,
    paddingVertical: 16,
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
});

export default ActionSheetPopupExample;
