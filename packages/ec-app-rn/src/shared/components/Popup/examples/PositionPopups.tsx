import React from 'react';
import { View, Button, Text, StyleSheet } from 'react-native';
import Popup from '../Popup';
import { usePopupState } from '../usePopupState';

const PositionPopupsExample = () => {
  const bottomPopup = usePopupState();
  const topPopup = usePopupState();
  const centerPopup = usePopupState();

  return (
    <View style={styles.container}>
      <View style={styles.buttonRow}>
        <Button title="底部弹出" onPress={bottomPopup.showPopup} />
        <View style={styles.buttonSpacer} />
        <Button title="顶部弹出" onPress={topPopup.showPopup} />
        <View style={styles.buttonSpacer} />
        <Button title="中心弹出" onPress={centerPopup.showPopup} />
      </View>
      
      {/* 底部弹出框 */}
      <Popup
        visible={bottomPopup.isVisible}
        onClose={bottomPopup.hidePopup}
        title="底部弹出框"
        position="bottom"
        animationType="slide"
      >
        <View style={styles.content}>
          <Text style={styles.text}>
            这是一个从底部弹出的弹出框。常用于显示操作菜单、筛选条件或其他需要用户选择的内容。
          </Text>
          <Button title="关闭" onPress={bottomPopup.hidePopup} />
        </View>
      </Popup>
      
      {/* 顶部弹出框 */}
      <Popup
        visible={topPopup.isVisible}
        onClose={topPopup.hidePopup}
        title="顶部弹出框"
        position="top"
        animationType="slide"
      >
        <View style={styles.content}>
          <Text style={styles.text}>
            这是一个从顶部弹出的弹出框。适合显示通知、警告或临时信息。
          </Text>
          <Button title="关闭" onPress={topPopup.hidePopup} />
        </View>
      </Popup>
      
      {/* 中心弹出框 */}
      <Popup
        visible={centerPopup.isVisible}
        onClose={centerPopup.hidePopup}
        title="中心弹出框"
        position="center"
        animationType="fade"
      >
        <View style={styles.content}>
          <Text style={styles.text}>
            这是一个从中心弹出的弹出框。适合显示重要信息或需要用户确认的内容。
          </Text>
          <Button title="关闭" onPress={centerPopup.hidePopup} />
        </View>
      </Popup>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  buttonSpacer: {
    width: 10,
  },
  content: {
    padding: 16,
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
});

export default PositionPopupsExample;
