import React, { createContext, useContext, useState, ReactNode } from 'react';
import Popup, { PopupProps } from './Popup';

type PopupOptions = Omit<PopupProps, 'visible' | 'onClose'>;

interface PopupContextType {
  showPopup: (options: PopupOptions) => void;
  hidePopup: () => void;
}

const PopupContext = createContext<PopupContextType | undefined>(undefined);

export const usePopup = () => {
  const context = useContext(PopupContext);
  if (!context) {
    throw new Error('usePopup must be used within a PopupProvider');
  }
  return context;
};

interface PopupProviderProps {
  children: ReactNode;
}

export const PopupProvider: React.FC<PopupProviderProps> = ({ children }) => {
  const [popupVisible, setPopupVisible] = useState(false);
  const [popupOptions, setPopupOptions] = useState<PopupOptions>({});

  const showPopup = (options: PopupOptions) => {
    setPopupOptions(options);
    setPopupVisible(true);
  };

  const hidePopup = () => {
    setPopupVisible(false);
  };

  return (
    <PopupContext.Provider value={{ showPopup, hidePopup }}>
      {children}
      <Popup
        visible={popupVisible}
        onClose={hidePopup}
        {...popupOptions}
      />
    </PopupContext.Provider>
  );
};
