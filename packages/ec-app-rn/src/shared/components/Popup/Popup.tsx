import React, { useEffect, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform,
  Animated,
  StyleProp,
  ViewStyle,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

// Get screen dimensions for animations
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export type PopupProps = {
  visible: boolean;
  onClose?: () => void;
  title?: string;
  children: React.ReactNode;
  position?: 'bottom' | 'top' | 'center';
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  animationType?: 'slide' | 'fade';
  height?: number;
  width?: number | "auto" | `${number}%`;
  borderRadius?: number;
  backgroundColor?: string;
  customHeader?: React.ReactNode;
  customFooter?: React.ReactNode;
  showOverlay?: boolean;
  overlayColor?: string;
  overlayOpacity?: number;
  style?: StyleProp<ViewStyle>;
};

const Popup: React.FC<PopupProps> = ({
  visible,
  title,
  onClose,
  children,
  position = 'bottom',
  showCloseButton = true,
  closeOnBackdropPress = true,
  animationType = 'slide',
  height: propHeight,
  width = "100%",
  borderRadius = 16,
  backgroundColor = '#FFFFFF',
  customHeader,
  customFooter,
  showOverlay = true,
  overlayColor = '#000000',
  overlayOpacity = 0.5,
  style,
}) => {
  // Use provided height or fallback to screen height
  const actualHeight: number = propHeight !== undefined ? propHeight : screenHeight / 2;

  // Initialize animation values based on position
  const translateY = useRef(new Animated.Value(position === 'bottom' ? actualHeight : position === 'top' ? -actualHeight : 0)).current;
  const scale = useRef(new Animated.Value(position === 'center' ? 0.9 : 1)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Only run animations if the component is mounted
    let isMounted = true;

    if (visible) {
      if (animationType === 'slide') {
        Animated.spring(translateY, {
          toValue: 0,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }).start();
      } else if (animationType === 'fade') {
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      } else if (position === 'center') {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.spring(scale, {
            toValue: 1,
            friction: 8,
            tension: 40,
            useNativeDriver: true,
          }),
        ]).start();
      }

      // Haptic feedback when popup opens
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    } else if (isMounted) { // Only animate if still mounted
      if (animationType === 'slide') {
        // Calculate target value based on position
        const targetValue = position === 'bottom' ? actualHeight : position === 'top' ? -actualHeight : 0;

        Animated.timing(translateY, {
          toValue: targetValue,
          duration: 250,
          useNativeDriver: true,
        }).start();
      } else if (animationType === 'fade') {
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      } else if (position === 'center') {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.9,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }

    // Cleanup function to prevent animation updates on unmounted component
    return () => {
      isMounted = false;
    };
  }, [visible, translateY, opacity, scale, position, animationType, actualHeight]);

  const handleBackdropPress = () => {
    if (closeOnBackdropPress && onClose) {
      onClose();

      // Haptic feedback on close
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Backdrop */}
        {showOverlay && (
          <TouchableWithoutFeedback onPress={handleBackdropPress}>
            <Animated.View
              style={[
                styles.backdrop,
                {
                  backgroundColor: overlayColor,
                  opacity: opacity,
                },
              ]}
            />
          </TouchableWithoutFeedback>
        )}
        
        {/* Popup Content */}
        <Animated.View
          style={[
            styles.popup,
            {
              height: actualHeight,
              borderRadius: borderRadius,
              backgroundColor: backgroundColor,
            },
            { width },
            position === 'bottom' ? styles.bottomPosition :
            position === 'top' ? styles.topPosition :
            styles.centerPosition,
            position === 'center' && {
              transform: [
                { scale: scale },
              ],
              opacity: opacity,
            },
            animationType === 'slide' && {
              transform: [
                { translateY: translateY },
              ],
            },
            animationType === 'fade' && {
              opacity: opacity,
            },
            style,
          ]}
        >
          {/* Header */}
          {customHeader ? (
            customHeader
          ) : (
            <View style={styles.header}>
              {title && <Text style={styles.title}>{title}</Text>}
              {showCloseButton && onClose && (
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onClose}
                  hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                >
                  <Icon name="close" size={24} color="#999" />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          {/* Content */}
          <View style={styles.content}>
            {children}
          </View>
          
          {/* Footer */}
          {customFooter && (
            <View style={styles.footer}>
              {customFooter}
            </View>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  popup: {
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    maxHeight: '80%',
  },
  bottomPosition: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  topPosition: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  centerPosition: {
    position: 'absolute',
    left: 20,
    right: 20,
    alignSelf: 'center',
    top: '50%',
    transform: [{ translateY: -100 }], // Adjust based on content
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#EEEEEE',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
});

export default Popup;
