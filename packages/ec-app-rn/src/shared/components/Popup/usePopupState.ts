import { useState } from 'react';

/**
 * A custom hook for managing popup state in components
 * @returns Popup state management utilities
 */
export const usePopupState = () => {
  const [isVisible, setIsVisible] = useState(false);

  const showPopup = () => setIsVisible(true);
  const hidePopup = () => setIsVisible(false);
  const togglePopup = () => setIsVisible(prev => !prev);

  return {
    isVisible,
    showPopup,
    hidePopup,
    togglePopup,
  };
};
