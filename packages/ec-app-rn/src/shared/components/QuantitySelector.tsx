import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ViewStyle,
  TextStyle,
  Animated,
  Easing,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

interface QuantitySelectorProps {
  initialValue?: number;
  minValue?: number;
  maxValue?: number;
  step?: number;
  onChange?: (value: number) => void;
  containerStyle?: ViewStyle;
  buttonStyle?: ViewStyle;
  textStyle?: TextStyle;
  size?: 'small' | 'medium' | 'large';
  theme?: 'default' | 'primary' | 'outline';
  disabled?: boolean;
  allowManualInput?: boolean;
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  initialValue = 1,
  minValue = 1,
  maxValue = 99,
  step = 1,
  onChange,
  containerStyle,
  buttonStyle,
  textStyle,
  size = 'medium',
  theme = 'default',
  disabled = false,
  allowManualInput = true,
}) => {
  const [quantity, setQuantity] = useState<number>(initialValue);
  const [inputValue, setInputValue] = useState<string>(initialValue.toString());
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const scaleAnim = useState(new Animated.Value(1))[0];

  useEffect(() => {
    setQuantity(initialValue);
    setInputValue(initialValue.toString());
  }, [initialValue]);

  const animateButton = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleIncrement = () => {
    if (disabled || quantity >= maxValue) return;
    
    const newValue = Math.min(quantity + step, maxValue);
    setQuantity(newValue);
    setInputValue(newValue.toString());
    onChange?.(newValue);
    animateButton();
  };

  const handleDecrement = () => {
    if (disabled || quantity <= minValue) return;
    
    const newValue = Math.max(quantity - step, minValue);
    setQuantity(newValue);
    setInputValue(newValue.toString());
    onChange?.(newValue);
    animateButton();
  };

  const handleInputChange = (text: string) => {
    // Allow only numbers
    if (/^\d*$/.test(text)) {
      setInputValue(text);
    }
  };

  const handleInputBlur = () => {
    setIsEditing(false);
    let newValue = parseInt(inputValue, 10);
    
    // Handle empty or invalid input
    if (isNaN(newValue)) {
      newValue = minValue;
    }
    
    // Ensure value is within bounds
    newValue = Math.max(minValue, Math.min(maxValue, newValue));
    
    setQuantity(newValue);
    setInputValue(newValue.toString());
    onChange?.(newValue);
  };

  // Determine sizes based on the size prop
  const getSizes = () => {
    switch (size) {
      case 'small':
        return {
          container: { height: 28 },
          button: { width: 28, height: 28 },
          icon: 16,
          text: { fontSize: 12 },
        };
      case 'large':
        return {
          container: { height: 40 },
          button: { width: 40, height: 40 },
          icon: 24,
          text: { fontSize: 18 },
        };
      default: // medium
        return {
          container: { height: 36 },
          button: { width: 36, height: 36 },
          icon: 20,
          text: { fontSize: 16 },
        };
    }
  };

  // Determine theme styles
  const getThemeStyles = () => {
    switch (theme) {
      case 'primary':
        return {
          container: { backgroundColor: '#F2F2F6' },
          button: { backgroundColor: '#FF4D4F' },
          buttonText: { color: '#FFFFFF' },
          disabledButton: { backgroundColor: '#FFCCCB' },
          text: { color: '#333333' },
        };
      case 'outline':
        return {
          container: { backgroundColor: '#FFFFFF', borderWidth: 1, borderColor: '#E0E0E0' },
          button: { backgroundColor: '#FFFFFF', borderWidth: 1, borderColor: '#E0E0E0' },
          buttonText: { color: '#333333' },
          disabledButton: { backgroundColor: '#F5F5F5', borderColor: '#E0E0E0' },
          text: { color: '#333333' },
        };
      default: // default
        return {
          container: { backgroundColor: '#F2F2F6' },
          button: { backgroundColor: '#F2F2F6' },
          buttonText: { color: '#333333' },
          disabledButton: { backgroundColor: '#F5F5F5' },
          text: { color: '#333333' },
        };
    }
  };

  const sizes = getSizes();
  const themeStyles = getThemeStyles();
  
  const isDecrementDisabled = disabled || quantity <= minValue;
  const isIncrementDisabled = disabled || quantity >= maxValue;

  return (
    <View
      style={[
        styles.container,
        themeStyles.container,
        sizes.container,
        containerStyle,
      ]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          themeStyles.button,
          sizes.button,
          isDecrementDisabled && styles.disabledButton,
          isDecrementDisabled && themeStyles.disabledButton,
          buttonStyle,
        ]}
        onPress={handleDecrement}
        disabled={isDecrementDisabled}
        activeOpacity={0.7}
      >
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Icon
            name="remove"
            size={sizes.icon}
            color={isDecrementDisabled ? '#CCCCCC' : themeStyles.buttonText.color}
          />
        </Animated.View>
      </TouchableOpacity>

      {allowManualInput ? (
        <TextInput
          style={[
            styles.input,
            themeStyles.text,
            sizes.text,
            textStyle,
            { minWidth: sizes.button.width * 0.8 },
          ]}
          value={inputValue}
          onChangeText={handleInputChange}
          onFocus={() => setIsEditing(true)}
          onBlur={handleInputBlur}
          keyboardType="numeric"
          selectTextOnFocus
          editable={!disabled}
          maxLength={3}
          textAlign="center"
        />
      ) : (
        <Text
          style={[
            styles.text,
            themeStyles.text,
            sizes.text,
            textStyle,
            { minWidth: sizes.button.width * 0.8 },
          ]}
        >
          {quantity}
        </Text>
      )}

      <TouchableOpacity
        style={[
          styles.button,
          themeStyles.button,
          sizes.button,
          isIncrementDisabled && styles.disabledButton,
          isIncrementDisabled && themeStyles.disabledButton,
          buttonStyle,
        ]}
        onPress={handleIncrement}
        disabled={isIncrementDisabled}
        activeOpacity={0.7}
      >
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Icon
            name="add"
            size={sizes.icon}
            color={isIncrementDisabled ? '#CCCCCC' : themeStyles.buttonText.color}
          />
        </Animated.View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 4,
    overflow: 'hidden',
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
  },
  disabledButton: {
    opacity: 0.7,
  },
  input: {
    textAlign: 'center',
    padding: 0,
    margin: 0,
  },
  text: {
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default QuantitySelector;
