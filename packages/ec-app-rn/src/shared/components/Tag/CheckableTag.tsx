import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface CheckableTagProps {
  /**
   * Text content of the tag
   */
  text: string;
  
  /**
   * Whether the tag is checked/selected
   */
  checked: boolean;
  
  /**
   * Callback when tag is toggled
   */
  onChange: (checked: boolean) => void;
  
  /**
   * Whether the tag is disabled
   */
  disabled?: boolean;
  
  /**
   * Custom style for the tag container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the tag text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom colors for the tag
   */
  colors?: {
    /**
     * Background color when checked
     */
    checkedBg?: string;
    
    /**
     * Text color when checked
     */
    checkedText?: string;
    
    /**
     * Background color when unchecked
     */
    uncheckedBg?: string;
    
    /**
     * Text color when unchecked
     */
    uncheckedText?: string;
  };
}

const CheckableTag: React.FC<CheckableTagProps> = ({
  text,
  checked,
  onChange,
  disabled = false,
  style,
  textStyle,
  colors = {},
}) => {
  const {
    checkedBg = '#1890FF',
    checkedText = '#FFFFFF',
    uncheckedBg = '#F5F5F5',
    uncheckedText = '#666666',
  } = colors;
  
  const handlePress = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: checked ? checkedBg : uncheckedBg,
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Text
        style={[
          styles.text,
          { color: checked ? checkedText : uncheckedText },
          textStyle,
        ]}
      >
        {text}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default CheckableTag;
