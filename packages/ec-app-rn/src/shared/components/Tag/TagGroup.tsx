import React from 'react';
import { View, StyleSheet, StyleProp, ViewStyle } from 'react-native';

interface TagGroupProps {
  /**
   * Child tag components
   */
  children: React.ReactNode;
  
  /**
   * Direction of the tag layout
   */
  direction?: 'horizontal' | 'vertical' | 'wrap';
  
  /**
   * Space between tags
   */
  spacing?: number;
  
  /**
   * Custom style for the tag group container
   */
  style?: StyleProp<ViewStyle>;
}

const TagGroup: React.FC<TagGroupProps> = ({
  children,
  direction = 'wrap',
  spacing = 8,
  style,
}) => {
  const getDirectionStyle = () => {
    switch (direction) {
      case 'horizontal':
        return {
          flexDirection: 'row' as const,
          flexWrap: 'nowrap' as const,
        };
      case 'vertical':
        return {
          flexDirection: 'column' as const,
          flexWrap: 'nowrap' as const,
        };
      case 'wrap':
      default:
        return {
          flexDirection: 'row' as const,
          flexWrap: 'wrap' as const,
        };
    }
  };

  const { flexDirection, flexWrap } = getDirectionStyle();

  // Instead of trying to modify children directly, we'll wrap each child in a View with margin
  const wrappedChildren = React.Children.map(children, (child, index) => {
    if (!React.isValidElement(child)) return child;
    
    return (
      <View
        key={index}
        style={{
          marginRight: direction !== 'vertical' ? spacing : 0,
          marginBottom: direction !== 'horizontal' ? spacing : 0,
        }}
      >
        {child}
      </View>
    );
  });

  return (
    <View
      style={[
        styles.container,
        { flexDirection, flexWrap },
        style,
      ]}
    >
      {wrappedChildren}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default TagGroup;
