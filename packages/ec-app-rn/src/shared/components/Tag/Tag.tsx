import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export type TagType = 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
export type TagSize = 'small' | 'medium' | 'large';
export type TagShape = 'square' | 'rounded' | 'pill';

export interface TagProps {
  /**
   * Text content of the tag
   */
  text: string;
  
  /**
   * Type/color variant of the tag
   */
  type?: TagType;
  
  /**
   * Size of the tag
   */
  size?: TagSize;
  
  /**
   * Shape of the tag
   */
  shape?: TagShape;
  
  /**
   * Whether the tag is outlined (border only) or filled
   */
  outlined?: boolean;
  
  /**
   * Whether the tag is closable/removable
   */
  closable?: boolean;
  
  /**
   * Callback when close button is pressed
   */
  onClose?: () => void;
  
  /**
   * Callback when tag is pressed
   */
  onPress?: () => void;
  
  /**
   * Optional icon name to display before text
   */
  iconName?: string;
  
  /**
   * Whether the tag is disabled
   */
  disabled?: boolean;
  
  /**
   * Custom style for the tag container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the tag text
   */
  textStyle?: StyleProp<TextStyle>;
}

const Tag: React.FC<TagProps> = ({
  text,
  type = 'default',
  size = 'medium',
  shape = 'rounded',
  outlined = false,
  closable = false,
  onClose,
  onPress,
  iconName,
  disabled = false,
  style,
  textStyle,
}) => {
  // Get background and text colors based on type and outlined state
  const getColors = () => {
    const colors = {
      default: { bg: '#F5F5F5', text: '#666666', border: '#E0E0E0' },
      primary: { bg: '#E6F7FF', text: '#1890FF', border: '#91D5FF' },
      success: { bg: '#F6FFED', text: '#52C41A', border: '#B7EB8F' },
      warning: { bg: '#FFF7E6', text: '#FA8C16', border: '#FFD591' },
      danger: { bg: '#FFF1F0', text: '#FF4D4F', border: '#FFA39E' },
      info: { bg: '#E6FFFB', text: '#13C2C2', border: '#87E8DE' },
    };
    
    const colorSet = colors[type];
    
    if (outlined) {
      return {
        backgroundColor: 'transparent',
        textColor: colorSet.text,
        borderColor: colorSet.border,
      };
    } else {
      return {
        backgroundColor: colorSet.bg,
        textColor: colorSet.text,
        borderColor: 'transparent',
      };
    }
  };
  
  // Get size-based styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 2,
          paddingHorizontal: 6,
          fontSize: 12,
          iconSize: 12,
          closeIconSize: 14,
        };
      case 'large':
        return {
          paddingVertical: 8,
          paddingHorizontal: 16,
          fontSize: 16,
          iconSize: 18,
          closeIconSize: 20,
        };
      case 'medium':
      default:
        return {
          paddingVertical: 4,
          paddingHorizontal: 10,
          fontSize: 14,
          iconSize: 14,
          closeIconSize: 16,
        };
    }
  };
  
  // Get border radius based on shape
  const getBorderRadius = () => {
    switch (shape) {
      case 'square':
        return 0;
      case 'pill':
        return 100;
      case 'rounded':
      default:
        return 4;
    }
  };
  
  const { backgroundColor, textColor, borderColor } = getColors();
  const { paddingVertical, paddingHorizontal, fontSize, iconSize, closeIconSize } = getSizeStyles();
  const borderRadius = getBorderRadius();
  
  // Opacity for disabled state
  const opacity = disabled ? 0.5 : 1;
  
  const TagComponent = onPress ? TouchableOpacity : View;
  
  return (
    <TagComponent
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
          borderRadius,
          paddingVertical,
          paddingHorizontal,
          opacity,
        },
        style,
      ]}
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {iconName && (
        <Icon
          name={iconName}
          size={iconSize}
          color={textColor}
          style={styles.icon}
        />
      )}
      
      <Text
        style={[
          styles.text,
          { color: textColor, fontSize },
          textStyle,
        ]}
        numberOfLines={1}
      >
        {text}
      </Text>
      
      {closable && onClose && (
        <TouchableOpacity
          onPress={disabled ? undefined : onClose}
          hitSlop={{ top: 6, right: 6, bottom: 6, left: 6 }}
          style={styles.closeButton}
          disabled={disabled}
        >
          <Icon
            name="close-circle"
            size={closeIconSize}
            color={textColor}
          />
        </TouchableOpacity>
      )}
    </TagComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    alignSelf: 'flex-start',
  },
  text: {
    fontWeight: '500',
  },
  icon: {
    marginRight: 4,
  },
  closeButton: {
    marginLeft: 4,
  },
});

export default Tag;
