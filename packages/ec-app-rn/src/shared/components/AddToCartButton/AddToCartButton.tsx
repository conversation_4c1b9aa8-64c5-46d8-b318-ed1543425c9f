import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  StyleProp,
  ActivityIndicator,
  Animated,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export interface AddToCartButtonProps {
  /**
   * Text to display on the button
   */
  text?: string;
  
  /**
   * Icon name from Ionicons
   */
  icon?: string;
  
  /**
   * Size of the button
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Type of the button
   */
  type?: 'primary' | 'outline' | 'ghost';
  
  /**
   * Shape of the button
   */
  shape?: 'square' | 'round' | 'circle';
  
  /**
   * Whether the button is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether the button is in loading state
   */
  loading?: boolean;
  
  /**
   * Whether to show quantity selector
   */
  showQuantity?: boolean;
  
  /**
   * Initial quantity
   */
  initialQuantity?: number;
  
  /**
   * Maximum quantity allowed
   */
  maxQuantity?: number;
  
  /**
   * Callback when button is pressed
   */
  onPress?: () => void;
  
  /**
   * Callback when quantity changes
   */
  onQuantityChange?: (quantity: number) => void;
  
  /**
   * Custom style for the button container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the button text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for the icon
   */
  iconStyle?: StyleProp<TextStyle>;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  text = '加入购物车',
  icon = 'cart',
  size = 'medium',
  type = 'primary',
  shape = 'round',
  disabled = false,
  loading = false,
  showQuantity = false,
  initialQuantity = 1,
  maxQuantity = 99,
  onPress,
  onQuantityChange,
  style,
  textStyle,
  iconStyle,
}) => {
  const [quantity, setQuantity] = useState(initialQuantity);
  const [scaleAnim] = useState(new Animated.Value(1));
  
  // Handle button press with animation
  const handlePress = () => {
    if (disabled || loading) return;
    
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    if (onPress) onPress();
  };
  
  // Handle quantity change
  const handleIncrement = () => {
    if (quantity < maxQuantity) {
      const newQuantity = quantity + 1;
      setQuantity(newQuantity);
      if (onQuantityChange) onQuantityChange(newQuantity);
    }
  };
  
  const handleDecrement = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      if (onQuantityChange) onQuantityChange(newQuantity);
    }
  };
  
  // Get size-based styles
  const getSizeStyles = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          height: 32,
          paddingHorizontal: 12,
        };
      case 'large':
        return {
          height: 48,
          paddingHorizontal: 24,
        };
      case 'medium':
      default:
        return {
          height: 40,
          paddingHorizontal: 16,
        };
    }
  };
  
  // Get type-based styles
  const getTypeStyles = (): { container: ViewStyle; text: TextStyle } => {
    switch (type) {
      case 'outline':
        return {
          container: {
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: '#FF4D4F',
          },
          text: {
            color: '#FF4D4F',
          },
        };
      case 'ghost':
        return {
          container: {
            backgroundColor: 'transparent',
          },
          text: {
            color: '#FF4D4F',
          },
        };
      case 'primary':
      default:
        return {
          container: {
            backgroundColor: '#FF4D4F',
          },
          text: {
            color: '#fff',
          },
        };
    }
  };
  
  // Get shape-based styles
  const getShapeStyles = (): ViewStyle => {
    switch (shape) {
      case 'square':
        return {
          borderRadius: 0,
        };
      case 'circle':
        return {
          borderRadius: 50,
          aspectRatio: 1,
          paddingHorizontal: 0,
        };
      case 'round':
      default:
        return {
          borderRadius: 20,
        };
    }
  };
  
  // Get font size based on button size
  const getFontSize = (): number => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 16;
      case 'medium':
      default:
        return 14;
    }
  };
  
  // Get icon size based on button size
  const getIconSize = (): number => {
    switch (size) {
      case 'small':
        return 14;
      case 'large':
        return 22;
      case 'medium':
      default:
        return 18;
    }
  };
  
  const sizeStyles = getSizeStyles();
  const typeStyles = getTypeStyles();
  const shapeStyles = getShapeStyles();
  const fontSize = getFontSize();
  const iconSize = getIconSize();
  
  // Render quantity selector
  const renderQuantitySelector = () => {
    if (!showQuantity) return null;
    
    return (
      <View style={styles.quantityContainer}>
        <TouchableOpacity
          style={[styles.quantityButton, { opacity: quantity <= 1 ? 0.5 : 1 }]}
          onPress={handleDecrement}
          disabled={quantity <= 1}
        >
          <Icon name="remove" size={iconSize} color="#FF4D4F" />
        </TouchableOpacity>
        
        <Text style={styles.quantityText}>{quantity}</Text>
        
        <TouchableOpacity
          style={[styles.quantityButton, { opacity: quantity >= maxQuantity ? 0.5 : 1 }]}
          onPress={handleIncrement}
          disabled={quantity >= maxQuantity}
        >
          <Icon name="add" size={iconSize} color="#FF4D4F" />
        </TouchableOpacity>
      </View>
    );
  };
  
  return (
    <View style={styles.wrapper}>
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[
            styles.container,
            sizeStyles,
            typeStyles.container,
            shapeStyles,
            disabled && styles.disabledContainer,
            style,
          ]}
          onPress={handlePress}
          disabled={disabled || loading}
          activeOpacity={0.8}
        >
          {loading ? (
            <ActivityIndicator
              size="small"
              color={type === 'primary' ? '#fff' : '#FF4D4F'}
            />
          ) : (
            <>
              {icon && shape !== 'circle' && (
                <Icon
                  name={icon}
                  size={iconSize}
                  color={typeStyles.text.color}
                  style={[styles.icon, iconStyle]}
                />
              )}
              
              {(text && shape !== 'circle') && (
                <Text
                  style={[
                    styles.text,
                    { fontSize },
                    typeStyles.text,
                    disabled && styles.disabledText,
                    textStyle,
                  ]}
                >
                  {text}
                </Text>
              )}
              
              {shape === 'circle' && (
                <Icon
                  name={icon}
                  size={iconSize}
                  color={typeStyles.text.color}
                  style={iconStyle}
                />
              )}
            </>
          )}
        </TouchableOpacity>
      </Animated.View>
      
      {renderQuantitySelector()}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledContainer: {
    backgroundColor: '#f5f5f5',
    borderColor: '#d9d9d9',
  },
  text: {
    fontWeight: '500',
  },
  disabledText: {
    color: '#bfbfbf',
  },
  icon: {
    marginRight: 6,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    borderWidth: 1,
    borderColor: '#e8e8e8',
    borderRadius: 4,
  },
  quantityButton: {
    width: 28,
    height: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    minWidth: 24,
    textAlign: 'center',
    fontSize: 14,
  },
});

export default AddToCartButton;
