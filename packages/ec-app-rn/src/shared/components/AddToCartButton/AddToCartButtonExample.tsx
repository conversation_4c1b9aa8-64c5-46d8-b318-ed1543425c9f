import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Text, Alert } from 'react-native';
import AddToCartButton from './AddToCartButton';

const AddToCartButtonExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert('成功', `已添加商品到购物车！`);
    }, 1000);
  };

  const handleQuantityChange = (newQuantity: number) => {
    setQuantity(newQuantity);
    console.log('Quantity changed:', newQuantity);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.sectionTitle}>基础样式</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton onPress={handleAddToCart} />
        </View>

        <Text style={styles.sectionTitle}>不同尺寸</Text>
        <View style={styles.row}>
          <AddToCartButton size="small" text="小" />
          <AddToCartButton size="medium" text="中" />
          <AddToCartButton size="large" text="大" />
        </View>

        <Text style={styles.sectionTitle}>不同类型</Text>
        <View style={styles.column}>
          <AddToCartButton type="primary" text="主要按钮" />
          <View style={styles.spacer} />
          <AddToCartButton type="outline" text="描边按钮" />
          <View style={styles.spacer} />
          <AddToCartButton type="ghost" text="幽灵按钮" />
        </View>

        <Text style={styles.sectionTitle}>不同形状</Text>
        <View style={styles.row}>
          <AddToCartButton shape="square" text="方形" />
          <AddToCartButton shape="round" text="圆角" />
          <AddToCartButton shape="circle" />
        </View>

        <Text style={styles.sectionTitle}>禁用状态</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton disabled />
        </View>

        <Text style={styles.sectionTitle}>加载状态</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton loading />
        </View>

        <Text style={styles.sectionTitle}>数量选择器</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton 
            showQuantity 
            onQuantityChange={handleQuantityChange}
          />
        </View>

        <Text style={styles.sectionTitle}>交互示例</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton 
            loading={loading}
            onPress={handleAddToCart}
            text={loading ? "添加中..." : "添加到购物车"}
          />
        </View>

        <Text style={styles.sectionTitle}>自定义图标</Text>
        <View style={styles.row}>
          <AddToCartButton icon="heart" text="收藏" />
          <AddToCartButton icon="share-social" text="分享" />
          <AddToCartButton icon="bag-check" text="购买" />
        </View>

        <Text style={styles.sectionTitle}>自定义样式</Text>
        <View style={styles.buttonContainer}>
          <AddToCartButton 
            style={styles.customButton}
            textStyle={styles.customButtonText}
            text="自定义样式"
          />
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>使用说明</Text>
          <Text style={styles.infoText}>1. 基础用法：{'<AddToCartButton />'}</Text>
          <Text style={styles.infoText}>2. 设置尺寸：{'<AddToCartButton size="small|medium|large" />'}</Text>
          <Text style={styles.infoText}>3. 设置类型：{'<AddToCartButton type="primary|outline|ghost" />'}</Text>
          <Text style={styles.infoText}>4. 设置形状：{'<AddToCartButton shape="square|round|circle" />'}</Text>
          <Text style={styles.infoText}>5. 禁用状态：{'<AddToCartButton disabled={true} />'}</Text>
          <Text style={styles.infoText}>6. 加载状态：{'<AddToCartButton loading={true} />'}</Text>
          <Text style={styles.infoText}>7. 数量选择器：{'<AddToCartButton showQuantity={true} />'}</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 12,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  column: {
    marginBottom: 8,
  },
  spacer: {
    height: 12,
  },
  customButton: {
    backgroundColor: '#722ed1',
    borderRadius: 8,
  },
  customButtonText: {
    fontWeight: 'bold',
  },
  infoBox: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#f0f5ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#1890ff',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#666',
  },
});

export default AddToCartButtonExample;
