import React, { useState } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Rating from './Rating';

const RatingExample = () => {
  const [userRating, setUserRating] = useState(0);
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>只读评分</Text>
      <View style={styles.exampleContainer}>
        <Rating value={4.5} showRating={true} totalRatings={1234} />
      </View>
      
      <Text style={styles.title}>自定义颜色和大小</Text>
      <View style={styles.exampleContainer}>
        <Rating 
          value={3.5} 
          size={30} 
          activeColor="#E94E77" 
          showRating={true} 
          ratingTextFormat="评分: {rating}" 
        />
      </View>
      
      <Text style={styles.title}>整数评分</Text>
      <View style={styles.exampleContainer}>
        <Rating 
          value={4} 
          allowHalf={false} 
          showRating={true} 
        />
      </View>
      
      <Text style={styles.title}>用户交互评分</Text>
      <View style={styles.exampleContainer}>
        <Rating 
          value={userRating} 
          readonly={false} 
          onRatingChange={setUserRating} 
          showRating={true} 
          ratingTextFormat="您的评分: {rating}" 
        />
      </View>
      
      <Text style={styles.title}>自定义图标</Text>
      <View style={styles.exampleContainer}>
        <Rating 
          value={3.5} 
          iconName="heart" 
          halfIconName="heart-half" 
          emptyIconName="heart-outline" 
          activeColor="#FF4D4F" 
          size={25} 
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  exampleContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
});

export default RatingExample;
