import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ViewStyle,
  StyleProp,
  TextStyle,
  Text,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export interface RatingProps {
  /**
   * The current rating value (0-5)
   */
  value: number;
  
  /**
   * Maximum rating value (default: 5)
   */
  maxRating?: number;
  
  /**
   * Size of each star (default: 20)
   */
  size?: number;
  
  /**
   * Color of filled stars (default: '#FFB800')
   */
  activeColor?: string;
  
  /**
   * Color of empty stars (default: '#DDDDDD')
   */
  inactiveColor?: string;
  
  /**
   * Icon to use for rating (default: 'star')
   */
  iconName?: string;
  
  /**
   * Icon to use for half ratings (default: 'star-half')
   */
  halfIconName?: string;
  
  /**
   * Icon to use for empty ratings (default: 'star-outline')
   */
  emptyIconName?: string;
  
  /**
   * Whether to allow half ratings (default: true)
   */
  allowHalf?: boolean;
  
  /**
   * Whether the rating can be changed by the user (default: false)
   */
  readonly?: boolean;
  
  /**
   * Callback when rating changes
   */
  onRatingChange?: (rating: number) => void;
  
  /**
   * Container style
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Whether to show the rating value as text (default: false)
   */
  showRating?: boolean;
  
  /**
   * Style for the rating text
   */
  ratingTextStyle?: StyleProp<TextStyle>;
  
  /**
   * Format for the rating text (default: '{rating}')
   */
  ratingTextFormat?: string;
  
  /**
   * Total number of ratings to show (e.g., "(123)")
   */
  totalRatings?: number;
  
  /**
   * Style for the total ratings text
   */
  totalRatingsStyle?: StyleProp<TextStyle>;
}

const Rating: React.FC<RatingProps> = ({
  value,
  maxRating = 5,
  size = 20,
  activeColor = '#FFB800',
  inactiveColor = '#DDDDDD',
  iconName = 'star',
  halfIconName = 'star-half',
  emptyIconName = 'star-outline',
  allowHalf = true,
  readonly = false,
  onRatingChange,
  containerStyle,
  showRating = false,
  ratingTextStyle,
  ratingTextFormat = '{rating}',
  totalRatings,
  totalRatingsStyle,
}) => {
  const [rating, setRating] = useState(value);

  // Ensure rating is within bounds
  const boundedRating = Math.max(0, Math.min(rating, maxRating));

  const handlePress = (selectedRating: number) => {
    if (readonly) return;
    
    const newRating = selectedRating;
    setRating(newRating);
    
    if (onRatingChange) {
      onRatingChange(newRating);
    }
  };

  // Handle half-star press based on touch position
  const handleStarPress = (selectedRating: number, event: any) => {
    if (readonly) return;
    
    if (allowHalf) {
      const starWidth = size;
      const touchX = event.nativeEvent.locationX;
      
      // If touch is on the left half of the star, use half rating
      if (touchX < starWidth / 2) {
        handlePress(selectedRating - 0.5);
      } else {
        handlePress(selectedRating);
      }
    } else {
      handlePress(selectedRating);
    }
  };

  const renderStars = () => {
    const stars = [];
    
    for (let i = 1; i <= maxRating; i++) {
      const starValue = i;
      let iconToUse = emptyIconName;
      let colorToUse = inactiveColor;
      
      if (boundedRating >= starValue) {
        iconToUse = iconName;
        colorToUse = activeColor;
      } else if (allowHalf && boundedRating + 0.5 >= starValue) {
        iconToUse = halfIconName;
        colorToUse = activeColor;
      }
      
      stars.push(
        <TouchableOpacity
          key={`star-${i}`}
          activeOpacity={readonly ? 1 : 0.7}
          onPress={(event) => handleStarPress(starValue, event)}
          style={{ padding: 2 }} // Add padding for better touch area
        >
          <Icon name={iconToUse} size={size} color={colorToUse} />
        </TouchableOpacity>
      );
    }
    
    return stars;
  };

  const formattedRating = ratingTextFormat.replace('{rating}', boundedRating.toString());

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.starsContainer}>{renderStars()}</View>
      
      {showRating && (
        <View style={styles.ratingTextContainer}>
          <Text style={[styles.ratingText, ratingTextStyle]}>
            {formattedRating}
          </Text>
          
          {totalRatings !== undefined && (
            <Text style={[styles.totalRatings, totalRatingsStyle]}>
              ({totalRatings})
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  totalRatings: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
});

export default Rating;
