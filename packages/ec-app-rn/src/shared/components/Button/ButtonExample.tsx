import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import Button from './Button';

const ButtonExample = () => {
  const [loading, setLoading] = useState(false);
  
  const handleLoadingPress = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>按钮类型</Text>
      <View style={styles.section}>
        <Button title="主要按钮" type="primary" containerStyle={styles.button} />
        <Button title="次要按钮" type="secondary" containerStyle={styles.button} />
        <Button title="线框按钮" type="outline" containerStyle={styles.button} />
        <Button title="幽灵按钮" type="ghost" containerStyle={styles.button} />
        <Button title="链接按钮" type="link" containerStyle={styles.button} />
        <Button title="危险按钮" type="danger" containerStyle={styles.button} />
      </View>
      
      <Text style={styles.title}>按钮尺寸</Text>
      <View style={styles.section}>
        <Button title="大号按钮" size="large" containerStyle={styles.button} />
        <Button title="中号按钮" size="medium" containerStyle={styles.button} />
        <Button title="小号按钮" size="small" containerStyle={styles.button} />
      </View>
      
      <Text style={styles.title}>按钮形状</Text>
      <View style={styles.section}>
        <Button title="默认形状" shape="default" containerStyle={styles.button} />
        <Button title="圆角按钮" shape="round" containerStyle={styles.button} />
        <Button icon="add" shape="circle" containerStyle={styles.button} />
      </View>
      
      <Text style={styles.title}>带图标的按钮</Text>
      <View style={styles.section}>
        <Button 
          title="左侧图标" 
          icon="cart-outline" 
          iconPosition="left" 
          containerStyle={styles.button} 
        />
        <Button 
          title="右侧图标" 
          icon="arrow-forward-outline" 
          iconPosition="right" 
          containerStyle={styles.button} 
        />
        <Button 
          icon="heart-outline" 
          type="outline" 
          containerStyle={styles.button} 
        />
      </View>
      
      <Text style={styles.title}>加载状态</Text>
      <View style={styles.section}>
        <Button 
          title="点击加载" 
          loading={loading} 
          onPress={handleLoadingPress} 
          containerStyle={styles.button} 
        />
        <Button 
          title="加载中" 
          loading={true} 
          containerStyle={styles.button} 
        />
        <Button 
          title="右侧加载" 
          loading={true} 
          iconPosition="right" 
          containerStyle={styles.button} 
        />
      </View>
      
      <Text style={styles.title}>禁用状态</Text>
      <View style={styles.section}>
        <Button 
          title="禁用主要按钮" 
          disabled={true} 
          containerStyle={styles.button} 
        />
        <Button 
          title="禁用线框按钮" 
          type="outline" 
          disabled={true} 
          containerStyle={styles.button} 
        />
        <Button 
          title="禁用链接按钮" 
          type="link" 
          disabled={true} 
          containerStyle={styles.button} 
        />
      </View>
      
      <Text style={styles.title}>块级按钮</Text>
      <View style={styles.blockSection}>
        <Button 
          title="块级主要按钮" 
          block={true} 
          containerStyle={styles.blockButton} 
        />
        <Button 
          title="块级线框按钮" 
          type="outline" 
          block={true} 
          containerStyle={styles.blockButton} 
        />
      </View>
      
      <Text style={styles.title}>自定义样式</Text>
      <View style={styles.section}>
        <Button 
          title="自定义颜色" 
          backgroundColor="#722ED1" 
          containerStyle={styles.button} 
        />
        <Button 
          title="自定义文本颜色" 
          type="outline" 
          textColor="#13C2C2" 
          backgroundColor="#13C2C2" 
          containerStyle={styles.button} 
        />
        <Button 
          title="自定义样式" 
          containerStyle={[styles.button, styles.customButton]} 
          textStyle={styles.customButtonText} 
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  blockSection: {
    width: '100%',
  },
  button: {
    margin: 5,
  },
  blockButton: {
    marginVertical: 5,
  },
  customButton: {
    backgroundColor: '#F5222D',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  customButtonText: {
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
});

export default ButtonExample;
