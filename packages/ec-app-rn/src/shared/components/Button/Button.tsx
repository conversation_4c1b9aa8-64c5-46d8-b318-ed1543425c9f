import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  View,
  ViewStyle,
  TextStyle,
  StyleProp,
  TouchableOpacityProps,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export type ButtonType = 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';
export type ButtonShape = 'default' | 'circle' | 'round';

export interface ButtonProps extends TouchableOpacityProps {
  /**
   * Button type/variant
   */
  type?: ButtonType;
  
  /**
   * Button size
   */
  size?: ButtonSize;
  
  /**
   * Button shape
   */
  shape?: ButtonShape;
  
  /**
   * Button text
   */
  title?: string;
  
  /**
   * Icon name from Ionicons
   */
  icon?: string;
  
  /**
   * Icon position (left or right)
   */
  iconPosition?: 'left' | 'right';
  
  /**
   * Custom icon size
   */
  iconSize?: number;
  
  /**
   * Custom icon color
   */
  iconColor?: string;
  
  /**
   * Whether the button is in loading state
   */
  loading?: boolean;
  
  /**
   * Whether the button is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether the button takes full width
   */
  block?: boolean;
  
  /**
   * Custom background color
   */
  backgroundColor?: string;
  
  /**
   * Custom text color
   */
  textColor?: string;
  
  /**
   * Custom style for the button container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the button text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for the button icon
   */
  iconStyle?: StyleProp<ViewStyle>;
  
  /**
   * Children to render inside the button (alternative to title)
   */
  children?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  type = 'primary',
  size = 'medium',
  shape = 'default',
  title,
  icon,
  iconPosition = 'left',
  iconSize,
  iconColor,
  loading = false,
  disabled = false,
  block = false,
  backgroundColor,
  textColor,
  containerStyle,
  textStyle,
  iconStyle,
  children,
  onPress,
  ...rest
}) => {
  // Get button styles based on type
  const getTypeStyles = (): { container: ViewStyle; text: TextStyle } => {
    switch (type) {
      case 'primary':
        return {
          container: {
            backgroundColor: backgroundColor || '#FF4D4F',
            borderWidth: 0,
          },
          text: {
            color: textColor || '#FFFFFF',
          },
        };
      case 'secondary':
        return {
          container: {
            backgroundColor: backgroundColor || '#F5F5F5',
            borderWidth: 0,
          },
          text: {
            color: textColor || '#333333',
          },
        };
      case 'outline':
        return {
          container: {
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: backgroundColor || '#FF4D4F',
          },
          text: {
            color: textColor || '#FF4D4F',
          },
        };
      case 'ghost':
        return {
          container: {
            backgroundColor: 'transparent',
            borderWidth: 0,
          },
          text: {
            color: textColor || '#FF4D4F',
          },
        };
      case 'link':
        return {
          container: {
            backgroundColor: 'transparent',
            borderWidth: 0,
            paddingHorizontal: 0,
            paddingVertical: 0,
            minHeight: 0,
          },
          text: {
            color: textColor || '#1890FF',
            textDecorationLine: 'underline',
          },
        };
      case 'danger':
        return {
          container: {
            backgroundColor: backgroundColor || '#FF4D4F',
            borderWidth: 0,
          },
          text: {
            color: textColor || '#FFFFFF',
          },
        };
      default:
        return {
          container: {
            backgroundColor: backgroundColor || '#FF4D4F',
            borderWidth: 0,
          },
          text: {
            color: textColor || '#FFFFFF',
          },
        };
    }
  };

  // Get button size styles
  const getSizeStyles = (): { container: ViewStyle; text: TextStyle; icon: number } => {
    switch (size) {
      case 'small':
        return {
          container: {
            paddingHorizontal: 12,
            paddingVertical: 6,
            minHeight: 32,
          },
          text: {
            fontSize: 12,
          },
          icon: iconSize || 14,
        };
      case 'large':
        return {
          container: {
            paddingHorizontal: 24,
            paddingVertical: 12,
            minHeight: 48,
          },
          text: {
            fontSize: 16,
          },
          icon: iconSize || 20,
        };
      default: // medium
        return {
          container: {
            paddingHorizontal: 16,
            paddingVertical: 8,
            minHeight: 40,
          },
          text: {
            fontSize: 14,
          },
          icon: iconSize || 16,
        };
    }
  };

  // Get button shape styles
  const getShapeStyles = (): ViewStyle => {
    switch (shape) {
      case 'circle':
        const sizeValue = getSizeStyles();
        const diameter = Math.max(sizeValue.container.minHeight as number, 40);
        return {
          width: diameter,
          height: diameter,
          borderRadius: diameter / 2,
          paddingHorizontal: 0,
          paddingVertical: 0,
          justifyContent: 'center',
          alignItems: 'center',
        };
      case 'round':
        return {
          borderRadius: 40,
        };
      default:
        return {
          borderRadius: 4,
        };
    }
  };

  // Get disabled styles
  const getDisabledStyles = (): { container: ViewStyle; text: TextStyle } => {
    return {
      container: {
        backgroundColor: type === 'outline' || type === 'ghost' || type === 'link' 
          ? 'transparent' 
          : '#F5F5F5',
        borderColor: '#D9D9D9',
        opacity: 0.6,
      },
      text: {
        color: '#BFBFBF',
      },
    };
  };

  // Combine all styles
  const typeStyles = getTypeStyles();
  const sizeStyles = getSizeStyles();
  const shapeStyles = getShapeStyles();
  const disabledStyles = disabled ? getDisabledStyles() : { container: {}, text: {} };

  // Determine icon color based on button type and state
  const getIconColor = () => {
    if (iconColor) return iconColor;
    if (disabled) return '#BFBFBF';
    return typeStyles.text.color;
  };

  // Render button content
  const renderContent = () => {
    const iconComponent = icon ? (
      <Icon
        name={icon}
        size={sizeStyles.icon}
        color={getIconColor()}
        style={[
          styles.icon,
          iconPosition === 'right' ? styles.iconRight : styles.iconLeft,
          iconStyle,
        ]}
      />
    ) : null;

    const loadingComponent = loading ? (
      <ActivityIndicator
        size="small"
        color={getIconColor()}
        style={[
          styles.loading,
          iconPosition === 'right' ? styles.iconRight : styles.iconLeft,
        ]}
      />
    ) : null;

    const textComponent = title ? (
      <Text
        style={[
          styles.text,
          typeStyles.text,
          sizeStyles.text,
          disabledStyles.text,
          textStyle,
        ]}
        numberOfLines={1}
      >
        {title}
      </Text>
    ) : null;

    // If children are provided, use them instead of the default content
    if (children) {
      return children;
    }

    // Show loading indicator or icon + text
    if (loading) {
      return (
        <>
          {iconPosition === 'left' && loadingComponent}
          {textComponent}
          {iconPosition === 'right' && loadingComponent}
        </>
      );
    }

    return (
      <>
        {iconPosition === 'left' && iconComponent}
        {textComponent}
        {iconPosition === 'right' && iconComponent}
      </>
    );
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={!disabled && !loading ? onPress : undefined}
      disabled={disabled || loading}
      style={[
        styles.container,
        typeStyles.container,
        sizeStyles.container,
        shapeStyles,
        disabledStyles.container,
        block && styles.blockContainer,
        containerStyle,
      ]}
      {...rest}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
  },
  blockContainer: {
    width: '100%',
  },
  text: {
    fontWeight: '500',
    textAlign: 'center',
  },
  icon: {
    alignSelf: 'center',
  },
  iconLeft: {
    marginRight: 6,
  },
  iconRight: {
    marginLeft: 6,
  },
  loading: {
    alignSelf: 'center',
  },
});

export default Button;
