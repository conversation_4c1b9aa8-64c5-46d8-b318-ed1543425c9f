import React from 'react';
import { View, Button } from 'react-native';
import Dialog from '../Dialog';
import { useDialogState } from '../useDialogState';

const BasicDialogExample = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Button title="显示基础对话框" onPress={showDialog} />
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="基础对话框"
        message="这是一个基础对话框示例，包含确认和取消按钮。"
        onConfirm={() => {
          console.log('确认按钮被点击');
          hideDialog();
        }}
      />
    </View>
  );
};

export default BasicDialogExample;
