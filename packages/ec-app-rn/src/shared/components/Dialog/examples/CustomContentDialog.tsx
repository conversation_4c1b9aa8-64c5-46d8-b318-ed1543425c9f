import React, { useState } from 'react';
import { View, Button, Text, TextInput, StyleSheet } from 'react-native';
import Dialog from '../Dialog';
import { useDialogState } from '../useDialogState';

const CustomContentDialog = () => {
  const { isVisible, showDialog, hideDialog } = useDialogState();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = () => {
    console.log('Submitted form data:', { name, email });
    // 在实际应用中，这里可以调用API提交表单数据
    hideDialog();
    // 重置表单
    setName('');
    setEmail('');
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Button title="显示自定义内容对话框" onPress={showDialog} />
      
      <Dialog
        visible={isVisible}
        onClose={hideDialog}
        title="用户信息"
        showCloseButton={true}
        onConfirm={handleSubmit}
        confirmText="提交"
        cancelText="取消"
      >
        <View style={styles.formContainer}>
          <Text style={styles.label}>姓名</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="请输入您的姓名"
          />
          
          <Text style={styles.label}>电子邮箱</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="请输入您的电子邮箱"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
      </Dialog>
    </View>
  );
};

const styles = StyleSheet.create({
  formContainer: {
    width: '100%',
    paddingHorizontal: 5,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
});

export default CustomContentDialog;
