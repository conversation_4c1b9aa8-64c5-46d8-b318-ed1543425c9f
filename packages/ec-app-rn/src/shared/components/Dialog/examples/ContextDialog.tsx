import React from 'react';
import { View, Button, Text } from 'react-native';
import { DialogProvider, useDialog } from '../DialogProvider';

// 子组件使用useDialog钩子来显示对话框
const DialogTrigger = () => {
  const { showDialog } = useDialog();

  const handleShowSuccessDialog = () => {
    showDialog({
      title: '操作成功',
      message: '您的操作已成功完成！',
      type: 'success',
      confirmText: '知道了',
      onConfirm: () => console.log('Success dialog confirmed'),
    });
  };

  const handleShowErrorDialog = () => {
    showDialog({
      title: '操作失败',
      message: '抱歉，操作过程中出现了错误，请稍后重试。',
      type: 'error',
      confirmText: '重试',
      cancelText: '取消',
      onConfirm: () => console.log('Error dialog retry clicked'),
    });
  };

  return (
    <View style={{ marginVertical: 20 }}>
      <Button title="显示成功对话框" onPress={handleShowSuccessDialog} />
      <View style={{ height: 20 }} />
      <Button title="显示错误对话框" onPress={handleShowErrorDialog} />
    </View>
  );
};

// 父组件提供DialogProvider
const ContextDialogExample = () => {
  return (
    <DialogProvider>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 16, marginBottom: 20, textAlign: 'center' }}>
          使用Context API管理对话框状态，可以在任何子组件中轻松调用对话框
        </Text>
        <DialogTrigger />
      </View>
    </DialogProvider>
  );
};

export default ContextDialogExample;
