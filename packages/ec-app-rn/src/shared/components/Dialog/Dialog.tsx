import React, { useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Platform,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

const { width } = Dimensions.get('window');

export type DialogProps = {
  visible: boolean;
  title?: string;
  message?: string;
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  children?: React.ReactNode;
  closeOnBackdropPress?: boolean;
  showCloseButton?: boolean;
  customButtons?: React.ReactNode;
  animationType?: 'none' | 'slide' | 'fade';
};

const Dialog: React.FC<DialogProps> = ({
  visible,
  title,
  message,
  onClose,
  onConfirm,
  confirmText = '确认',
  cancelText = '取消',
  type = 'info',
  children,
  closeOnBackdropPress = true,
  showCloseButton = true,
  customButtons,
  animationType = 'fade',
}) => {
  const opacity = React.useRef(new Animated.Value(0)).current;
  const scale = React.useRef(new Animated.Value(0.9)).current;
  
  useEffect(() => {
    // Only run animations if the component is mounted
    let isMounted = true;
    
    if (visible) {
      // Ensure we're using valid values for animation
      const targetOpacity = 1;
      const targetScale = 1;
      
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: targetOpacity,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: targetScale,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Haptic feedback when dialog opens
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    } else if (isMounted) { // Only animate if still mounted
      // Ensure we're using valid values for animation
      const targetOpacity = 0;
      const targetScale = 0.9;
      
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: targetOpacity,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: targetScale,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
    
    // Cleanup function to prevent animation updates on unmounted component
    return () => {
      isMounted = false;
    };
  }, [visible, opacity, scale]);

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    // Haptic feedback on confirm
    ReactNativeHapticFeedback.trigger('impactMedium', {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    });
  };

  const handleCancel = () => {
    onClose();
    // Haptic feedback on cancel
    ReactNativeHapticFeedback.trigger('impactLight', {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    });
  };

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  const getIconName = () => {
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'warning':
        return 'warning';
      case 'error':
        return 'alert-circle';
      case 'info':
      default:
        return 'information-circle';
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'success':
        return '#4CAF50';
      case 'warning':
        return '#FF9800';
      case 'error':
        return '#F44336';
      case 'info':
      default:
        return '#2196F3';
    }
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.backdrop}>
          <TouchableWithoutFeedback>
            <Animated.View 
              style={[
                styles.container, 
                { 
                  opacity: opacity,
                  transform: [{ scale: scale }] 
                }
              ]}
            >
              {showCloseButton && (
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onClose}
                  hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                >
                  <Icon name="close" size={24} color="#999" />
                </TouchableOpacity>
              )}

              {type && (
                <View style={styles.iconContainer}>
                  <Icon name={getIconName()} size={40} color={getIconColor()} />
                </View>
              )}

              {title && <Text style={styles.title}>{title}</Text>}
              
              {message && <Text style={styles.message}>{message}</Text>}
              
              {children && <View style={styles.contentContainer}>{children}</View>}
              
              {customButtons ? (
                <View style={styles.customButtonsContainer}>{customButtons}</View>
              ) : (
                <View style={styles.buttonContainer}>
                  {cancelText && (
                    <TouchableOpacity
                      style={[styles.button, styles.cancelButton]}
                      onPress={handleCancel}
                    >
                      <Text style={styles.cancelButtonText}>{cancelText}</Text>
                    </TouchableOpacity>
                  )}
                  
                  {onConfirm && (
                    <TouchableOpacity
                      style={[styles.button, styles.confirmButton]}
                      onPress={handleConfirm}
                    >
                      <Text style={styles.confirmButtonText}>{confirmText}</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width * 0.85,
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    lineHeight: 22,
  },
  contentContainer: {
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  customButtonsContainer: {
    marginTop: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  confirmButton: {
    backgroundColor: '#2196F3',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default Dialog;
