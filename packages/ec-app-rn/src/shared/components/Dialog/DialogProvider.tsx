import React, { createContext, useContext, useState, ReactNode } from 'react';
import Dialog, { DialogProps } from './Dialog';

type DialogOptions = Omit<DialogProps, 'visible' | 'onClose'>;

interface DialogContextType {
  showDialog: (options: DialogOptions) => void;
  hideDialog: () => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

export const useDialog = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
};

interface DialogProviderProps {
  children: ReactNode;
}

export const DialogProvider: React.FC<DialogProviderProps> = ({ children }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogOptions, setDialogOptions] = useState<DialogOptions>({});

  const showDialog = (options: DialogOptions) => {
    setDialogOptions(options);
    setDialogVisible(true);
  };

  const hideDialog = () => {
    setDialogVisible(false);
  };

  return (
    <DialogContext.Provider value={{ showDialog, hideDialog }}>
      {children}
      <Dialog
        visible={dialogVisible}
        onClose={hideDialog}
        {...dialogOptions}
      />
    </DialogContext.Provider>
  );
};
