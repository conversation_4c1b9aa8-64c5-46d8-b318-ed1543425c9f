import { useState } from 'react';

/**
 * A custom hook for managing dialog state in components
 * @returns Dialog state management utilities
 */
export const useDialogState = () => {
  const [isVisible, setIsVisible] = useState(false);

  const showDialog = () => setIsVisible(true);
  const hideDialog = () => setIsVisible(false);
  const toggleDialog = () => setIsVisible(prev => !prev);

  return {
    isVisible,
    showDialog,
    hideDialog,
    toggleDialog,
  };
};
