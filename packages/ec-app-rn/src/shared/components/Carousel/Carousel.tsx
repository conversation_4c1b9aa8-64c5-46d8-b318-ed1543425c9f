import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Image,
  Dimensions,
  TouchableOpacity,
  ImageSourcePropType,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ViewStyle,
  ImageStyle,
  StyleProp,
} from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

export interface CarouselItem {
  id: string;
  image: string | ImageSourcePropType;
  onPress?: () => void;
}

interface CarouselProps {
  data: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  dotStyle?: StyleProp<ViewStyle>;
  dotActiveStyle?: StyleProp<ViewStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
  width?: number;
  height?: number;
  borderRadius?: number;
  loop?: boolean;
}

const Carousel: React.FC<CarouselProps> = ({
  data,
  autoPlay = true,
  autoPlayInterval = 3000,
  showDots = true,
  dotStyle,
  dotActiveStyle,
  containerStyle,
  imageStyle,
  width = screenWidth - 30, // Default with some padding
  height = width * 0.5, // Default aspect ratio
  borderRadius = 10,
  loop = true,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Reset the timer when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Start auto-play if enabled
  useEffect(() => {
    if (autoPlay && data.length > 1) {
      startAutoPlay();
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [autoPlay, activeIndex, data.length]);

  const startAutoPlay = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (data.length > 1) {
        const nextIndex = (activeIndex + 1) % data.length;
        goToSlide(nextIndex);
      }
    }, autoPlayInterval);
  };

  const goToSlide = (index: number) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * width,
        animated: true,
      });
    }
    setActiveIndex(index);
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / width);
    
    if (newIndex !== activeIndex) {
      setActiveIndex(newIndex);
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffsetX / width);
    
    // Handle loop functionality
    if (loop && data.length > 1) {
      if (newIndex === data.length - 1) {
        // When reaching the last slide, prepare to loop back to first slide
        setTimeout(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ x: 0, animated: false });
          }
          setActiveIndex(0);
        }, autoPlayInterval);
      }
    }
    
    if (autoPlay) {
      startAutoPlay();
    }
  };

  const renderItem = (item: CarouselItem, index: number) => {
    const imageSource = typeof item.image === 'string' 
      ? { uri: item.image } 
      : item.image;

    return (
      <TouchableOpacity
        key={`${item.id}-${index}`}
        activeOpacity={item.onPress ? 0.8 : 1}
        onPress={item.onPress}
        style={{ width }}
      >
        <Image
          source={imageSource}
          style={[
            styles.image,
            { width, height, borderRadius },
            imageStyle,
          ]}
          resizeMode="cover"
        />
      </TouchableOpacity>
    );
  };

  const renderDots = () => {
    if (!showDots || data.length <= 1) return null;

    return (
      <View style={styles.dotsContainer}>
        {data.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => goToSlide(index)}
            style={[
              styles.dot,
              index === activeIndex ? styles.activeDot : {},
              dotStyle,
              index === activeIndex ? dotActiveStyle : {},
            ]}
          />
        ))}
      </View>
    );
  };

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, { width, height }, containerStyle]}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        onMomentumScrollEnd={handleScrollEnd}
        scrollEventThrottle={16}
      >
        {data.map((item, index) => renderItem(item, index))}
      </ScrollView>
      {renderDots()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    backgroundColor: '#f0f0f0', // Placeholder color while loading
  },
  dotsContainer: {
    position: 'absolute',
    bottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#fff',
    width: 16,
  },
});

export default Carousel;
