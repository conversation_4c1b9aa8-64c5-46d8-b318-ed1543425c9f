import React from 'react';
import { StyleSheet, View, Text, Alert } from 'react-native';
import Carousel from './Carousel';

// Example data for the carousel
const carouselData = [
  {
    id: '1',
    image: 'https://i.imgur.com/gTj9s2Y.png',
    onPress: () => Alert.alert('Carousel', 'You clicked on item 1'),
  },
  {
    id: '2',
    image: 'https://i.imgur.com/4l3gU22.png',
    onPress: () => Alert.alert('Carousel', 'You clicked on item 2'),
  },
  {
    id: '3',
    image: 'https://i.imgur.com/JPlS2Vz.png',
    onPress: () => Alert.alert('Carousel', 'You clicked on item 3'),
  },
];

const CarouselExample = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Basic Carousel</Text>
      <Carousel data={carouselData} />
      
      <Text style={styles.title}>Custom Carousel</Text>
      <Carousel
        data={carouselData}
        autoPlay={true}
        autoPlayInterval={5000}
        width={300}
        height={200}
        borderRadius={20}
        dotStyle={styles.customDot}
        dotActiveStyle={styles.customActiveDot}
      />
      
      <Text style={styles.title}>No Dots Carousel</Text>
      <Carousel
        data={carouselData}
        showDots={false}
        height={150}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 15,
  },
  customDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  customActiveDot: {
    width: 20,
    backgroundColor: '#FF4D4F',
  },
});

export default CarouselExample;
