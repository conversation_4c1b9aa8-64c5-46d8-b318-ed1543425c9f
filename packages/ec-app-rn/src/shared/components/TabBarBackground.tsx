import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemeColor } from '../hooks/useThemeColor';

/**
 * Custom tab bar background component
 * This provides a themed background for the tab bar
 */
export default function TabBarBackground() {
  const backgroundColor = useThemeColor({}, 'tabBackground');
  const borderColor = useThemeColor({}, 'tabBorder');

  return (
    <View 
      style={[
        styles.background, 
        { 
          backgroundColor,
          borderTopColor: borderColor 
        }
      ]} 
    />
  );
}

const styles = StyleSheet.create({
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderTopWidth: 1,
  },
});
