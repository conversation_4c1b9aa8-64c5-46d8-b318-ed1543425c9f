import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import Switch from './Switch';

const SwitchExample = () => {
  const [switch1, setSwitch1] = useState(true);
  const [switch2, setSwitch2] = useState(false);
  const [switch3, setSwitch3] = useState(true);
  const [switch4, setSwitch4] = useState(false);
  const [switch5, setSwitch5] = useState(true);
  const [switch6, setSwitch6] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const handleLoadingToggle = (value: boolean) => {
    setLoading(true);
    setSwitch6(value);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>基础开关</Text>
      <View style={styles.section}>
        <Switch 
          value={switch1} 
          onValueChange={setSwitch1} 
        />
      </View>
      
      <Text style={styles.title}>不同尺寸</Text>
      <View style={styles.section}>
        <View style={styles.row}>
          <Text style={styles.label}>小号：</Text>
          <Switch 
            value={switch1} 
            onValueChange={setSwitch1} 
            size="small" 
          />
        </View>
        
        <View style={styles.row}>
          <Text style={styles.label}>中号：</Text>
          <Switch 
            value={switch1} 
            onValueChange={setSwitch1} 
            size="medium" 
          />
        </View>
        
        <View style={styles.row}>
          <Text style={styles.label}>大号：</Text>
          <Switch 
            value={switch1} 
            onValueChange={setSwitch1} 
            size="large" 
          />
        </View>
      </View>
      
      <Text style={styles.title}>自定义颜色</Text>
      <View style={styles.section}>
        <View style={styles.row}>
          <Switch 
            value={switch2} 
            onValueChange={setSwitch2} 
            activeColor="#1890FF" 
            containerStyle={styles.marginRight} 
          />
          
          <Switch 
            value={switch3} 
            onValueChange={setSwitch3} 
            activeColor="#52C41A" 
            containerStyle={styles.marginRight} 
          />
          
          <Switch 
            value={switch2} 
            onValueChange={setSwitch2} 
            activeColor="#722ED1" 
          />
        </View>
      </View>
      
      <Text style={styles.title}>禁用状态</Text>
      <View style={styles.section}>
        <View style={styles.row}>
          <Switch 
            value={true} 
            disabled={true} 
            containerStyle={styles.marginRight} 
          />
          
          <Switch 
            value={false} 
            disabled={true} 
          />
        </View>
      </View>
      
      <Text style={styles.title}>带标签</Text>
      <View style={styles.section}>
        <View style={styles.column}>
          <Switch 
            value={switch4} 
            onValueChange={setSwitch4} 
            label="右侧标签" 
            labelPosition="right" 
            containerStyle={styles.marginBottom} 
          />
          
          <Switch 
            value={switch5} 
            onValueChange={setSwitch5} 
            label="左侧标签" 
            labelPosition="left" 
            containerStyle={styles.marginBottom} 
          />
          
          <Switch 
            value={switch4} 
            onValueChange={setSwitch4} 
            label="自定义标签样式" 
            labelStyle={styles.customLabel} 
          />
        </View>
      </View>
      
      <Text style={styles.title}>加载状态</Text>
      <View style={styles.section}>
        <Switch 
          value={switch6} 
          onValueChange={handleLoadingToggle} 
          loading={loading} 
        />
      </View>
      
      <Text style={styles.title}>应用场景</Text>
      <View style={styles.section}>
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>推送通知</Text>
          <Switch 
            value={switch1} 
            onValueChange={setSwitch1} 
          />
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>深色模式</Text>
          <Switch 
            value={switch2} 
            onValueChange={setSwitch2} 
          />
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>自动播放</Text>
          <Switch 
            value={switch3} 
            onValueChange={setSwitch3} 
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  column: {
    flexDirection: 'column',
  },
  label: {
    fontSize: 14,
    marginRight: 10,
    width: 50,
  },
  marginRight: {
    marginRight: 20,
  },
  marginBottom: {
    marginBottom: 15,
  },
  customLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF4D4F',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  settingLabel: {
    fontSize: 16,
  },
  divider: {
    height: 1,
    backgroundColor: '#f0f0f0',
  },
});

export default SwitchExample;
