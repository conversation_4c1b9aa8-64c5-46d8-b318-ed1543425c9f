import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Animated,
  ViewStyle,
  StyleProp,
  Text,
  TextStyle,
} from 'react-native';

export interface SwitchProps {
  /**
   * Whether the switch is checked/on
   */
  value?: boolean;
  
  /**
   * Callback when the switch value changes
   */
  onValueChange?: (value: boolean) => void;
  
  /**
   * Whether the switch is disabled
   */
  disabled?: boolean;
  
  /**
   * Size of the switch (width will be 2x height)
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Color when the switch is on
   */
  activeColor?: string;
  
  /**
   * Color when the switch is off
   */
  inactiveColor?: string;
  
  /**
   * Color of the thumb/knob
   */
  thumbColor?: string;
  
  /**
   * Custom style for the switch container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Label to display next to the switch
   */
  label?: string;
  
  /**
   * Position of the label
   */
  labelPosition?: 'left' | 'right';
  
  /**
   * Custom style for the label
   */
  labelStyle?: StyleProp<TextStyle>;
  
  /**
   * Loading state
   */
  loading?: boolean;
}

const Switch: React.FC<SwitchProps> = ({
  value = false,
  onValueChange,
  disabled = false,
  size = 'medium',
  activeColor = '#FF4D4F',
  inactiveColor = '#BFBFBF',
  thumbColor = '#FFFFFF',
  containerStyle,
  label,
  labelPosition = 'right',
  labelStyle,
  loading = false,
}) => {
  const [isChecked, setIsChecked] = useState(value);
  const [thumbAnim] = useState(new Animated.Value(value ? 1 : 0));
  
  // Update internal state when prop changes
  useEffect(() => {
    setIsChecked(value);
    Animated.timing(thumbAnim, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, thumbAnim]);
  
  // Get dimensions based on size
  const getDimensions = () => {
    switch (size) {
      case 'small':
        return { width: 36, height: 20, thumbSize: 16 };
      case 'large':
        return { width: 60, height: 32, thumbSize: 28 };
      default: // medium
        return { width: 48, height: 26, thumbSize: 22 };
    }
  };
  
  const { width, height, thumbSize } = getDimensions();
  
  // Calculate thumb position
  const thumbPosition = thumbAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [2, width - thumbSize - 2],
  });
  
  // Calculate background color
  const backgroundColor = thumbAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [inactiveColor, activeColor],
  });
  
  // Handle toggle
  const handleToggle = () => {
    if (disabled || loading) return;
    
    const newValue = !isChecked;
    setIsChecked(newValue);
    
    Animated.timing(thumbAnim, {
      toValue: newValue ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
    
    if (onValueChange) {
      onValueChange(newValue);
    }
  };
  
  // Render thumb with loading indicator if needed
  const renderThumb = () => {
    return (
      <Animated.View
        style={[
          styles.thumb,
          {
            width: thumbSize,
            height: thumbSize,
            backgroundColor: thumbColor,
            transform: [{ translateX: thumbPosition }],
          },
          loading && styles.loadingThumb,
        ]}
      >
        {loading && (
          <View style={[styles.loadingIndicator, { backgroundColor: activeColor }]} />
        )}
      </Animated.View>
    );
  };
  
  // Render switch
  const renderSwitch = () => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={handleToggle}
        disabled={disabled}
        style={[
          styles.container,
          {
            opacity: disabled ? 0.5 : 1,
            width,
            height,
            borderRadius: height / 2,
          },
          containerStyle,
        ]}
      >
        <Animated.View
          style={[
            StyleSheet.absoluteFill,
            {
              backgroundColor,
              borderRadius: height / 2,
            },
          ]}
        />
        {renderThumb()}
      </TouchableOpacity>
    );
  };
  
  // If no label, just render the switch
  if (!label) {
    return renderSwitch();
  }
  
  // Render switch with label
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handleToggle}
      disabled={disabled}
      style={[
        styles.labelContainer,
        labelPosition === 'left' ? styles.labelLeft : styles.labelRight,
      ]}
    >
      {labelPosition === 'left' && (
        <Text
          style={[
            styles.label,
            { marginRight: 10, opacity: disabled ? 0.5 : 1 },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
      
      {renderSwitch()}
      
      {labelPosition === 'right' && (
        <Text
          style={[
            styles.label,
            { marginLeft: 10, opacity: disabled ? 0.5 : 1 },
            labelStyle,
          ]}
        >
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    backgroundColor: '#BFBFBF',
  },
  thumb: {
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    borderRadius: 999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingThumb: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    width: '30%',
    height: '30%',
    borderRadius: 999,
    opacity: 0.8,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelLeft: {
    justifyContent: 'flex-end',
  },
  labelRight: {
    justifyContent: 'flex-start',
  },
  label: {
    fontSize: 14,
    color: '#333',
  },
});

export default Switch;
