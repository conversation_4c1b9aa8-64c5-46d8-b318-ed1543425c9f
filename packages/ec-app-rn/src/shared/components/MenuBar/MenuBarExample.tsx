import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MenuBar, { MenuItem, MenuBarPosition, MenuBarStyle } from './MenuBar';

const MenuBarExample: React.FC = () => {
  const navigation = useNavigation();
  const [selectedKey, setSelectedKey] = useState<string>('home');
  const [position, setPosition] = useState<MenuBarPosition>('bottom');
  const [style, setStyle] = useState<MenuBarStyle>('default');
  const [showIcons, setShowIcons] = useState<boolean>(true);
  const [showLabels, setShowLabels] = useState<boolean>(true);
  const [showDividers, setShowDividers] = useState<boolean>(false);
  const [showBorder, setShowBorder] = useState<boolean>(true);
  const [showShadow, setShowShadow] = useState<boolean>(true);
  
  // Sample menu items
  const bottomTabItems: MenuItem[] = [
    { key: 'home', label: '首页', icon: 'home-outline' },
    { key: 'category', label: '分类', icon: 'grid-outline' },
    { key: 'cart', label: '购物车', icon: 'cart-outline', badge: 2 },
    { key: 'orders', label: '订单', icon: 'document-text-outline' },
    { key: 'profile', label: '我的', icon: 'person-outline' },
  ];
  
  const sideMenuItems: MenuItem[] = [
    { key: 'dashboard', label: '控制台', icon: 'speedometer-outline' },
    {
      key: 'products',
      label: '商品管理',
      icon: 'cube-outline',
      children: [
        { key: 'product-list', label: '商品列表', icon: 'list-outline' },
        { key: 'product-add', label: '添加商品', icon: 'add-circle-outline' },
        { key: 'product-categories', label: '商品分类', icon: 'folder-outline' },
      ],
    },
    {
      key: 'orders',
      label: '订单管理',
      icon: 'receipt-outline',
      children: [
        { key: 'order-list', label: '订单列表', icon: 'list-outline' },
        { key: 'order-processing', label: '订单处理', icon: 'sync-outline' },
        { key: 'order-shipping', label: '物流管理', icon: 'car-outline' },
      ],
    },
    { key: 'customers', label: '客户管理', icon: 'people-outline' },
    { key: 'marketing', label: '营销活动', icon: 'megaphone-outline' },
    { key: 'analytics', label: '数据分析', icon: 'bar-chart-outline' },
    { key: 'settings', label: '系统设置', icon: 'settings-outline' },
  ];
  
  const topMenuItems: MenuItem[] = [
    { key: 'home', label: '首页', icon: 'home-outline' },
    { key: 'products', label: '全部商品', icon: 'grid-outline' },
    { key: 'new', label: '新品上市', icon: 'star-outline', badge: 'NEW' },
    { key: 'sale', label: '特惠活动', icon: 'pricetag-outline' },
    { key: 'brands', label: '品牌专区', icon: 'bookmark-outline' },
  ];
  
  // Get menu items based on position
  const getMenuItems = () => {
    if (position === 'left' || position === 'right') {
      return sideMenuItems;
    } else if (position === 'top') {
      return topMenuItems;
    } else {
      return bottomTabItems;
    }
  };
  
  // Handle menu item selection
  const handleSelect = (key: string) => {
    setSelectedKey(key);
  };
  
  // Render position option
  const renderPositionOption = (pos: MenuBarPosition, label: string) => (
    <TouchableOpacity
      style={[
        styles.styleOption,
        position === pos && styles.selectedStyleOption,
      ]}
      onPress={() => setPosition(pos)}
    >
      <Text
        style={[
          styles.styleOptionText,
          position === pos && styles.selectedStyleOptionText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
  
  // Render style option
  const renderStyleOption = (styleOption: MenuBarStyle, label: string) => (
    <TouchableOpacity
      style={[
        styles.styleOption,
        style === styleOption && styles.selectedStyleOption,
      ]}
      onPress={() => setStyle(styleOption)}
    >
      <Text
        style={[
          styles.styleOptionText,
          style === styleOption && styles.selectedStyleOptionText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
  
  // Get style label for display
  const getPositionLabel = (pos: MenuBarPosition): string => {
    switch (pos) {
      case 'top': return '顶部';
      case 'bottom': return '底部';
      case 'left': return '左侧';
      case 'right': return '右侧';
      default: return '';
    }
  };
  
  // Get style label for display
  const getStyleLabel = (styleOption: MenuBarStyle): string => {
    switch (styleOption) {
      case 'default': return '默认';
      case 'primary': return '主要';
      case 'secondary': return '次要';
      case 'dark': return '深色';
      case 'light': return '浅色';
      case 'transparent': return '透明';
      default: return '';
    }
  };
  
  // Render custom examples
  const renderCustomExamples = () => (
    <>
      <Text style={styles.sectionTitle}>自定义菜单栏示例</Text>
      
      {/* E-commerce top menu */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>电商顶部菜单</Text>
        <MenuBar
          items={[
            { key: 'all', label: '全部', icon: 'apps-outline' },
            { key: 'clothing', label: '服装', icon: 'shirt-outline' },
            { key: 'electronics', label: '电子产品', icon: 'laptop-outline' },
            { key: 'home', label: '家居', icon: 'home-outline' },
            { key: 'beauty', label: '美妆', icon: 'color-palette-outline' },
            { key: 'sports', label: '运动', icon: 'football-outline' },
            { key: 'books', label: '图书', icon: 'book-outline' },
          ]}
          selectedKey="all"
          position="top"
          style="light"
          showBorder={true}
          showShadow={false}
        />
      </View>
      
      {/* Social media bottom tab */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>社交媒体底部标签</Text>
        <MenuBar
          items={[
            { key: 'feed', label: '动态', icon: 'home-outline' },
            { key: 'discover', label: '发现', icon: 'compass-outline' },
            { key: 'create', label: '', icon: 'add-circle' },
            { key: 'notifications', label: '通知', icon: 'notifications-outline', badge: 5 },
            { key: 'profile', label: '我的', icon: 'person-outline' },
          ]}
          selectedKey="feed"
          position="bottom"
          style="light"
          showBorder={true}
          showShadow={true}
        />
      </View>
      
      {/* Admin dashboard side menu */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>管理后台侧边菜单</Text>
        <View style={styles.sideMenuContainer}>
          <MenuBar
            items={[
              { key: 'dashboard', label: '仪表盘', icon: 'speedometer-outline' },
              { key: 'users', label: '用户管理', icon: 'people-outline' },
              { key: 'content', label: '内容管理', icon: 'document-outline' },
              { key: 'settings', label: '系统设置', icon: 'settings-outline' },
              { key: 'analytics', label: '数据分析', icon: 'bar-chart-outline' },
            ]}
            selectedKey="dashboard"
            position="left"
            style="dark"
            showBorder={false}
            showShadow={true}
            containerStyle={styles.adminSideMenu}
          />
          <View style={styles.adminContent}>
            <Text style={styles.adminTitle}>仪表盘</Text>
            <Text style={styles.adminText}>欢迎使用管理系统</Text>
          </View>
        </View>
      </View>
    </>
  );
  
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container}>
        <Text style={styles.title}>菜单栏组件</Text>
        
        <View style={styles.menuContainer}>
          <MenuBar
            items={getMenuItems()}
            selectedKey={selectedKey}
            position={position}
            style={style}
            showIcons={showIcons}
            showLabels={showLabels}
            showDividers={showDividers}
            showBorder={showBorder}
            showShadow={showShadow}
            onSelect={handleSelect}
          />
        </View>
        
        <Text style={styles.sectionTitle}>菜单位置</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsRow}>
          {renderPositionOption('top', '顶部')}
          {renderPositionOption('bottom', '底部')}
          {renderPositionOption('left', '左侧')}
          {renderPositionOption('right', '右侧')}
        </ScrollView>
        
        <Text style={styles.sectionTitle}>菜单样式</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsRow}>
          {renderStyleOption('default', '默认')}
          {renderStyleOption('primary', '主要')}
          {renderStyleOption('secondary', '次要')}
          {renderStyleOption('dark', '深色')}
          {renderStyleOption('light', '浅色')}
          {renderStyleOption('transparent', '透明')}
        </ScrollView>
        
        <Text style={styles.sectionTitle}>配置选项</Text>
        <View style={styles.optionsContainer}>
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示图标</Text>
            <Switch
              value={showIcons}
              onValueChange={setShowIcons}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示文字</Text>
            <Switch
              value={showLabels}
              onValueChange={setShowLabels}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示分隔线</Text>
            <Switch
              value={showDividers}
              onValueChange={setShowDividers}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示边框</Text>
            <Switch
              value={showBorder}
              onValueChange={setShowBorder}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示阴影</Text>
            <Switch
              value={showShadow}
              onValueChange={setShowShadow}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
        </View>
        
        <View style={styles.currentConfig}>
          <Text style={styles.currentConfigTitle}>当前配置</Text>
          <Text style={styles.configItem}>位置: {getPositionLabel(position)}</Text>
          <Text style={styles.configItem}>样式: {getStyleLabel(style)}</Text>
          <Text style={styles.configItem}>图标: {showIcons ? '显示' : '隐藏'}</Text>
          <Text style={styles.configItem}>文字: {showLabels ? '显示' : '隐藏'}</Text>
          <Text style={styles.configItem}>分隔线: {showDividers ? '显示' : '隐藏'}</Text>
          <Text style={styles.configItem}>边框: {showBorder ? '显示' : '隐藏'}</Text>
          <Text style={styles.configItem}>阴影: {showShadow ? '显示' : '隐藏'}</Text>
        </View>
        
        {renderCustomExamples()}
        
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>使用说明</Text>
          <Text style={styles.infoText}>1. 基础用法：{'<MenuBar items={items} selectedKey="home" />'}</Text>
          <Text style={styles.infoText}>2. 设置位置：{'<MenuBar position="bottom" items={items} />'}</Text>
          <Text style={styles.infoText}>3. 设置样式：{'<MenuBar style="primary" items={items} />'}</Text>
          <Text style={styles.infoText}>4. 处理选择：{'<MenuBar onSelect={handleSelect} items={items} />'}</Text>
          <Text style={styles.infoText}>5. 带徽标：{'items={[{ key: "cart", label: "购物车", badge: 5 }]}'}</Text>
          <Text style={styles.infoText}>6. 带子菜单：{'items={[{ key: "products", children: [...] }]}'}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#333',
  },
  menuContainer: {
    marginBottom: 24,
    borderRadius: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: '#333',
  },
  optionsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  styleOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  selectedStyleOption: {
    backgroundColor: '#1890ff',
  },
  styleOptionText: {
    fontSize: 14,
    color: '#666',
  },
  selectedStyleOptionText: {
    color: '#fff',
  },
  optionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#f0f0f0',
  },
  optionLabel: {
    fontSize: 16,
    color: '#333',
  },
  currentConfig: {
    backgroundColor: '#f0f5ff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  currentConfigTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  configItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  exampleContainer: {
    marginBottom: 24,
  },
  exampleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#666',
  },
  sideMenuContainer: {
    flexDirection: 'row',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  adminSideMenu: {
    width: 160,
    height: '100%',
  },
  adminContent: {
    flex: 1,
    padding: 16,
  },
  adminTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  adminText: {
    fontSize: 14,
    color: '#666',
  },
  infoBox: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f0f5ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#1890ff',
    marginBottom: 32,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#666',
  },
});

export default MenuBarExample;
