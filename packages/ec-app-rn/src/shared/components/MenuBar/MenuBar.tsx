import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export type MenuBarPosition = 'top' | 'bottom' | 'left' | 'right';
export type MenuBarStyle = 'default' | 'primary' | 'secondary' | 'dark' | 'light' | 'transparent';

export interface MenuItem {
  /**
   * Unique key for the menu item
   */
  key: string;
  
  /**
   * Label text to display
   */
  label: string;
  
  /**
   * Optional icon name (from Ionicons)
   */
  icon?: string;
  
  /**
   * Whether the item is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether the item has a badge
   */
  badge?: boolean | number | string;
  
  /**
   * Custom badge style
   */
  badgeStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom badge text style
   */
  badgeTextStyle?: StyleProp<TextStyle>;
  
  /**
   * Children items for submenu
   */
  children?: MenuItem[];
}

export interface MenuBarProps {
  /**
   * Array of menu items
   */
  items: MenuItem[];
  
  /**
   * Currently selected item key
   */
  selectedKey?: string;
  
  /**
   * Position of the menu bar
   */
  position?: MenuBarPosition;
  
  /**
   * Style variant of the menu bar
   */
  style?: MenuBarStyle;
  
  /**
   * Whether to show icons
   */
  showIcons?: boolean;
  
  /**
   * Whether to show labels
   */
  showLabels?: boolean;
  
  /**
   * Whether to show dividers between items
   */
  showDividers?: boolean;
  
  /**
   * Whether to show a border
   */
  showBorder?: boolean;
  
  /**
   * Whether to show a shadow
   */
  showShadow?: boolean;
  
  /**
   * Custom background color
   */
  backgroundColor?: string;
  
  /**
   * Custom text color
   */
  textColor?: string;
  
  /**
   * Custom selected text color
   */
  selectedTextColor?: string;
  
  /**
   * Custom icon color
   */
  iconColor?: string;
  
  /**
   * Custom selected icon color
   */
  selectedIconColor?: string;
  
  /**
   * Custom active indicator color
   */
  activeIndicatorColor?: string;
  
  /**
   * Custom styles for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom styles for menu items
   */
  itemStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom styles for item text
   */
  itemTextStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom styles for selected item
   */
  selectedItemStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom styles for selected item text
   */
  selectedItemTextStyle?: StyleProp<TextStyle>;
  
  /**
   * Callback when an item is selected
   */
  onSelect?: (key: string) => void;
  
  /**
   * Custom render function for menu item
   */
  renderItem?: (item: MenuItem, isSelected: boolean) => React.ReactNode;
}

const MenuBar: React.FC<MenuBarProps> = ({
  items = [],
  selectedKey,
  position = 'bottom',
  style = 'default',
  showIcons = true,
  showLabels = true,
  showDividers = false,
  showBorder = true,
  showShadow = true,
  backgroundColor,
  textColor,
  selectedTextColor,
  iconColor,
  selectedIconColor,
  activeIndicatorColor,
  containerStyle,
  itemStyle,
  itemTextStyle,
  selectedItemStyle,
  selectedItemTextStyle,
  onSelect,
  renderItem,
}) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  
  // Get style based on variant
  const getStyleForVariant = (): {
    container: ViewStyle;
    text: TextStyle;
    selectedText: TextStyle;
    icon: TextStyle;
    selectedIcon: TextStyle;
    activeIndicator: ViewStyle;
  } => {
    switch (style) {
      case 'primary':
        return {
          container: {
            backgroundColor: '#1890ff',
          },
          text: {
            color: 'rgba(255, 255, 255, 0.65)',
          },
          selectedText: {
            color: '#fff',
          },
          icon: {
            color: 'rgba(255, 255, 255, 0.65)',
          },
          selectedIcon: {
            color: '#fff',
          },
          activeIndicator: {
            backgroundColor: '#fff',
          },
        };
      case 'secondary':
        return {
          container: {
            backgroundColor: '#f5f5f5',
          },
          text: {
            color: '#666',
          },
          selectedText: {
            color: '#1890ff',
          },
          icon: {
            color: '#666',
          },
          selectedIcon: {
            color: '#1890ff',
          },
          activeIndicator: {
            backgroundColor: '#1890ff',
          },
        };
      case 'dark':
        return {
          container: {
            backgroundColor: '#222',
          },
          text: {
            color: 'rgba(255, 255, 255, 0.65)',
          },
          selectedText: {
            color: '#fff',
          },
          icon: {
            color: 'rgba(255, 255, 255, 0.65)',
          },
          selectedIcon: {
            color: '#fff',
          },
          activeIndicator: {
            backgroundColor: '#1890ff',
          },
        };
      case 'light':
        return {
          container: {
            backgroundColor: '#fff',
          },
          text: {
            color: '#999',
          },
          selectedText: {
            color: '#1890ff',
          },
          icon: {
            color: '#999',
          },
          selectedIcon: {
            color: '#1890ff',
          },
          activeIndicator: {
            backgroundColor: '#1890ff',
          },
        };
      case 'transparent':
        return {
          container: {
            backgroundColor: 'transparent',
          },
          text: {
            color: '#999',
          },
          selectedText: {
            color: '#1890ff',
          },
          icon: {
            color: '#999',
          },
          selectedIcon: {
            color: '#1890ff',
          },
          activeIndicator: {
            backgroundColor: '#1890ff',
          },
        };
      case 'default':
      default:
        return {
          container: {
            backgroundColor: '#fff',
          },
          text: {
            color: '#666',
          },
          selectedText: {
            color: '#1890ff',
          },
          icon: {
            color: '#666',
          },
          selectedIcon: {
            color: '#1890ff',
          },
          activeIndicator: {
            backgroundColor: '#1890ff',
          },
        };
    }
  };
  
  const variantStyle = getStyleForVariant();
  
  // Toggle submenu
  const toggleSubmenu = (key: string) => {
    if (expandedKeys.includes(key)) {
      setExpandedKeys(expandedKeys.filter(k => k !== key));
    } else {
      setExpandedKeys([...expandedKeys, key]);
    }
  };
  
  // Determine if an item is expanded
  const isExpanded = (key: string) => expandedKeys.includes(key);
  
  // Handle item press
  const handleItemPress = (item: MenuItem) => {
    if (item.disabled) return;
    
    if (item.children && item.children.length > 0) {
      toggleSubmenu(item.key);
    } else if (onSelect) {
      onSelect(item.key);
    }
  };
  
  // Render badge
  const renderBadge = (badge: boolean | number | string, badgeStyle?: StyleProp<ViewStyle>, badgeTextStyle?: StyleProp<TextStyle>) => {
    if (!badge) return null;
    
    const content = typeof badge === 'boolean' ? '' : badge;
    
    return (
      <View style={[styles.badge, badgeStyle]}>
        {typeof content !== 'string' && typeof content !== 'number' ? null : (
          <Text style={[styles.badgeText, badgeTextStyle]}>{content}</Text>
        )}
      </View>
    );
  };
  
  // Render a menu item
  const renderMenuItem = (item: MenuItem, index: number, isSubmenuItem: boolean = false) => {
    const isSelected = item.key === selectedKey;
    
    if (renderItem) {
      return renderItem(item, isSelected);
    }
    
    const isItemExpanded = isExpanded(item.key);
    const hasSubmenu = item.children && item.children.length > 0;
    
    return (
      <React.Fragment key={item.key}>
        <TouchableOpacity
          style={[
            position === 'left' || position === 'right'
              ? styles.verticalItem
              : styles.horizontalItem,
            isSubmenuItem && styles.submenuItem,
            isSelected && styles.selectedItem,
            isSelected && selectedItemStyle,
            item.disabled && styles.disabledItem,
            itemStyle,
          ]}
          onPress={() => handleItemPress(item)}
          disabled={item.disabled}
          activeOpacity={0.7}
        >
          {/* Active indicator */}
          {isSelected && (
            <View
              style={[
                styles.activeIndicator,
                position === 'top' && styles.activeIndicatorBottom,
                position === 'bottom' && styles.activeIndicatorTop,
                position === 'left' && styles.activeIndicatorRight,
                position === 'right' && styles.activeIndicatorLeft,
                { backgroundColor: activeIndicatorColor || variantStyle.activeIndicator.backgroundColor },
              ]}
            />
          )}
          
          {/* Icon */}
          {showIcons && item.icon && (
            <View style={styles.iconContainer}>
              <Icon
                name={item.icon}
                size={20}
                color={
                  item.disabled
                    ? '#ccc'
                    : isSelected
                      ? selectedIconColor || variantStyle.selectedIcon.color
                      : iconColor || variantStyle.icon.color
                }
              />
            </View>
          )}
          
          {/* Label */}
          {showLabels && (
            <Text
              style={[
                styles.itemText,
                { color: variantStyle.text.color },
                isSelected && { color: variantStyle.selectedText.color },
                isSelected && selectedItemTextStyle,
                item.disabled && styles.disabledText,
                itemTextStyle,
              ]}
              numberOfLines={1}
            >
              {item.label}
            </Text>
          )}
          
          {/* Badge */}
          {item.badge && renderBadge(item.badge, item.badgeStyle, item.badgeTextStyle)}
          
          {/* Submenu indicator */}
          {hasSubmenu && (
            <Icon
              name={isItemExpanded ? 'chevron-up' : 'chevron-down'}
              size={16}
              color={
                item.disabled
                  ? '#ccc'
                  : isSelected
                    ? selectedIconColor || variantStyle.selectedIcon.color
                    : iconColor || variantStyle.icon.color
              }
              style={styles.submenuIcon}
            />
          )}
        </TouchableOpacity>
        
        {/* Divider */}
        {showDividers && index < items.length - 1 && (
          <View
            style={[
              styles.divider,
              position === 'left' || position === 'right'
                ? styles.horizontalDivider
                : styles.verticalDivider,
            ]}
          />
        )}
        
        {/* Submenu */}
        {hasSubmenu && isItemExpanded && (
          <View style={styles.submenuContainer}>
            {item.children!.map((subItem, subIndex) => renderMenuItem(subItem, subIndex, true))}
          </View>
        )}
      </React.Fragment>
    );
  };
  
  // Get container style based on position
  const getContainerStyle = (): ViewStyle => {
    switch (position) {
      case 'top':
        return {
          flexDirection: 'row',
          borderBottomWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        };
      case 'bottom':
        return {
          flexDirection: 'row',
          borderTopWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        };
      case 'left':
        return {
          flexDirection: 'column',
          borderRightWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        };
      case 'right':
        return {
          flexDirection: 'column',
          borderLeftWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        };
      default:
        return {
          flexDirection: 'row',
          borderTopWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        };
    }
  };
  
  // Determine if the menu is horizontal or vertical
  const isHorizontal = position === 'top' || position === 'bottom';
  
  return (
    <View
      style={[
        styles.container,
        getContainerStyle(),
        { backgroundColor: backgroundColor || variantStyle.container.backgroundColor },
        { borderColor: '#e8e8e8' },
        showShadow && styles.shadow,
        containerStyle,
      ]}
    >
      {isHorizontal ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {items.map((item, index) => renderMenuItem(item, index))}
        </ScrollView>
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {items.map((item, index) => renderMenuItem(item, index))}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 4,
  },
  scrollContent: {
    flexGrow: 1,
  },
  horizontalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'relative',
  },
  verticalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'relative',
  },
  submenuItem: {
    paddingLeft: 32,
  },
  selectedItem: {
    backgroundColor: 'rgba(24, 144, 255, 0.1)',
  },
  disabledItem: {
    opacity: 0.5,
  },
  iconContainer: {
    marginRight: 8,
  },
  itemText: {
    fontSize: 14,
  },
  disabledText: {
    color: '#ccc',
  },
  badge: {
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#ff4d4f',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  submenuIcon: {
    marginLeft: 4,
  },
  divider: {
    backgroundColor: '#f0f0f0',
  },
  horizontalDivider: {
    height: '80%',
    width: StyleSheet.hairlineWidth,
  },
  verticalDivider: {
    width: '100%',
    height: StyleSheet.hairlineWidth,
  },
  submenuContainer: {
    width: '100%',
  },
  activeIndicator: {
    position: 'absolute',
    backgroundColor: '#1890ff',
  },
  activeIndicatorTop: {
    top: 0,
    left: 0,
    right: 0,
    height: 2,
  },
  activeIndicatorBottom: {
    bottom: 0,
    left: 0,
    right: 0,
    height: 2,
  },
  activeIndicatorLeft: {
    left: 0,
    top: 0,
    bottom: 0,
    width: 2,
  },
  activeIndicatorRight: {
    right: 0,
    top: 0,
    bottom: 0,
    width: 2,
  },
});

export default MenuBar;
