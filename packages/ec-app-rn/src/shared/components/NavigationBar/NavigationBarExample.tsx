import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Image,
  TextInput,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import NavigationBar, { NavigationBarStyle } from './NavigationBar';

const BACKGROUND_IMAGE = 'https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80';

const NavigationBarExample: React.FC = () => {
  const navigation = useNavigation();
  const [showShadow, setShowShadow] = useState(true);
  const [showBorder, setShowBorder] = useState(false);
  const [showBackButton, setShowBackButton] = useState(true);
  const [showSearch, setShowSearch] = useState(false);
  const [showSubtitle, setShowSubtitle] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<NavigationBarStyle>('default');
  
  const handleBackPress = () => {
    navigation.goBack();
  };
  
  const renderStyleOption = (style: NavigationBarStyle, label: string) => (
    <TouchableOpacity
      style={[
        styles.styleOption,
        selectedStyle === style && styles.selectedStyleOption,
      ]}
      onPress={() => setSelectedStyle(style)}
    >
      <Text
        style={[
          styles.styleOptionText,
          selectedStyle === style && styles.selectedStyleOptionText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
  
  // Custom render functions for specific styles
  const renderGradientNavBar = () => (
    <View style={styles.gradientContainer}>
      <View style={styles.gradientBackground} />
      <NavigationBar
        style="gradient"
        title="渐变导航栏"
        subtitle={showSubtitle ? "Gradient Style" : undefined}
        showBack={showBackButton}
        showSearch={showSearch}
        showShadow={showShadow}
        showBorder={showBorder}
        onBackPress={handleBackPress}
        backgroundColor="transparent"
      />
    </View>
  );
  
  const renderBlurNavBar = () => (
    <View style={styles.blurContainer}>
      <ImageBackground
        source={{ uri: BACKGROUND_IMAGE }}
        style={styles.backgroundImage}
      >
        <View style={styles.blurOverlay}>
          <NavigationBar
            style="blur"
            title="模糊导航栏"
            subtitle={showSubtitle ? "Blur Style" : undefined}
            showBack={showBackButton}
            showSearch={showSearch}
            showShadow={showShadow}
            showBorder={showBorder}
            onBackPress={handleBackPress}
            backgroundColor="rgba(255,255,255,0.7)"
          />
        </View>
      </ImageBackground>
    </View>
  );
  
  const renderFloatingNavBar = () => (
    <View style={styles.floatingContainer}>
      <NavigationBar
        style="floating"
        title="浮动导航栏"
        subtitle={showSubtitle ? "Floating Style" : undefined}
        showBack={showBackButton}
        showSearch={showSearch}
        showShadow={true} // Always show shadow for floating style
        showBorder={false} // No border for floating style
        onBackPress={handleBackPress}
      />
    </View>
  );
  
  // Render the appropriate navigation bar based on selected style
  const renderSelectedNavBar = () => {
    switch (selectedStyle) {
      case 'gradient':
        return renderGradientNavBar();
      case 'blur':
        return renderBlurNavBar();
      case 'floating':
        return renderFloatingNavBar();
      default:
        return (
          <NavigationBar
            style={selectedStyle}
            title={`${getStyleLabel(selectedStyle)}导航栏`}
            subtitle={showSubtitle ? `${getStyleLabel(selectedStyle)} Style` : undefined}
            showBack={showBackButton}
            showSearch={showSearch}
            showShadow={showShadow}
            showBorder={showBorder}
            onBackPress={handleBackPress}
            rightIcon="ellipsis-horizontal"
            onRightPress={() => {}}
          />
        );
    }
  };
  
  // Get style label for display
  const getStyleLabel = (style: NavigationBarStyle): string => {
    switch (style) {
      case 'default': return '默认';
      case 'transparent': return '透明';
      case 'primary': return '主要';
      case 'secondary': return '次要';
      case 'dark': return '深色';
      case 'light': return '浅色';
      case 'gradient': return '渐变';
      case 'blur': return '模糊';
      case 'floating': return '浮动';
      case 'compact': return '紧凑';
      default: return '';
    }
  };
  
  // Custom navigation bar examples
  const renderCustomExamples = () => (
    <>
      <Text style={styles.sectionTitle}>自定义导航栏示例</Text>
      
      {/* E-commerce search navigation bar */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>电商搜索导航栏</Text>
        <NavigationBar
          style="light"
          showBorder
          renderLeft={() => (
            <TouchableOpacity style={styles.iconButton}>
              <Image 
                source={{ uri: 'https://via.placeholder.com/30' }} 
                style={styles.avatar} 
              />
            </TouchableOpacity>
          )}
          renderCenter={() => (
            <TouchableOpacity style={styles.searchBar}>
              <View style={styles.searchIconContainer}>
                <View style={styles.searchIcon} />
              </View>
              <Text style={styles.searchText}>搜索商品</Text>
            </TouchableOpacity>
          )}
          renderRight={() => (
            <View style={styles.rightButtons}>
              <TouchableOpacity style={styles.iconButton}>
                <View style={styles.messageIcon}>
                  <View style={styles.messageBadge} />
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconButton}>
                <View style={styles.cartIcon}>
                  <View style={styles.cartBadge}>
                    <Text style={styles.cartBadgeText}>2</Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          )}
        />
      </View>
      
      {/* Social media navigation bar */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>社交媒体导航栏</Text>
        <NavigationBar
          style="light"
          renderLeft={() => (
            <TouchableOpacity style={styles.iconButton}>
              <View style={styles.cameraIcon} />
            </TouchableOpacity>
          )}
          renderCenter={() => (
            <View style={styles.socialTitle}>
              <Text style={styles.socialTitleText}>社交动态</Text>
            </View>
          )}
          renderRight={() => (
            <TouchableOpacity style={styles.iconButton}>
              <View style={styles.directIcon} />
            </TouchableOpacity>
          )}
        />
      </View>
      
      {/* News navigation bar */}
      <View style={styles.exampleContainer}>
        <Text style={styles.exampleTitle}>新闻导航栏</Text>
        <NavigationBar
          style="light"
          showBorder
          renderLeft={() => (
            <TouchableOpacity style={styles.iconButton}>
              <View style={styles.menuIcon} />
            </TouchableOpacity>
          )}
          renderCenter={() => (
            <View style={styles.tabsContainer}>
              <TouchableOpacity style={styles.tabActive}>
                <Text style={styles.tabTextActive}>推荐</Text>
                <View style={styles.tabIndicator} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.tab}>
                <Text style={styles.tabText}>热门</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.tab}>
                <Text style={styles.tabText}>关注</Text>
              </TouchableOpacity>
            </View>
          )}
          renderRight={() => (
            <TouchableOpacity style={styles.iconButton}>
              <View style={styles.searchIcon} />
            </TouchableOpacity>
          )}
        />
      </View>
    </>
  );
  
  return (
    <View style={styles.container}>
      {renderSelectedNavBar()}
      
      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>导航栏样式</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.styleOptions}>
          {renderStyleOption('default', '默认')}
          {renderStyleOption('transparent', '透明')}
          {renderStyleOption('primary', '主要')}
          {renderStyleOption('secondary', '次要')}
          {renderStyleOption('dark', '深色')}
          {renderStyleOption('light', '浅色')}
          {renderStyleOption('gradient', '渐变')}
          {renderStyleOption('blur', '模糊')}
          {renderStyleOption('floating', '浮动')}
          {renderStyleOption('compact', '紧凑')}
        </ScrollView>
        
        <Text style={styles.sectionTitle}>配置选项</Text>
        <View style={styles.optionsContainer}>
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示阴影</Text>
            <Switch
              value={showShadow}
              onValueChange={setShowShadow}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示边框</Text>
            <Switch
              value={showBorder}
              onValueChange={setShowBorder}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示返回按钮</Text>
            <Switch
              value={showBackButton}
              onValueChange={setShowBackButton}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示搜索框</Text>
            <Switch
              value={showSearch}
              onValueChange={setShowSearch}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={styles.optionLabel}>显示副标题</Text>
            <Switch
              value={showSubtitle}
              onValueChange={setShowSubtitle}
              trackColor={{ false: '#d9d9d9', true: '#1890ff' }}
              thumbColor="#fff"
            />
          </View>
        </View>
        
        {renderCustomExamples()}
        
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>使用说明</Text>
          <Text style={styles.infoText}>1. 基础用法：{'<NavigationBar title="标题" />'}</Text>
          <Text style={styles.infoText}>2. 设置样式：{'<NavigationBar style="primary" title="标题" />'}</Text>
          <Text style={styles.infoText}>3. 添加返回按钮：{'<NavigationBar showBack onBackPress={handleBack} />'}</Text>
          <Text style={styles.infoText}>4. 添加图标：{'<NavigationBar leftIcon="menu" rightIcon="more" />'}</Text>
          <Text style={styles.infoText}>5. 自定义渲染：{'<NavigationBar renderLeft={() => <CustomComponent />} />'}</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  gradientContainer: {
    width: '100%',
    position: 'relative',
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1890ff',
    // Simple gradient simulation with a single color
    borderRightWidth: 200,
    borderRightColor: '#722ed1',
  },
  blurContainer: {
    width: '100%',
    overflow: 'hidden',
  },
  backgroundImage: {
    width: '100%',
    height: 200,
  },
  blurOverlay: {
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  floatingContainer: {
    width: '100%',
    backgroundColor: '#f8f8f8',
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 16,
    color: '#333',
  },
  styleOptions: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  styleOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  selectedStyleOption: {
    backgroundColor: '#1890ff',
  },
  styleOptionText: {
    fontSize: 14,
    color: '#666',
  },
  selectedStyleOptionText: {
    color: '#fff',
  },
  optionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#f0f0f0',
  },
  optionLabel: {
    fontSize: 16,
    color: '#333',
  },
  exampleContainer: {
    marginBottom: 24,
  },
  exampleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#666',
  },
  // Custom navigation bar styles
  iconButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
  },
  searchBar: {
    flex: 1,
    height: 36,
    backgroundColor: '#f5f5f5',
    borderRadius: 18,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  searchIconContainer: {
    width: 16,
    height: 16,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchIcon: {
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 1.5,
    borderColor: '#999',
  },
  searchText: {
    fontSize: 14,
    color: '#999',
  },
  rightButtons: {
    flexDirection: 'row',
  },
  messageIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
  },
  messageBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ff4d4f',
  },
  cartIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#ff4d4f',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  cameraIcon: {
    width: 24,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#666',
  },
  socialTitle: {
    flex: 1,
    alignItems: 'center',
  },
  socialTitleText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  directIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#666',
  },
  menuIcon: {
    width: 20,
    height: 14,
    justifyContent: 'space-between',
  },
  tabsContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  tabActive: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 16,
    color: '#999',
  },
  tabTextActive: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    width: 20,
    height: 2,
    backgroundColor: '#1890ff',
    borderRadius: 1,
  },
  infoBox: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#f0f5ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#1890ff',
    marginBottom: 32,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#666',
  },
});

export default NavigationBarExample;
