import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export type NavigationBarStyle = 
  | 'default'
  | 'transparent'
  | 'primary'
  | 'secondary'
  | 'dark'
  | 'light'
  | 'gradient'
  | 'blur'
  | 'floating'
  | 'compact';

export interface NavigationBarProps {
  /**
   * Title text to display
   */
  title?: string;
  
  /**
   * Subtitle text to display
   */
  subtitle?: string;
  
  /**
   * Style variant of the navigation bar
   */
  style?: NavigationBarStyle;
  
  /**
   * Left button icon name (from Ionicons)
   */
  leftIcon?: string;
  
  /**
   * Right button icon name (from Ionicons)
   */
  rightIcon?: string;
  
  /**
   * Whether to show a back button
   */
  showBack?: boolean;
  
  /**
   * Whether to show a search input
   */
  showSearch?: boolean;
  
  /**
   * Whether to show a shadow
   */
  showShadow?: boolean;
  
  /**
   * Whether to show a border
   */
  showBorder?: boolean;
  
  /**
   * Custom background color
   */
  backgroundColor?: string;
  
  /**
   * Custom text color
   */
  textColor?: string;
  
  /**
   * Custom icon color
   */
  iconColor?: string;
  
  /**
   * Custom height
   */
  height?: number;
  
  /**
   * Custom styles for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom styles for the title
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom styles for the subtitle
   */
  subtitleStyle?: StyleProp<TextStyle>;
  
  /**
   * Callback when left button is pressed
   */
  onLeftPress?: () => void;
  
  /**
   * Callback when right button is pressed
   */
  onRightPress?: () => void;
  
  /**
   * Callback when back button is pressed
   */
  onBackPress?: () => void;
  
  /**
   * Callback when search input changes
   */
  onSearchChange?: (text: string) => void;
  
  /**
   * Custom render function for the left side
   */
  renderLeft?: () => React.ReactNode;
  
  /**
   * Custom render function for the center
   */
  renderCenter?: () => React.ReactNode;
  
  /**
   * Custom render function for the right side
   */
  renderRight?: () => React.ReactNode;
}

const NavigationBar: React.FC<NavigationBarProps> = ({
  title = '',
  subtitle,
  style = 'default',
  leftIcon,
  rightIcon,
  showBack = false,
  showSearch = false,
  showShadow = true,
  showBorder = false,
  backgroundColor,
  textColor,
  iconColor,
  height,
  containerStyle,
  titleStyle,
  subtitleStyle,
  onLeftPress,
  onRightPress,
  onBackPress,
  onSearchChange,
  renderLeft,
  renderCenter,
  renderRight,
}) => {
  const insets = useSafeAreaInsets();
  
  // Get style based on variant
  const getStyleForVariant = (): {
    container: ViewStyle;
    text: TextStyle;
    icon: TextStyle;
  } => {
    switch (style) {
      case 'transparent':
        return {
          container: {
            backgroundColor: 'transparent',
          },
          text: {
            color: '#000',
          },
          icon: {
            color: '#000',
          },
        };
      case 'primary':
        return {
          container: {
            backgroundColor: '#1890ff',
          },
          text: {
            color: '#fff',
          },
          icon: {
            color: '#fff',
          },
        };
      case 'secondary':
        return {
          container: {
            backgroundColor: '#f5f5f5',
          },
          text: {
            color: '#333',
          },
          icon: {
            color: '#666',
          },
        };
      case 'dark':
        return {
          container: {
            backgroundColor: '#222',
          },
          text: {
            color: '#fff',
          },
          icon: {
            color: '#fff',
          },
        };
      case 'light':
        return {
          container: {
            backgroundColor: '#fff',
          },
          text: {
            color: '#333',
          },
          icon: {
            color: '#666',
          },
        };
      case 'gradient':
        return {
          container: {
            backgroundColor: '#1890ff', // Gradient is applied in render
          },
          text: {
            color: '#fff',
          },
          icon: {
            color: '#fff',
          },
        };
      case 'blur':
        return {
          container: {
            backgroundColor: 'rgba(255, 255, 255, 0.8)', // Blur is applied in render
          },
          text: {
            color: '#333',
          },
          icon: {
            color: '#666',
          },
        };
      case 'floating':
        return {
          container: {
            backgroundColor: '#fff',
            marginHorizontal: 16,
            marginTop: 8,
            borderRadius: 8,
          },
          text: {
            color: '#333',
          },
          icon: {
            color: '#666',
          },
        };
      case 'compact':
        return {
          container: {
            backgroundColor: '#fff',
            height: 44,
          },
          text: {
            color: '#333',
            fontSize: 16,
          },
          icon: {
            color: '#666',
          },
        };
      case 'default':
      default:
        return {
          container: {
            backgroundColor: '#fff',
          },
          text: {
            color: '#333',
          },
          icon: {
            color: '#666',
          },
        };
    }
  };
  
  const variantStyle = getStyleForVariant();
  const navBarHeight = height || (style === 'compact' ? 44 : 56);
  
  // Render the left side of the navigation bar
  const renderLeftSide = () => {
    if (renderLeft) {
      return renderLeft();
    }
    
    if (showBack) {
      return (
        <TouchableOpacity
          style={styles.button}
          onPress={onBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon
            name="chevron-back"
            size={24}
            color={iconColor || variantStyle.icon.color}
          />
        </TouchableOpacity>
      );
    }
    
    if (leftIcon) {
      return (
        <TouchableOpacity
          style={styles.button}
          onPress={onLeftPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon
            name={leftIcon}
            size={24}
            color={iconColor || variantStyle.icon.color}
          />
        </TouchableOpacity>
      );
    }
    
    return <View style={styles.button} />;
  };
  
  // Render the center of the navigation bar
  const renderCenterContent = () => {
    if (renderCenter) {
      return renderCenter();
    }
    
    if (showSearch) {
      // Simple search input placeholder - in a real app you'd implement a proper search input
      return (
        <View style={styles.searchContainer}>
          <Icon
            name="search"
            size={16}
            color={iconColor || variantStyle.icon.color}
            style={styles.searchIcon}
          />
          <Text style={[styles.searchPlaceholder, { color: variantStyle.text.color }]}>
            搜索
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.titleContainer}>
        <Text
          style={[
            styles.title,
            { color: textColor || variantStyle.text.color },
            titleStyle,
          ]}
          numberOfLines={1}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={[
              styles.subtitle,
              { color: textColor || variantStyle.text.color },
              subtitleStyle,
            ]}
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
      </View>
    );
  };
  
  // Render the right side of the navigation bar
  const renderRightSide = () => {
    if (renderRight) {
      return renderRight();
    }
    
    if (rightIcon) {
      return (
        <TouchableOpacity
          style={styles.button}
          onPress={onRightPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon
            name={rightIcon}
            size={24}
            color={iconColor || variantStyle.icon.color}
          />
        </TouchableOpacity>
      );
    }
    
    return <View style={styles.button} />;
  };
  
  // Determine status bar style based on navigation bar style
  const getStatusBarStyle = () => {
    switch (style) {
      case 'dark':
      case 'primary':
      case 'gradient':
        return 'light-content';
      default:
        return 'dark-content';
    }
  };
  
  return (
    <View
      style={[
        styles.container,
        variantStyle.container,
        {
          paddingTop: insets.top,
          height: navBarHeight + insets.top,
          backgroundColor: backgroundColor || variantStyle.container.backgroundColor,
        },
        showShadow && styles.shadow,
        showBorder && styles.border,
        containerStyle,
      ]}
    >
      <StatusBar
        barStyle={getStatusBarStyle()}
        backgroundColor="transparent"
        translucent
      />
      <View style={[styles.content, { height: navBarHeight }]}>
        {renderLeftSide()}
        {renderCenterContent()}
        {renderRightSide()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  shadow: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  border: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#e1e1e1',
  },
  button: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    marginTop: 2,
    opacity: 0.7,
    textAlign: 'center',
  },
  searchContainer: {
    flex: 1,
    height: 36,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
    borderRadius: 10,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 6,
  },
  searchPlaceholder: {
    fontSize: 16,
    opacity: 0.5,
  },
});

export default NavigationBar;
