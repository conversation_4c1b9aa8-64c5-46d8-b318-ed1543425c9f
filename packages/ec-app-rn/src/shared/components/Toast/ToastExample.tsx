import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { ToastProvider, useToast, ToastPosition } from './index';

// Component that uses the toast
const ToastDemo = () => {
  const toast = useToast();
  const [loadingToastKey, setLoadingToastKey] = useState<string | null>(null);
  const [position, setPosition] = useState<ToastPosition>('center');
  
  // Show different types of toasts
  const showSuccessToast = () => {
    toast.success('操作成功！', { position });
  };
  
  const showErrorToast = () => {
    toast.error('操作失败，请重试', { position });
  };
  
  const showInfoToast = () => {
    toast.info('系统将于今晚23:00进行维护', { position });
  };
  
  const showWarningToast = () => {
    toast.warning('您的存储空间即将用完', { position });
  };
  
  const showCustomToast = () => {
    toast.show('自定义消息内容', {
      position,
      duration: 5000,
      icon: 'heart',
      closable: true,
    });
  };
  
  const showLoadingToast = () => {
    const key = toast.loading('加载中...', { position });
    setLoadingToastKey(key);
    
    // Simulate loading
    setTimeout(() => {
      if (loadingToastKey) {
        toast.hide(key);
        toast.success('加载完成！', { position });
        setLoadingToastKey(null);
      }
    }, 3000);
  };
  
  const hideAllToasts = () => {
    toast.hideAll();
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.positionSelector}>
        <Text style={styles.sectionTitle}>位置</Text>
        <View style={styles.positionButtons}>
          <TouchableOpacity
            style={[styles.positionButton, position === 'top' && styles.activeButton]}
            onPress={() => setPosition('top')}
          >
            <Text style={[styles.buttonText, position === 'top' && styles.activeButtonText]}>顶部</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.positionButton, position === 'center' && styles.activeButton]}
            onPress={() => setPosition('center')}
          >
            <Text style={[styles.buttonText, position === 'center' && styles.activeButtonText]}>中间</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.positionButton, position === 'bottom' && styles.activeButton]}
            onPress={() => setPosition('bottom')}
          >
            <Text style={[styles.buttonText, position === 'bottom' && styles.activeButtonText]}>底部</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.sectionTitle}>基础类型</Text>
      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.button, styles.successButton]} onPress={showSuccessToast}>
          <Text style={styles.buttonText}>成功</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.errorButton]} onPress={showErrorToast}>
          <Text style={styles.buttonText}>错误</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.button, styles.infoButton]} onPress={showInfoToast}>
          <Text style={styles.buttonText}>信息</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={showWarningToast}>
          <Text style={styles.buttonText}>警告</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.sectionTitle}>高级用法</Text>
      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.button, styles.customButton]} onPress={showCustomToast}>
          <Text style={styles.buttonText}>自定义</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.loadingButton]} onPress={showLoadingToast}>
          <Text style={styles.buttonText}>加载中</Text>
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity style={styles.hideAllButton} onPress={hideAllToasts}>
        <Text style={styles.hideAllButtonText}>隐藏所有</Text>
      </TouchableOpacity>
      
      <View style={styles.infoBox}>
        <Text style={styles.infoTitle}>Toast 使用指南</Text>
        <Text style={styles.infoText}>1. 导入 ToastProvider 并包裹应用根组件</Text>
        <Text style={styles.infoText}>2. 使用 useToast() hook 获取 toast 实例</Text>
        <Text style={styles.infoText}>3. 调用 toast.success(), toast.error() 等方法显示不同类型的提示</Text>
        <Text style={styles.infoText}>4. 使用 toast.hide(key) 手动隐藏特定的 toast</Text>
        <Text style={styles.infoText}>5. 使用 toast.hideAll() 隐藏所有 toast</Text>
      </View>
    </View>
  );
};

// Wrap the example with ToastProvider
const ToastExample = () => {
  return (
    <ToastProvider>
      <ScrollView style={styles.scrollView}>
        <ToastDemo />
      </ScrollView>
    </ToastProvider>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  button: {
    flex: 1,
    height: 44,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  successButton: {
    backgroundColor: '#52c41a',
  },
  errorButton: {
    backgroundColor: '#ff4d4f',
  },
  infoButton: {
    backgroundColor: '#1890ff',
  },
  warningButton: {
    backgroundColor: '#faad14',
  },
  customButton: {
    backgroundColor: '#722ed1',
  },
  loadingButton: {
    backgroundColor: '#13c2c2',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  hideAllButton: {
    height: 44,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#8c8c8c',
    marginTop: 8,
    marginHorizontal: 4,
  },
  hideAllButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  positionSelector: {
    marginBottom: 20,
  },
  positionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  positionButton: {
    flex: 1,
    height: 36,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    marginHorizontal: 4,
  },
  activeButton: {
    backgroundColor: '#1890ff',
  },
  activeButtonText: {
    color: '#fff',
  },
  infoBox: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#1890ff',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#666',
  },
});

export default ToastExample;
