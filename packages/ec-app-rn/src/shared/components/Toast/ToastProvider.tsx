import React, { createContext, useContext, useState, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import Toast, { ToastProps, ToastType, ToastPosition } from './Toast';

interface ToastOptions extends Omit<ToastProps, 'message'> {
  key?: string;
}

interface ToastInstance extends ToastProps {
  key: string;
}

interface ToastContextProps {
  show: (message: string, options?: ToastOptions) => string;
  success: (message: string, options?: ToastOptions) => string;
  error: (message: string, options?: ToastOptions) => string;
  info: (message: string, options?: ToastOptions) => string;
  warning: (message: string, options?: ToastOptions) => string;
  loading: (message: string, options?: ToastOptions) => string;
  hide: (key: string) => void;
  hideAll: () => void;
}

const ToastContext = createContext<ToastContextProps | null>(null);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export interface ToastProviderProps {
  children: React.ReactNode;
  maxCount?: number;
  defaultDuration?: number;
  defaultPosition?: ToastPosition;
}

let uniqueId = 0;
const getUniqueId = () => {
  return `toast-${uniqueId++}`;
};

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  maxCount = 5,
  defaultDuration = 3000,
  defaultPosition = 'center',
}) => {
  const [toasts, setToasts] = useState<ToastInstance[]>([]);

  const remove = useCallback((key: string) => {
    setToasts(prev => prev.filter(toast => toast.key !== key));
  }, []);

  const show = useCallback((message: string, options: ToastOptions = {}) => {
    const key = options.key || getUniqueId();
    
    setToasts(prev => {
      // Remove if key already exists
      const filtered = prev.filter(toast => toast.key !== key);
      
      // Add new toast
      // Extract options without key to avoid duplicate key issue
      const { key: optionsKey, ...restOptions } = options;
      
      const newToast: ToastInstance = {
        message,
        key,
        duration: options.duration ?? defaultDuration,
        position: options.position ?? defaultPosition,
        ...restOptions,
        onClose: () => {
          options.onClose?.();
          remove(key);
        },
      };
      
      // Limit the number of toasts
      const result = [...filtered, newToast];
      if (result.length > maxCount) {
        return result.slice(result.length - maxCount);
      }
      
      return result;
    });
    
    return key;
  }, [defaultDuration, defaultPosition, maxCount, remove]);

  const success = useCallback((message: string, options: ToastOptions = {}) => {
    return show(message, { ...options, type: 'success' });
  }, [show]);

  const error = useCallback((message: string, options: ToastOptions = {}) => {
    return show(message, { ...options, type: 'error' });
  }, [show]);

  const info = useCallback((message: string, options: ToastOptions = {}) => {
    return show(message, { ...options, type: 'info' });
  }, [show]);

  const warning = useCallback((message: string, options: ToastOptions = {}) => {
    return show(message, { ...options, type: 'warning' });
  }, [show]);

  const loading = useCallback((message: string, options: ToastOptions = {}) => {
    return show(message, { ...options, type: 'loading', duration: 0 });
  }, [show]);

  const hide = useCallback((key: string) => {
    remove(key);
  }, [remove]);

  const hideAll = useCallback(() => {
    setToasts([]);
  }, []);

  const contextValue = {
    show,
    success,
    error,
    info,
    warning,
    loading,
    hide,
    hideAll,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <View style={styles.toastContainer} pointerEvents="box-none">
        {toasts.map(toast => {
          // Extract key from toast to avoid duplicate key warning
          const { key, ...toastProps } = toast;
          return <Toast key={key} {...toastProps} />;
        })}
      </View>
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'box-none',
  },
});

export default ToastProvider;
