import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Animated,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  StyleProp,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

const { width } = Dimensions.get('window');

export type ToastType = 'success' | 'error' | 'info' | 'warning' | 'loading';
export type ToastPosition = 'top' | 'bottom' | 'center';

export interface ToastProps {
  /**
   * Toast message content
   */
  message: string;
  
  /**
   * Toast type
   */
  type?: ToastType;
  
  /**
   * Toast position on screen
   */
  position?: ToastPosition;
  
  /**
   * Duration in milliseconds before auto hiding
   * Set to 0 to disable auto hiding
   */
  duration?: number;
  
  /**
   * Whether to show an icon based on type
   */
  showIcon?: boolean;
  
  /**
   * Custom icon name from Ionicons
   */
  icon?: string;
  
  /**
   * Whether to show a close button
   */
  closable?: boolean;
  
  /**
   * Callback when toast is closed
   */
  onClose?: () => void;
  
  /**
   * Custom container style
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom text style
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom mask style
   */
  maskStyle?: StyleProp<ViewStyle>;
  
  /**
   * Whether to show a mask behind the toast
   */
  mask?: boolean;
}

const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  position = 'center',
  duration = 3000,
  showIcon = true,
  icon,
  closable = false,
  onClose,
  style,
  textStyle,
  maskStyle,
  mask = false,
}) => {
  const [visible, setVisible] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  // Define icon based on type
  const getIcon = () => {
    if (icon) return icon;
    
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'warning':
        return 'warning';
      case 'loading':
        return 'reload';
      case 'info':
      default:
        return 'information-circle';
    }
  };
  
  // Define icon color based on type
  const getIconColor = () => {
    switch (type) {
      case 'success':
        return '#52c41a';
      case 'error':
        return '#ff4d4f';
      case 'warning':
        return '#faad14';
      case 'loading':
        return '#1890ff';
      case 'info':
      default:
        return '#1890ff';
    }
  };
  
  // Handle close
  const handleClose = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setVisible(false);
      if (onClose) onClose();
    });
  };
  
  // Fade in animation
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    // Auto hide after duration
    if (duration > 0 && type !== 'loading') {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, []);
  
  // Get position style
  const getPositionStyle = (): ViewStyle => {
    switch (position) {
      case 'top':
        return { top: 50 };
      case 'bottom':
        return { bottom: 50 };
      case 'center':
      default:
        return { top: '40%' };
    }
  };
  
  // Render loading icon with animation
  const renderLoadingIcon = () => {
    const spinValue = useRef(new Animated.Value(0)).current;
    
    useEffect(() => {
      Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ).start();
    }, []);
    
    const spin = spinValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });
    
    return (
      <Animated.View style={{ transform: [{ rotate: spin }] }}>
        <Icon name="reload" size={20} color="#fff" />
      </Animated.View>
    );
  };
  
  if (!visible) return null;
  
  return (
    <>
      {mask && (
        <View
          style={[
            styles.mask,
            maskStyle,
          ]}
        />
      )}
      
      <Animated.View
        style={[
          styles.container,
          styles[`${type}Container`],
          getPositionStyle(),
          { opacity: fadeAnim },
          style,
        ]}
      >
        {showIcon && (
          <View style={styles.iconContainer}>
            {type === 'loading' ? (
              renderLoadingIcon()
            ) : (
              <Icon name={getIcon()} size={20} color={getIconColor()} />
            )}
          </View>
        )}
        
        <Text style={[styles.text, textStyle]}>{message}</Text>
        
        {closable && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
          >
            <Icon name="close" size={16} color="#999" />
          </TouchableOpacity>
        )}
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
    minWidth: 120,
    maxWidth: width - 40,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 4,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    elevation: 10,
  },
  successContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  errorContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  warningContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  infoContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  loadingContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  iconContainer: {
    marginRight: 8,
  },
  text: {
    color: '#fff',
    fontSize: 14,
  },
  closeButton: {
    marginLeft: 8,
  },
  mask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 999,
  },
});

export default Toast;
