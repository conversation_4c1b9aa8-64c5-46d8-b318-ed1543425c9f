import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import Loading from './Loading';

const LoadingExample = () => {
  const [loading, setLoading] = useState(true);
  const [fullScreenLoading, setFullScreenLoading] = useState(false);
  const [loadingType, setLoadingType] = useState<'spinner' | 'circular' | 'dots' | 'pulse' | 'wave'>('spinner');
  
  // Toggle loading state
  const toggleLoading = () => {
    setLoading(prev => !prev);
  };
  
  // Toggle full screen loading
  const showFullScreenLoading = () => {
    setFullScreenLoading(true);
    
    // Auto hide after 3 seconds
    setTimeout(() => {
      setFullScreenLoading(false);
    }, 3000);
  };
  
  // Change loading type
  const changeLoadingType = (type: 'spinner' | 'circular' | 'dots' | 'pulse' | 'wave') => {
    setLoadingType(type);
  };
  
  // Simulate async loading
  const simulateAsyncLoading = () => {
    setLoading(true);
    
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>基础加载指示器</Text>
      <View style={styles.section}>
        <Loading loading={loading} />
        
        <TouchableOpacity
          style={styles.button}
          onPress={toggleLoading}
        >
          <Text style={styles.buttonText}>
            {loading ? '隐藏' : '显示'}加载指示器
          </Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.title}>不同类型</Text>
      <View style={styles.section}>
        <View style={styles.typesContainer}>
          <View style={styles.typeItem}>
            <Loading type="spinner" />
            <Text style={styles.typeLabel}>Spinner</Text>
          </View>
          
          <View style={styles.typeItem}>
            <Loading type="circular" />
            <Text style={styles.typeLabel}>Circular</Text>
          </View>
          
          <View style={styles.typeItem}>
            <Loading type="dots" />
            <Text style={styles.typeLabel}>Dots</Text>
          </View>
          
          <View style={styles.typeItem}>
            <Loading type="pulse" />
            <Text style={styles.typeLabel}>Pulse</Text>
          </View>
          
          <View style={styles.typeItem}>
            <Loading type="wave" />
            <Text style={styles.typeLabel}>Wave</Text>
          </View>
        </View>
        
        <View style={styles.typeButtons}>
          <TouchableOpacity
            style={[styles.typeButton, loadingType === 'spinner' && styles.activeTypeButton]}
            onPress={() => changeLoadingType('spinner')}
          >
            <Text style={styles.typeButtonText}>Spinner</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.typeButton, loadingType === 'circular' && styles.activeTypeButton]}
            onPress={() => changeLoadingType('circular')}
          >
            <Text style={styles.typeButtonText}>Circular</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.typeButton, loadingType === 'dots' && styles.activeTypeButton]}
            onPress={() => changeLoadingType('dots')}
          >
            <Text style={styles.typeButtonText}>Dots</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.typeButton, loadingType === 'pulse' && styles.activeTypeButton]}
            onPress={() => changeLoadingType('pulse')}
          >
            <Text style={styles.typeButtonText}>Pulse</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.typeButton, loadingType === 'wave' && styles.activeTypeButton]}
            onPress={() => changeLoadingType('wave')}
          >
            <Text style={styles.typeButtonText}>Wave</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.demoContainer}>
          <Loading type={loadingType} size="large" />
        </View>
      </View>
      
      <Text style={styles.title}>不同尺寸</Text>
      <View style={styles.section}>
        <View style={styles.sizesContainer}>
          <View style={styles.sizeItem}>
            <Loading size="small" />
            <Text style={styles.sizeLabel}>小</Text>
          </View>
          
          <View style={styles.sizeItem}>
            <Loading size="default" />
            <Text style={styles.sizeLabel}>中</Text>
          </View>
          
          <View style={styles.sizeItem}>
            <Loading size="large" />
            <Text style={styles.sizeLabel}>大</Text>
          </View>
        </View>
      </View>
      
      <Text style={styles.title}>带文本的加载指示器</Text>
      <View style={styles.section}>
        <Loading text="加载中..." />
        
        <View style={styles.spacer} />
        
        <Loading text="加载中..." indicatorPosition="right" />
        
        <View style={styles.spacer} />
        
        <Loading text="加载中..." indicatorPosition="top" />
        
        <View style={styles.spacer} />
        
        <Loading text="加载中..." indicatorPosition="bottom" />
      </View>
      
      <Text style={styles.title}>自定义颜色</Text>
      <View style={styles.section}>
        <View style={styles.colorsContainer}>
          <Loading color="#1890FF" />
          <Loading color="#52C41A" />
          <Loading color="#722ED1" />
          <Loading color="#FAAD14" />
        </View>
      </View>
      
      <Text style={styles.title}>全屏加载</Text>
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.button}
          onPress={showFullScreenLoading}
        >
          <Text style={styles.buttonText}>显示全屏加载</Text>
        </TouchableOpacity>
        
        <Loading
          loading={fullScreenLoading}
          fullScreen
          text="加载中..."
          type="circular"
          size="large"
        />
      </View>
      
      <Text style={styles.title}>延迟显示</Text>
      <View style={styles.section}>
        <Loading delay={1000} text="延迟1秒显示" />
      </View>
      
      <Text style={styles.title}>应用场景：数据加载</Text>
      <View style={styles.section}>
        <View style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>热门商品</Text>
            <TouchableOpacity onPress={simulateAsyncLoading}>
              <Text style={styles.refreshText}>刷新</Text>
            </TouchableOpacity>
          </View>
          
          {loading ? (
            <View style={styles.cardLoading}>
              <Loading text="加载商品数据..." />
            </View>
          ) : (
            <View style={styles.productList}>
              <View style={styles.productItem}>
                <View style={styles.productImage} />
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>无线蓝牙耳机</Text>
                  <Text style={styles.productPrice}>¥299.00</Text>
                </View>
              </View>
              
              <View style={styles.productItem}>
                <View style={styles.productImage} />
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>智能手表</Text>
                  <Text style={styles.productPrice}>¥1299.00</Text>
                </View>
              </View>
              
              <View style={styles.productItem}>
                <View style={styles.productImage} />
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>手机保护壳</Text>
                  <Text style={styles.productPrice}>¥49.00</Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </View>
      
      <Text style={styles.title}>应用场景：按钮加载状态</Text>
      <View style={styles.section}>
        <TouchableOpacity style={styles.loadingButton}>
          <Loading size="small" color="#fff" />
          <Text style={styles.loadingButtonText}>提交中...</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  button: {
    backgroundColor: '#FF4D4F',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 15,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
  },
  typesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  typeItem: {
    alignItems: 'center',
    width: '18%',
  },
  typeLabel: {
    fontSize: 12,
    marginTop: 5,
  },
  typeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 20,
    justifyContent: 'center',
  },
  typeButton: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginHorizontal: 4,
    marginBottom: 8,
  },
  activeTypeButton: {
    backgroundColor: '#FF4D4F',
  },
  typeButtonText: {
    fontSize: 12,
    color: '#333',
  },
  demoContainer: {
    alignItems: 'center',
    marginTop: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  sizesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  sizeItem: {
    alignItems: 'center',
  },
  sizeLabel: {
    fontSize: 12,
    marginTop: 5,
  },
  spacer: {
    height: 20,
  },
  colorsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  cardContainer: {
    borderWidth: 1,
    borderColor: '#f0f0f0',
    borderRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  refreshText: {
    fontSize: 12,
    color: '#1890FF',
  },
  cardLoading: {
    padding: 30,
    alignItems: 'center',
  },
  productList: {
    padding: 10,
  },
  productItem: {
    flexDirection: 'row',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  productImage: {
    width: 40,
    height: 40,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
  },
  productInfo: {
    marginLeft: 10,
    flex: 1,
  },
  productName: {
    fontSize: 14,
  },
  productPrice: {
    fontSize: 14,
    color: '#FF4D4F',
    marginTop: 4,
  },
  loadingButton: {
    backgroundColor: '#1890FF',
    paddingVertical: 12,
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingButtonText: {
    color: '#fff',
    marginLeft: 8,
  },
});

export default LoadingExample;
