import React, { useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Animated,
  Easing,
  ViewStyle,
  TextStyle,
  StyleProp,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export type LoadingSize = 'small' | 'default' | 'large';
export type LoadingType = 'spinner' | 'circular' | 'dots' | 'pulse' | 'wave';
export type LoadingIndicatorPosition = 'left' | 'right' | 'top' | 'bottom';

export interface LoadingProps {
  /**
   * Whether to show the loading indicator
   */
  loading?: boolean;
  
  /**
   * Size of the loading indicator
   */
  size?: LoadingSize;
  
  /**
   * Type of loading animation
   */
  type?: LoadingType;
  
  /**
   * Color of the loading indicator
   */
  color?: string;
  
  /**
   * Text to display with the loading indicator
   */
  text?: string;
  
  /**
   * Position of the loading indicator relative to text
   */
  indicatorPosition?: LoadingIndicatorPosition;
  
  /**
   * Whether to show the loading indicator in a full screen overlay
   */
  fullScreen?: boolean;
  
  /**
   * Background color for the full screen overlay
   */
  overlayColor?: string;
  
  /**
   * Opacity for the full screen overlay
   */
  overlayOpacity?: number;
  
  /**
   * Custom style for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for the indicator
   */
  indicatorStyle?: StyleProp<ViewStyle>;
  
  /**
   * Children to render inside the loading container
   */
  children?: React.ReactNode;
  
  /**
   * Whether to show a delay before displaying the loading indicator
   */
  delay?: number;
  
  /**
   * Custom icon name from Ionicons (for circular type)
   */
  customIcon?: string;
}

const Loading: React.FC<LoadingProps> = ({
  loading = true,
  size = 'default',
  type = 'spinner',
  color = '#FF4D4F',
  text,
  indicatorPosition = 'left',
  fullScreen = false,
  overlayColor = '#ffffff',
  overlayOpacity = 0.7,
  containerStyle,
  textStyle,
  indicatorStyle,
  children,
  delay = 0,
  customIcon,
}) => {
  // Animation values
  const spinValue = new Animated.Value(0);
  const scaleValue = new Animated.Value(0.8);
  const opacityValue = new Animated.Value(0);
  const dotValues = [
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ];
  const waveValues = [
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ];
  
  // Get size dimensions
  const getSizeDimensions = () => {
    switch (size) {
      case 'small':
        return {
          indicatorSize: 16,
          textSize: 12,
          spacing: 4,
          iconSize: 14,
          dotSize: 4,
          waveHeight: 12,
        };
      case 'large':
        return {
          indicatorSize: 36,
          textSize: 16,
          spacing: 12,
          iconSize: 32,
          dotSize: 8,
          waveHeight: 24,
        };
      case 'default':
      default:
        return {
          indicatorSize: 24,
          textSize: 14,
          spacing: 8,
          iconSize: 22,
          dotSize: 6,
          waveHeight: 18,
        };
    }
  };
  
  const sizeDimensions = getSizeDimensions();
  
  // Start animations
  useEffect(() => {
    if (loading) {
      // Delayed display
      const displayTimer = setTimeout(() => {
        // Fade in
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }).start();
        
        // Start specific animations based on type
        switch (type) {
          case 'circular':
            startSpinAnimation();
            break;
          case 'pulse':
            startPulseAnimation();
            break;
          case 'dots':
            startDotsAnimation();
            break;
          case 'wave':
            startWaveAnimation();
            break;
          case 'spinner':
          default:
            // Native spinner doesn't need animation
            break;
        }
      }, delay);
      
      return () => clearTimeout(displayTimer);
    } else {
      // Fade out
      Animated.timing(opacityValue, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [loading, type, delay]);
  
  // Spin animation for circular type
  const startSpinAnimation = () => {
    spinValue.setValue(0);
    Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  };
  
  // Pulse animation
  const startPulseAnimation = () => {
    scaleValue.setValue(0.8);
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 600,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 0.8,
          duration: 600,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };
  
  // Dots animation
  const startDotsAnimation = () => {
    const animations = dotValues.map((value, index) => {
      value.setValue(0);
      return Animated.sequence([
        Animated.delay(index * 150),
        Animated.timing(value, {
          toValue: 1,
          duration: 400,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(value, {
          toValue: 0,
          duration: 400,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ]);
    });
    
    Animated.loop(
      Animated.parallel(animations)
    ).start();
  };
  
  // Wave animation
  const startWaveAnimation = () => {
    const animations = waveValues.map((value, index) => {
      value.setValue(0);
      return Animated.sequence([
        Animated.delay(index * 120),
        Animated.timing(value, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(value, {
          toValue: 0,
          duration: 300,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ]);
    });
    
    Animated.loop(
      Animated.parallel(animations)
    ).start();
  };
  
  // Spin interpolation
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  // Render spinner type
  const renderSpinner = () => {
    return (
      <ActivityIndicator
        size={size === 'small' ? 'small' : 'large'}
        color={color}
        style={indicatorStyle}
      />
    );
  };
  
  // Render circular type
  const renderCircular = () => {
    return (
      <Animated.View
        style={[
          styles.circular,
          {
            width: sizeDimensions.indicatorSize,
            height: sizeDimensions.indicatorSize,
            borderWidth: size === 'small' ? 1.5 : 2,
            borderColor: color,
            transform: [{ rotate: spin }],
          },
          indicatorStyle,
        ]}
      >
        {customIcon && (
          <Icon
            name={customIcon}
            size={sizeDimensions.iconSize * 0.6}
            color={color}
            style={{ opacity: 0.7 }}
          />
        )}
      </Animated.View>
    );
  };
  
  // Render pulse type
  const renderPulse = () => {
    return (
      <Animated.View
        style={[
          styles.pulse,
          {
            width: sizeDimensions.indicatorSize,
            height: sizeDimensions.indicatorSize,
            backgroundColor: color,
            transform: [{ scale: scaleValue }],
          },
          indicatorStyle,
        ]}
      />
    );
  };
  
  // Render dots type
  const renderDots = () => {
    return (
      <View
        style={[
          styles.dots,
          { width: sizeDimensions.indicatorSize * 1.5 },
          indicatorStyle,
        ]}
      >
        {dotValues.map((value, index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                width: sizeDimensions.dotSize,
                height: sizeDimensions.dotSize,
                backgroundColor: color,
                opacity: value,
                transform: [
                  {
                    translateY: value.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [0, -sizeDimensions.dotSize, 0],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    );
  };
  
  // Render wave type
  const renderWave = () => {
    return (
      <View
        style={[
          styles.wave,
          { width: sizeDimensions.indicatorSize * 1.5 },
          indicatorStyle,
        ]}
      >
        {waveValues.map((value, index) => (
          <Animated.View
            key={index}
            style={[
              styles.waveBar,
              {
                width: sizeDimensions.dotSize / 2,
                height: value.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [
                    sizeDimensions.waveHeight * 0.3,
                    sizeDimensions.waveHeight,
                    sizeDimensions.waveHeight * 0.3,
                  ],
                }),
                backgroundColor: color,
                marginHorizontal: sizeDimensions.dotSize / 2,
              },
            ]}
          />
        ))}
      </View>
    );
  };
  
  // Render loading indicator based on type
  const renderLoadingIndicator = () => {
    switch (type) {
      case 'circular':
        return renderCircular();
      case 'pulse':
        return renderPulse();
      case 'dots':
        return renderDots();
      case 'wave':
        return renderWave();
      case 'spinner':
      default:
        return renderSpinner();
    }
  };
  
  // Get container direction based on indicator position
  const getContainerDirection = () => {
    switch (indicatorPosition) {
      case 'top':
      case 'bottom':
        return 'column';
      case 'left':
      case 'right':
      default:
        return 'row';
    }
  };
  
  // Get spacing style based on indicator position
  const getSpacingStyle = () => {
    if (!text) return {};
    
    switch (indicatorPosition) {
      case 'top':
        return { marginBottom: sizeDimensions.spacing };
      case 'bottom':
        return { marginTop: sizeDimensions.spacing };
      case 'left':
        return { marginRight: sizeDimensions.spacing };
      case 'right':
        return { marginLeft: sizeDimensions.spacing };
      default:
        return {};
    }
  };
  
  // Render content
  const renderContent = () => {
    const isVertical = indicatorPosition === 'top' || indicatorPosition === 'bottom';
    
    return (
      <Animated.View
        style={[
          styles.contentContainer,
          {
            flexDirection: getContainerDirection(),
            opacity: opacityValue,
          },
          fullScreen ? styles.fullScreenContent : {},
          containerStyle,
        ]}
      >
        {(indicatorPosition === 'left' || indicatorPosition === 'top') && (
          <View style={getSpacingStyle()}>
            {renderLoadingIndicator()}
          </View>
        )}
        
        {text && (
          <Text
            style={[
              styles.text,
              {
                fontSize: sizeDimensions.textSize,
                textAlign: isVertical ? 'center' : 'left',
              },
              textStyle,
            ]}
          >
            {text}
          </Text>
        )}
        
        {(indicatorPosition === 'right' || indicatorPosition === 'bottom') && (
          <View style={getSpacingStyle()}>
            {renderLoadingIndicator()}
          </View>
        )}
        
        {children}
      </Animated.View>
    );
  };
  
  // If not loading and not full screen, render nothing
  if (!loading && !fullScreen) {
    return null;
  }
  
  // Full screen overlay
  if (fullScreen) {
    return (
      <Animated.View
        style={[
          styles.fullScreenContainer,
          {
            backgroundColor: overlayColor,
            opacity: opacityValue.interpolate({
              inputRange: [0, 1],
              outputRange: [0, overlayOpacity],
            }),
          },
        ]}
      >
        {renderContent()}
      </Animated.View>
    );
  }
  
  // Regular loading indicator
  return renderContent();
};

const styles = StyleSheet.create({
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreenContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 999,
  },
  fullScreenContent: {
    backgroundColor: 'transparent',
  },
  text: {
    color: '#333',
  },
  circular: {
    borderRadius: 100,
    borderTopColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulse: {
    borderRadius: 100,
  },
  dots: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dot: {
    borderRadius: 100,
  },
  wave: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    height: 24,
  },
  waveBar: {
    borderRadius: 100,
  },
});

export default Loading;
