import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  StyleProp,
  FlatList,
  ListRenderItemInfo,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export type ListSize = 'small' | 'default' | 'large';
export type ListItemLayout = 'horizontal' | 'vertical';
export type ListBorderPosition = 'top' | 'bottom' | 'both' | 'none';

export interface ListItemProps {
  /**
   * Unique key for the item
   */
  key?: string;
  
  /**
   * Title of the list item
   */
  title?: string;
  
  /**
   * Description of the list item
   */
  description?: string;
  
  /**
   * Custom content to render in the list item
   */
  content?: React.ReactNode;
  
  /**
   * Icon name from Ionicons to display at the start of the item
   */
  icon?: string;
  
  /**
   * Icon color
   */
  iconColor?: string;
  
  /**
   * Avatar component or image source to display
   */
  avatar?: React.ReactNode;
  
  /**
   * Extra content to display at the end of the item
   */
  extra?: React.ReactNode;
  
  /**
   * Whether the item is clickable
   */
  clickable?: boolean;
  
  /**
   * Callback when the item is pressed
   */
  onPress?: () => void;
  
  /**
   * Whether to show a divider below this item
   */
  showDivider?: boolean;
  
  /**
   * Custom style for the item container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the title text
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for the description text
   */
  descriptionStyle?: StyleProp<TextStyle>;
  
  /**
   * Whether the item is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether to show a right arrow icon
   */
  arrow?: boolean;
  
  /**
   * Custom arrow icon name from Ionicons
   */
  arrowIcon?: string;
  
  /**
   * Whether the item is selected
   */
  selected?: boolean;
  
  /**
   * Custom selected style
   */
  selectedStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom data to pass to the item
   */
  itemData?: any;
}

export interface ListProps<T = any> {
  /**
   * Data array to render list items
   */
  data?: T[];
  
  /**
   * Header component or title string
   */
  header?: React.ReactNode | string;
  
  /**
   * Footer component or text string
   */
  footer?: React.ReactNode | string;
  
  /**
   * Size of the list items
   */
  size?: ListSize;
  
  /**
   * Layout direction of list items
   */
  layout?: ListItemLayout;
  
  /**
   * Whether to show borders
   */
  bordered?: boolean;
  
  /**
   * Position of the borders
   */
  borderPosition?: ListBorderPosition;
  
  /**
   * Whether to split list items with dividers
   */
  split?: boolean;
  
  /**
   * Custom render function for list items
   */
  renderItem?: (item: T, index: number) => React.ReactNode;
  
  /**
   * Custom style for the list container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for list items
   */
  itemStyle?: StyleProp<ViewStyle>;
  
  /**
   * Whether the list is loading
   */
  loading?: boolean;
  
  /**
   * Custom loading component
   */
  loadingComponent?: React.ReactNode;
  
  /**
   * Whether to show a load more footer
   */
  loadMore?: boolean;
  
  /**
   * Callback when load more is triggered
   */
  onLoadMore?: () => void;
  
  /**
   * Whether more data is loading
   */
  loadingMore?: boolean;
  
  /**
   * Whether all data has been loaded
   */
  allLoaded?: boolean;
  
  /**
   * Text to display when all data is loaded
   */
  allLoadedText?: string;
  
  /**
   * Whether the list is empty
   */
  empty?: boolean;
  
  /**
   * Component to display when the list is empty
   */
  emptyComponent?: React.ReactNode;
  
  /**
   * Text to display when the list is empty
   */
  emptyText?: string;
  
  /**
   * Whether to enable pull to refresh
   */
  refreshable?: boolean;
  
  /**
   * Callback when refresh is triggered
   */
  onRefresh?: () => void;
  
  /**
   * Whether the list is refreshing
   */
  refreshing?: boolean;
  
  /**
   * Key extractor function for list items
   */
  keyExtractor?: (item: T, index: number) => string;
  
  /**
   * Whether to show scrollbar
   */
  showsScrollIndicator?: boolean;
  
  /**
   * Number of columns for grid layout
   */
  numColumns?: number;
  
  /**
   * Callback when the end of the list is reached
   */
  onEndReached?: () => void;
  
  /**
   * How far from the end to trigger onEndReached
   */
  onEndReachedThreshold?: number;
  
  /**
   * Whether to invert the list
   */
  inverted?: boolean;
  
  /**
   * Custom ListHeaderComponent for FlatList
   */
  ListHeaderComponent?: React.ReactNode;
  
  /**
   * Custom ListFooterComponent for FlatList
   */
  ListFooterComponent?: React.ReactNode;
  
  /**
   * Custom ItemSeparatorComponent for FlatList
   */
  ItemSeparatorComponent?: React.ComponentType<any>;
  
  /**
   * Additional props for FlatList
   */
  flatListProps?: any;
  
  /**
   * Whether to render the list as a section list
   */
  sections?: boolean;
  
  /**
   * Children components
   */
  children?: React.ReactNode;
}

// List Item Component
export const ListItem: React.FC<ListItemProps> = ({
  title,
  description,
  content,
  icon,
  iconColor = '#1890FF',
  avatar,
  extra,
  clickable = false,
  onPress,
  showDivider = true,
  style,
  titleStyle,
  descriptionStyle,
  disabled = false,
  arrow = false,
  arrowIcon = 'chevron-forward',
  selected = false,
  selectedStyle,
}) => {
  // Get size-based styles
  const getItemStyles = () => {
    return {
      container: {
        opacity: disabled ? 0.5 : 1,
      },
    };
  };
  
  const itemStyles = getItemStyles();
  
  // Render item content
  const renderContent = () => {
    if (content) {
      return content;
    }
    
    return (
      <View style={styles.itemContentContainer}>
        {(icon || avatar) && (
          <View style={styles.itemIconContainer}>
            {icon && (
              <Icon name={icon} size={24} color={iconColor} style={styles.itemIcon} />
            )}
            {avatar && avatar}
          </View>
        )}
        
        <View style={styles.itemTextContainer}>
          {title && (
            <Text style={[styles.itemTitle, titleStyle]} numberOfLines={1}>
              {title}
            </Text>
          )}
          
          {description && (
            <Text style={[styles.itemDescription, descriptionStyle]} numberOfLines={2}>
              {description}
            </Text>
          )}
        </View>
        
        {(extra || arrow) && (
          <View style={styles.itemExtraContainer}>
            {extra}
            {arrow && (
              <Icon name={arrowIcon} size={20} color="#BFBFBF" style={styles.itemArrow} />
            )}
          </View>
        )}
      </View>
    );
  };
  
  // Render item
  const renderItem = () => {
    const content = (
      <View
        style={[
          styles.itemContainer,
          itemStyles.container,
          selected && styles.selectedItem,
          selected && selectedStyle,
          style,
        ]}
      >
        {renderContent()}
        {showDivider && <View style={styles.divider} />}
      </View>
    );
    
    if (clickable && onPress && !disabled) {
      return (
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={onPress}
        >
          {content}
        </TouchableOpacity>
      );
    }
    
    return content;
  };
  
  return renderItem();
};

// List Component
function List<T = any>({
  data = [],
  header,
  footer,
  size = 'default',
  layout = 'vertical',
  bordered = true,
  borderPosition = 'both',
  split = true,
  renderItem,
  style,
  itemStyle,
  loading = false,
  loadingComponent,
  loadMore = false,
  onLoadMore,
  loadingMore = false,
  allLoaded = false,
  allLoadedText = '没有更多数据了',
  empty = false,
  emptyComponent,
  emptyText = '暂无数据',
  refreshable = false,
  onRefresh,
  refreshing = false,
  keyExtractor,
  showsScrollIndicator = true,
  numColumns = 1,
  onEndReached,
  onEndReachedThreshold = 0.5,
  inverted = false,
  ListHeaderComponent,
  ListFooterComponent,
  ItemSeparatorComponent,
  flatListProps = {},
  sections = false,
  children,
}: ListProps<T>) {
  // Get size-based styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          padding: 8,
          fontSize: 12,
        };
      case 'large':
        return {
          padding: 16,
          fontSize: 16,
        };
      case 'default':
      default:
        return {
          padding: 12,
          fontSize: 14,
        };
    }
  };
  
  const sizeStyles = getSizeStyles();
  
  // Get border styles
  const getBorderStyles = () => {
    if (!bordered) return {};
    
    const styles: ViewStyle = {};
    
    if (borderPosition === 'top' || borderPosition === 'both') {
      styles.borderTopWidth = StyleSheet.hairlineWidth;
      styles.borderTopColor = '#f0f0f0';
    }
    
    if (borderPosition === 'bottom' || borderPosition === 'both') {
      styles.borderBottomWidth = StyleSheet.hairlineWidth;
      styles.borderBottomColor = '#f0f0f0';
    }
    
    return styles;
  };
  
  const borderStyles = getBorderStyles();
  
  // Render header
  const renderHeader = () => {
    if (!header) return null;
    
    if (typeof header === 'string') {
      return (
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>{header}</Text>
        </View>
      );
    }
    
    return header;
  };
  
  // Render footer
  const renderFooter = () => {
    if (!footer) return null;
    
    if (typeof footer === 'string') {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>{footer}</Text>
        </View>
      );
    }
    
    return footer;
  };
  
  // Render load more footer
  const renderLoadMoreFooter = () => {
    if (!loadMore) return null;
    
    if (loadingMore) {
      return (
        <View style={styles.loadMoreContainer}>
          <ActivityIndicator size="small" color="#1890FF" />
          <Text style={styles.loadMoreText}>加载中...</Text>
        </View>
      );
    }
    
    if (allLoaded) {
      return (
        <View style={styles.loadMoreContainer}>
          <Text style={styles.loadMoreText}>{allLoadedText}</Text>
        </View>
      );
    }
    
    return null;
  };
  
  // Render empty component
  const renderEmptyComponent = () => {
    if (!empty) return null;
    
    if (emptyComponent) {
      return emptyComponent;
    }
    
    return (
      <View style={styles.emptyContainer}>
        <Icon name="document-outline" size={48} color="#BFBFBF" />
        <Text style={styles.emptyText}>{emptyText}</Text>
      </View>
    );
  };
  
  // Render loading component
  const renderLoadingComponent = () => {
    if (!loading) return null;
    
    if (loadingComponent) {
      return loadingComponent;
    }
    
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1890FF" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  };
  
  // Handle end reached
  const handleEndReached = () => {
    if (loadMore && onLoadMore && !loadingMore && !allLoaded) {
      onLoadMore();
    }
    
    if (onEndReached) {
      onEndReached();
    }
  };
  
  // Render list item
  const renderListItem = ({ item, index }: ListRenderItemInfo<T>) => {
    if (renderItem) {
      return renderItem(item, index);
    }
    
    return (
      <ListItem
        key={index.toString()}
        title={item.title}
        description={item.description}
        icon={item.icon}
        extra={item.extra}
        style={[
          layout === 'horizontal' && styles.horizontalItem,
          itemStyle,
        ]}
        showDivider={split && index !== data.length - 1}
        clickable={item.clickable}
        onPress={item.onPress}
        arrow={item.arrow}
      />
    );
  };
  
  // If children are provided, render them directly
  if (children) {
    return (
      <View
        style={[
          styles.container,
          borderStyles,
          style,
        ]}
      >
        {renderHeader()}
        {children}
        {renderFooter()}
      </View>
    );
  }
  
  // If loading, render loading component
  if (loading && !refreshable) {
    return (
      <View
        style={[
          styles.container,
          borderStyles,
          style,
        ]}
      >
        {renderHeader()}
        {renderLoadingComponent()}
        {renderFooter()}
      </View>
    );
  }
  
  // If empty, render empty component
  if (empty && data.length === 0) {
    return (
      <View
        style={[
          styles.container,
          borderStyles,
          style,
        ]}
      >
        {renderHeader()}
        {renderEmptyComponent()}
        {renderFooter()}
      </View>
    );
  }
  
  // Render FlatList
  return (
    <View
      style={[
        styles.container,
        borderStyles,
        style,
      ]}
    >
      <FlatList
        data={data}
        renderItem={renderListItem}
        keyExtractor={keyExtractor || ((item, index) => index.toString())}
        ListHeaderComponent={ListHeaderComponent || renderHeader()}
        ListFooterComponent={
          <>
            {ListFooterComponent}
            {renderLoadMoreFooter()}
            {renderFooter()}
          </>
        }
        ItemSeparatorComponent={ItemSeparatorComponent}
        horizontal={layout === 'horizontal'}
        showsVerticalScrollIndicator={showsScrollIndicator && layout === 'vertical'}
        showsHorizontalScrollIndicator={showsScrollIndicator && layout === 'horizontal'}
        numColumns={layout === 'vertical' ? numColumns : 1}
        onEndReached={handleEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        refreshControl={
          refreshable ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1890FF']}
              tintColor="#1890FF"
            />
          ) : undefined
        }
        inverted={inverted}
        {...flatListProps}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
  },
  headerContainer: {
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  headerText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  footerContainer: {
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  footerText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
  itemContainer: {
    backgroundColor: '#fff',
  },
  itemContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  itemIconContainer: {
    marginRight: 12,
  },
  itemIcon: {},
  itemTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  itemTitle: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 12,
    color: '#999',
  },
  itemExtraContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemArrow: {
    marginLeft: 8,
  },
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: '#f0f0f0',
    marginLeft: 12,
  },
  horizontalItem: {
    marginRight: 12,
  },
  selectedItem: {
    backgroundColor: '#f0f7ff',
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  loadMoreText: {
    fontSize: 12,
    color: '#999',
    marginLeft: 8,
  },
  emptyContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    marginTop: 12,
  },
  loadingContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#999',
    marginTop: 12,
  },
});

export default List;
