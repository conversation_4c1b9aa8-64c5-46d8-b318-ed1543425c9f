import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Image, Switch } from 'react-native';
import List, { ListItem } from './List';
import Icon from 'react-native-vector-icons/Ionicons';

// Mock data for basic list
const basicListData = [
  { title: '标题文本 1', description: '描述文本内容' },
  { title: '标题文本 2', description: '描述文本内容' },
  { title: '标题文本 3', description: '描述文本内容' },
];

// Mock data for icon list
const iconListData = [
  { title: '收藏', icon: 'heart-outline', arrow: true },
  { title: '订单', icon: 'document-text-outline', arrow: true },
  { title: '地址', icon: 'location-outline', arrow: true },
  { title: '设置', icon: 'settings-outline', arrow: true },
];

// Mock data for avatar list
const avatarListData = [
  { 
    title: '用户名 1', 
    description: '用户简介信息', 
    avatarUrl: 'https://randomuser.me/api/portraits/women/68.jpg',
    arrow: true 
  },
  { 
    title: '用户名 2', 
    description: '用户简介信息', 
    avatarUrl: 'https://randomuser.me/api/portraits/men/75.jpg',
    arrow: true 
  },
  { 
    title: '用户名 3', 
    description: '用户简介信息', 
    avatarUrl: 'https://randomuser.me/api/portraits/women/81.jpg',
    arrow: true 
  },
];

// Mock data for products
const productListData = [
  { 
    id: '1',
    title: '无线蓝牙耳机', 
    description: '高音质立体声', 
    price: '¥299.00',
    image: 'https://via.placeholder.com/60',
    stock: 10
  },
  { 
    id: '2',
    title: '智能手表', 
    description: '多功能健康监测', 
    price: '¥1299.00',
    image: 'https://via.placeholder.com/60',
    stock: 5
  },
  { 
    id: '3',
    title: '手机保护壳', 
    description: '防摔防刮耐用', 
    price: '¥49.00',
    image: 'https://via.placeholder.com/60',
    stock: 20
  },
  { 
    id: '4',
    title: '便携充电宝', 
    description: '大容量快充', 
    price: '¥129.00',
    image: 'https://via.placeholder.com/60',
    stock: 15
  },
];

// Mock data for horizontal list
const horizontalListData = [
  { title: '推荐', selected: true },
  { title: '手机' },
  { title: '电脑' },
  { title: '配件' },
  { title: '家电' },
  { title: '服饰' },
  { title: '美妆' },
  { title: '食品' },
];

// Mock data for load more
const generateMockData = (start: number, count: number) => {
  return Array(count).fill(0).map((_, index) => ({
    id: `${start + index}`,
    title: `列表项 ${start + index}`,
    description: `这是列表项 ${start + index} 的描述内容`,
  }));
};

const ListExample = () => {
  const [selectedTab, setSelectedTab] = useState('basic');
  const [switchValues, setSwitchValues] = useState({
    notification: true,
    location: false,
    darkMode: false,
  });
  
  // State for load more example
  const [loadMoreData, setLoadMoreData] = useState(generateMockData(1, 10));
  const [loadingMore, setLoadingMore] = useState(false);
  const [allLoaded, setAllLoaded] = useState(false);
  const [page, setPage] = useState(1);
  
  // State for refresh example
  const [refreshing, setRefreshing] = useState(false);
  const [refreshData, setRefreshData] = useState(generateMockData(1, 5));
  
  // Handle load more
  const handleLoadMore = () => {
    if (loadingMore || allLoaded) return;
    
    setLoadingMore(true);
    
    // Simulate API call
    setTimeout(() => {
      const nextPage = page + 1;
      
      if (nextPage > 3) {
        setAllLoaded(true);
      } else {
        const newData = [...loadMoreData, ...generateMockData((nextPage - 1) * 10 + 1, 10)];
        setLoadMoreData(newData);
        setPage(nextPage);
      }
      
      setLoadingMore(false);
    }, 1500);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    
    // Simulate API call
    setTimeout(() => {
      setRefreshData(generateMockData(1, 5));
      setRefreshing(false);
    }, 1500);
  };
  
  // Handle switch toggle
  const handleSwitchToggle = (key: 'notification' | 'location' | 'darkMode') => {
    setSwitchValues(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };
  
  // Render tab content
  const renderTabContent = () => {
    switch (selectedTab) {
      case 'basic':
        return (
          <>
            <Text style={styles.sectionTitle}>基础列表</Text>
            <List
              header="基础列表"
              footer="列表脚注信息"
              data={basicListData}
              bordered
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>不同尺寸</Text>
            <List
              header="小尺寸"
              data={basicListData.slice(0, 2)}
              size="small"
              bordered
            />
            
            <View style={styles.spacer} />
            
            <List
              header="默认尺寸"
              data={basicListData.slice(0, 2)}
              size="default"
              bordered
            />
            
            <View style={styles.spacer} />
            
            <List
              header="大尺寸"
              data={basicListData.slice(0, 2)}
              size="large"
              bordered
            />
          </>
        );
      
      case 'icon':
        return (
          <>
            <Text style={styles.sectionTitle}>图标列表</Text>
            <List
              header="个人中心菜单"
              data={iconListData}
              bordered
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>头像列表</Text>
            <List
              header="用户列表"
              data={avatarListData}
              bordered
              renderItem={(item, index) => (
                <ListItem
                  key={index.toString()}
                  title={item.title}
                  description={item.description}
                  avatar={<Image source={{ uri: item.avatarUrl }} style={styles.avatar} />}
                  arrow={item.arrow}
                />
              )}
            />
          </>
        );
      
      case 'custom':
        return (
          <>
            <Text style={styles.sectionTitle}>自定义列表项</Text>
            <List
              header="商品列表"
              bordered
            >
              {productListData.map((item, index) => (
                <ListItem
                  key={item.id}
                  content={
                    <View style={styles.productItem}>
                      <Image
                        source={{ uri: item.image }}
                        style={styles.productImage}
                      />
                      
                      <View style={styles.productInfo}>
                        <Text style={styles.productTitle}>{item.title}</Text>
                        <Text style={styles.productDescription}>{item.description}</Text>
                        <Text style={styles.productPrice}>{item.price}</Text>
                      </View>
                      
                      <TouchableOpacity style={styles.addButton}>
                        <Icon name="add" size={16} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  }
                  showDivider={index !== productListData.length - 1}
                />
              ))}
            </List>
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>开关列表</Text>
            <List
              header="设置"
              bordered
            >
              <ListItem
                title="推送通知"
                extra={
                  <Switch
                    value={switchValues.notification}
                    onValueChange={() => handleSwitchToggle('notification')}
                    trackColor={{ false: '#d9d9d9', true: '#1890FF' }}
                  />
                }
              />
              
              <ListItem
                title="位置服务"
                extra={
                  <Switch
                    value={switchValues.location}
                    onValueChange={() => handleSwitchToggle('location')}
                    trackColor={{ false: '#d9d9d9', true: '#1890FF' }}
                  />
                }
              />
              
              <ListItem
                title="深色模式"
                extra={
                  <Switch
                    value={switchValues.darkMode}
                    onValueChange={() => handleSwitchToggle('darkMode')}
                    trackColor={{ false: '#d9d9d9', true: '#1890FF' }}
                  />
                }
                showDivider={false}
              />
            </List>
          </>
        );
      
      case 'layout':
        return (
          <>
            <Text style={styles.sectionTitle}>水平列表</Text>
            <List
              header="分类导航"
              data={horizontalListData}
              layout="horizontal"
              bordered
              renderItem={(item, index) => (
                <ListItem
                  key={index.toString()}
                  title={item.title}
                  style={styles.horizontalItem}
                  selected={item.selected}
                  selectedStyle={styles.selectedHorizontalItem}
                  titleStyle={item.selected ? styles.selectedHorizontalItemText : {}}
                  showDivider={false}
                  clickable
                  onPress={() => {}}
                />
              )}
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>网格布局</Text>
            <List
              header="商品网格"
              data={productListData}
              numColumns={2}
              bordered
              renderItem={(item, index) => (
                <View style={styles.gridItem}>
                  <Image
                    source={{ uri: item.image }}
                    style={styles.gridImage}
                  />
                  <Text style={styles.gridTitle} numberOfLines={1}>{item.title}</Text>
                  <Text style={styles.gridPrice}>{item.price}</Text>
                </View>
              )}
            />
          </>
        );
      
      case 'loading':
        return (
          <>
            <Text style={styles.sectionTitle}>加载更多</Text>
            <List
              header="下拉加载更多示例"
              data={loadMoreData}
              loadMore
              onLoadMore={handleLoadMore}
              loadingMore={loadingMore}
              allLoaded={allLoaded}
              allLoadedText="没有更多数据了"
              bordered
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>下拉刷新</Text>
            <List
              header="下拉刷新示例"
              data={refreshData}
              refreshable
              onRefresh={handleRefresh}
              refreshing={refreshing}
              bordered
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>加载中状态</Text>
            <List
              header="加载中"
              loading
              bordered
            />
            
            <View style={styles.spacer} />
            
            <Text style={styles.sectionTitle}>空状态</Text>
            <List
              header="空列表"
              data={[]}
              empty
              emptyText="暂无数据"
              bordered
            />
          </>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'basic' && styles.activeTab]}
          onPress={() => setSelectedTab('basic')}
        >
          <Text style={[styles.tabText, selectedTab === 'basic' && styles.activeTabText]}>基础</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'icon' && styles.activeTab]}
          onPress={() => setSelectedTab('icon')}
        >
          <Text style={[styles.tabText, selectedTab === 'icon' && styles.activeTabText]}>图标</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'custom' && styles.activeTab]}
          onPress={() => setSelectedTab('custom')}
        >
          <Text style={[styles.tabText, selectedTab === 'custom' && styles.activeTabText]}>自定义</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'layout' && styles.activeTab]}
          onPress={() => setSelectedTab('layout')}
        >
          <Text style={[styles.tabText, selectedTab === 'layout' && styles.activeTabText]}>布局</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'loading' && styles.activeTab]}
          onPress={() => setSelectedTab('loading')}
        >
          <Text style={[styles.tabText, selectedTab === 'loading' && styles.activeTabText]}>加载</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {renderTabContent()}
        <View style={styles.bottomSpacer} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#f0f0f0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#1890FF',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#1890FF',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  spacer: {
    height: 20,
  },
  bottomSpacer: {
    height: 40,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  productItem: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
  },
  productInfo: {
    flex: 1,
    marginLeft: 12,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  productDescription: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  productPrice: {
    fontSize: 14,
    color: '#FF4D4F',
    marginTop: 4,
  },
  addButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#1890FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  horizontalItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
  },
  selectedHorizontalItem: {
    backgroundColor: '#e6f7ff',
  },
  selectedHorizontalItemText: {
    color: '#1890FF',
    fontWeight: '500',
  },
  gridItem: {
    flex: 1,
    margin: 5,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 4,
    alignItems: 'center',
  },
  gridImage: {
    width: 100,
    height: 100,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
    marginBottom: 8,
  },
  gridTitle: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  gridPrice: {
    fontSize: 14,
    color: '#FF4D4F',
  },
});

export default ListExample;
