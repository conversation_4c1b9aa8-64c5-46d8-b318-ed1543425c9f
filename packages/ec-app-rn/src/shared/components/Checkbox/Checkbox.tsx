import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  StyleProp,
  Animated,
  Easing,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export interface CheckboxProps {
  /**
   * Whether the checkbox is checked
   */
  checked?: boolean;
  
  /**
   * Default checked state (for uncontrolled component)
   */
  defaultChecked?: boolean;
  
  /**
   * Callback when the checkbox state changes
   */
  onChange?: (checked: boolean) => void;
  
  /**
   * Whether the checkbox is disabled
   */
  disabled?: boolean;
  
  /**
   * Label text for the checkbox
   */
  label?: string;
  
  /**
   * Custom render function for label
   */
  renderLabel?: () => React.ReactNode;
  
  /**
   * Position of the label
   */
  labelPosition?: 'left' | 'right';
  
  /**
   * Size of the checkbox
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Shape of the checkbox
   */
  shape?: 'square' | 'circle';
  
  /**
   * Color when checkbox is checked
   */
  checkedColor?: string;
  
  /**
   * Color when checkbox is unchecked
   */
  uncheckedColor?: string;
  
  /**
   * Color of the checkmark icon
   */
  checkmarkColor?: string;
  
  /**
   * Custom icon name from Ionicons
   */
  checkmarkIcon?: string;
  
  /**
   * Whether to show animation when state changes
   */
  animated?: boolean;
  
  /**
   * Custom style for the checkbox container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the checkbox
   */
  checkboxStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the label
   */
  labelStyle?: StyleProp<TextStyle>;
  
  /**
   * Whether the checkbox is in an indeterminate state
   */
  indeterminate?: boolean;
  
  /**
   * Whether to show a border when unchecked
   */
  showBorder?: boolean;
  
  /**
   * Children to render (alternative to label)
   */
  children?: React.ReactNode;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  defaultChecked = false,
  onChange,
  disabled = false,
  label,
  renderLabel,
  labelPosition = 'right',
  size = 'medium',
  shape = 'square',
  checkedColor = '#FF4D4F',
  uncheckedColor = '#d9d9d9',
  checkmarkColor = '#fff',
  checkmarkIcon = 'checkmark-outline',
  animated = true,
  containerStyle,
  checkboxStyle,
  labelStyle,
  indeterminate = false,
  showBorder = true,
  children,
}) => {
  // For controlled/uncontrolled component
  const [internalChecked, setInternalChecked] = useState(defaultChecked);
  const isChecked = checked !== undefined ? checked : internalChecked;
  
  // Animation value
  const animatedValue = new Animated.Value(isChecked || indeterminate ? 1 : 0);
  
  // Update animation when checked state changes
  useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: isChecked || indeterminate ? 1 : 0,
        duration: 150,
        easing: Easing.ease,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(isChecked || indeterminate ? 1 : 0);
    }
  }, [isChecked, indeterminate, animated, animatedValue]);
  
  // Get size-based styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          boxSize: 16,
          iconSize: 12,
          labelFontSize: 12,
        };
      case 'large':
        return {
          boxSize: 24,
          iconSize: 18,
          labelFontSize: 16,
        };
      case 'medium':
      default:
        return {
          boxSize: 20,
          iconSize: 15,
          labelFontSize: 14,
        };
    }
  };
  
  const sizeStyles = getSizeStyles();
  
  // Handle press
  const handlePress = () => {
    if (disabled) return;
    
    const newChecked = !isChecked;
    
    // For uncontrolled component
    if (checked === undefined) {
      setInternalChecked(newChecked);
    }
    
    // Call onChange callback
    if (onChange) {
      onChange(newChecked);
    }
  };
  
  // Background color animation
  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [showBorder ? 'transparent' : '#f5f5f5', checkedColor],
  });
  
  // Border color animation
  const borderColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [uncheckedColor, checkedColor],
  });
  
  // Scale animation for checkmark
  const checkmarkScale = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0.5, 1],
  });
  
  // Render checkbox
  const renderCheckbox = () => {
    return (
      <Animated.View
        style={[
          styles.checkbox,
          {
            width: sizeStyles.boxSize,
            height: sizeStyles.boxSize,
            borderRadius: shape === 'circle' ? sizeStyles.boxSize / 2 : 2,
            backgroundColor,
            borderColor,
            borderWidth: showBorder ? 1 : 0,
          },
          checkboxStyle,
          disabled && styles.disabled,
        ]}
      >
        {indeterminate ? (
          <Animated.View
            style={{
              width: sizeStyles.boxSize * 0.6,
              height: 2,
              backgroundColor: checkmarkColor,
              transform: [{ scale: checkmarkScale }],
            }}
          />
        ) : (
          <Animated.View
            style={{
              transform: [{ scale: checkmarkScale }],
              opacity: animatedValue,
            }}
          >
            <Icon
              name={checkmarkIcon}
              size={sizeStyles.iconSize}
              color={checkmarkColor}
            />
          </Animated.View>
        )}
      </Animated.View>
    );
  };
  
  // Render label
  const renderLabelContent = () => {
    if (renderLabel) {
      return renderLabel();
    }
    
    if (children) {
      return children;
    }
    
    if (label) {
      return (
        <Text
          style={[
            styles.label,
            { fontSize: sizeStyles.labelFontSize },
            disabled && styles.disabledText,
            labelStyle,
          ]}
        >
          {label}
        </Text>
      );
    }
    
    return null;
  };
  
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handlePress}
      disabled={disabled}
      style={[
        styles.container,
        { flexDirection: labelPosition === 'left' ? 'row-reverse' : 'row' },
        containerStyle,
      ]}
    >
      {renderCheckbox()}
      
      {renderLabelContent() && (
        <View
          style={[
            styles.labelContainer,
            { marginLeft: labelPosition === 'right' ? 8 : 0, marginRight: labelPosition === 'left' ? 8 : 0 },
          ]}
        >
          {renderLabelContent()}
        </View>
      )}
    </TouchableOpacity>
  );
};

// Checkbox Group Component
export interface CheckboxGroupProps {
  /**
   * Array of options
   */
  options: Array<{
    label: string;
    value: string | number;
    disabled?: boolean;
  }>;
  
  /**
   * Selected values
   */
  value?: Array<string | number>;
  
  /**
   * Default selected values (for uncontrolled component)
   */
  defaultValue?: Array<string | number>;
  
  /**
   * Callback when selection changes
   */
  onChange?: (checkedValues: Array<string | number>) => void;
  
  /**
   * Direction of the group
   */
  direction?: 'horizontal' | 'vertical';
  
  /**
   * Props to pass to each checkbox
   */
  checkboxProps?: Omit<CheckboxProps, 'checked' | 'onChange' | 'label'>;
  
  /**
   * Custom style for the group container
   */
  containerStyle?: StyleProp<ViewStyle>;
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  options = [],
  value,
  defaultValue = [],
  onChange,
  direction = 'vertical',
  checkboxProps,
  containerStyle,
}) => {
  // For controlled/uncontrolled component
  const [internalValue, setInternalValue] = useState<Array<string | number>>(defaultValue);
  const selectedValues = value !== undefined ? value : internalValue;
  
  // Handle checkbox change
  const handleCheckboxChange = (optionValue: string | number, checked: boolean) => {
    const newValues = checked
      ? [...selectedValues, optionValue]
      : selectedValues.filter(v => v !== optionValue);
    
    // For uncontrolled component
    if (value === undefined) {
      setInternalValue(newValues);
    }
    
    // Call onChange callback
    if (onChange) {
      onChange(newValues);
    }
  };
  
  return (
    <View
      style={[
        styles.groupContainer,
        { flexDirection: direction === 'horizontal' ? 'row' : 'column' },
        containerStyle,
      ]}
    >
      {options.map((option, index) => (
        <Checkbox
          key={option.value.toString()}
          label={option.label}
          checked={selectedValues.includes(option.value)}
          onChange={(checked) => handleCheckboxChange(option.value, checked)}
          disabled={option.disabled}
          containerStyle={[
            direction === 'horizontal' ? styles.horizontalItem : styles.verticalItem,
            index === options.length - 1 && { marginRight: 0, marginBottom: 0 },
          ]}
          {...checkboxProps}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.4,
  },
  disabledText: {
    color: '#00000040',
  },
  labelContainer: {
    flexShrink: 1,
  },
  label: {
    color: '#333',
  },
  groupContainer: {
    flexWrap: 'wrap',
  },
  horizontalItem: {
    marginRight: 16,
    marginBottom: 8,
  },
  verticalItem: {
    marginBottom: 12,
  },
});

export default Checkbox;
