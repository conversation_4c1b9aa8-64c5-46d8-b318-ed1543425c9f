import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import Checkbox, { CheckboxGroup } from './Checkbox';
import Icon from 'react-native-vector-icons/Ionicons';

const CheckboxExample = () => {
  const [checked, setChecked] = useState(false);
  const [groupValues, setGroupValues] = useState<Array<string | number>>(['option1']);
  const [selectedCategories, setSelectedCategories] = useState<Array<string>>(['electronics']);
  const [selectedFilters, setSelectedFilters] = useState<Array<string>>([]);
  
  const handleToggleAll = () => {
    if (selectedFilters.length === filterOptions.length) {
      setSelectedFilters([]);
    } else {
      setSelectedFilters(filterOptions.map(option => option.value));
    }
  };
  
  const filterOptions = [
    { label: '免运费', value: 'freeShipping' },
    { label: '有优惠券', value: 'hasCoupon' },
    { label: '24小时发货', value: 'fastDelivery' },
    { label: '可退换', value: 'returnable' },
  ];
  
  const categoryOptions = [
    { label: '电子产品', value: 'electronics' },
    { label: '服装', value: 'clothing' },
    { label: '家居', value: 'home' },
    { label: '美妆', value: 'beauty' },
    { label: '食品', value: 'food' },
  ];
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>基础复选框</Text>
      <View style={styles.section}>
        <Checkbox
          label="基础复选框"
          checked={checked}
          onChange={setChecked}
        />
      </View>
      
      <Text style={styles.title}>不同尺寸</Text>
      <View style={styles.section}>
        <Checkbox
          label="小尺寸"
          size="small"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="默认尺寸"
          size="medium"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="大尺寸"
          size="large"
        />
      </View>
      
      <Text style={styles.title}>不同形状</Text>
      <View style={styles.section}>
        <Checkbox
          label="方形复选框"
          shape="square"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="圆形复选框"
          shape="circle"
        />
      </View>
      
      <Text style={styles.title}>禁用状态</Text>
      <View style={styles.section}>
        <Checkbox
          label="禁用未选中"
          disabled
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="禁用已选中"
          checked
          disabled
        />
      </View>
      
      <Text style={styles.title}>自定义颜色</Text>
      <View style={styles.section}>
        <Checkbox
          label="蓝色主题"
          checkedColor="#1890FF"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="绿色主题"
          checkedColor="#52C41A"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="紫色主题"
          checkedColor="#722ED1"
        />
      </View>
      
      <Text style={styles.title}>标签位置</Text>
      <View style={styles.section}>
        <Checkbox
          label="标签在右侧"
          labelPosition="right"
          containerStyle={styles.marginBottom}
        />
        
        <Checkbox
          label="标签在左侧"
          labelPosition="left"
        />
      </View>
      
      <Text style={styles.title}>不确定状态</Text>
      <View style={styles.section}>
        <Checkbox
          label="不确定状态"
          indeterminate
        />
      </View>
      
      <Text style={styles.title}>自定义图标</Text>
      <View style={styles.section}>
        <Checkbox
          label="自定义图标"
          checkmarkIcon="heart"
          checkedColor="#FF4D4F"
        />
      </View>
      
      <Text style={styles.title}>带自定义内容</Text>
      <View style={styles.section}>
        <Checkbox
          containerStyle={styles.marginBottom}
        >
          <View style={styles.customContent}>
            <Icon name="star" size={16} color="#FFB800" />
            <Text style={styles.customText}>带有自定义内容的复选框</Text>
          </View>
        </Checkbox>
        
        <Checkbox>
          <View style={styles.customContent}>
            <Text style={styles.customText}>接受</Text>
            <Text style={styles.linkText}>服务条款</Text>
            <Text style={styles.customText}>和</Text>
            <Text style={styles.linkText}>隐私政策</Text>
          </View>
        </Checkbox>
      </View>
      
      <Text style={styles.title}>复选框组 - 垂直</Text>
      <View style={styles.section}>
        <CheckboxGroup
          options={[
            { label: '选项 A', value: 'option1' },
            { label: '选项 B', value: 'option2' },
            { label: '选项 C', value: 'option3', disabled: true },
          ]}
          value={groupValues}
          onChange={setGroupValues}
        />
      </View>
      
      <Text style={styles.title}>复选框组 - 水平</Text>
      <View style={styles.section}>
        <CheckboxGroup
          options={[
            { label: '选项 A', value: 'option1' },
            { label: '选项 B', value: 'option2' },
            { label: '选项 C', value: 'option3' },
          ]}
          value={groupValues}
          onChange={setGroupValues}
          direction="horizontal"
        />
      </View>
      
      <Text style={styles.title}>应用场景：商品筛选</Text>
      <View style={styles.section}>
        <View style={styles.filterHeader}>
          <Text style={styles.filterTitle}>筛选条件</Text>
          <TouchableOpacity onPress={handleToggleAll}>
            <Text style={styles.filterToggle}>
              {selectedFilters.length === filterOptions.length ? '取消全选' : '全选'}
            </Text>
          </TouchableOpacity>
        </View>
        
        <CheckboxGroup
          options={filterOptions}
          value={selectedFilters}
          onChange={setSelectedFilters}
          direction="horizontal"
          checkboxProps={{
            size: 'small',
            checkedColor: '#1890FF',
          }}
        />
        
        <View style={styles.filterDivider} />
        
        <Text style={styles.filterSubtitle}>商品分类</Text>
        
        <CheckboxGroup
          options={categoryOptions}
          value={selectedCategories}
          onChange={setSelectedCategories}
          checkboxProps={{
            size: 'small',
            checkedColor: '#1890FF',
          }}
        />
        
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>应用筛选</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.title}>应用场景：购物车选择</Text>
      <View style={styles.section}>
        <View style={styles.cartItem}>
          <Checkbox
            checked
            checkedColor="#1890FF"
          />
          <View style={styles.cartItemContent}>
            <Text style={styles.cartItemTitle}>无线蓝牙耳机</Text>
            <Text style={styles.cartItemDesc}>白色 / 标准版</Text>
            <Text style={styles.cartItemPrice}>¥299.00</Text>
          </View>
        </View>
        
        <View style={styles.cartItem}>
          <Checkbox
            checked
            checkedColor="#1890FF"
          />
          <View style={styles.cartItemContent}>
            <Text style={styles.cartItemTitle}>智能手表</Text>
            <Text style={styles.cartItemDesc}>黑色 / GPS版</Text>
            <Text style={styles.cartItemPrice}>¥1299.00</Text>
          </View>
        </View>
        
        <View style={styles.cartItem}>
          <Checkbox
            checkedColor="#1890FF"
          />
          <View style={styles.cartItemContent}>
            <Text style={styles.cartItemTitle}>手机保护壳</Text>
            <Text style={styles.cartItemDesc}>透明 / 防摔款</Text>
            <Text style={styles.cartItemPrice}>¥49.00</Text>
          </View>
        </View>
        
        <View style={styles.cartFooter}>
          <Checkbox
            label="全选"
            indeterminate
            checkedColor="#1890FF"
          />
          <View style={styles.cartTotal}>
            <Text style={styles.cartTotalText}>总计: </Text>
            <Text style={styles.cartTotalPrice}>¥1598.00</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  marginBottom: {
    marginBottom: 15,
  },
  customContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customText: {
    fontSize: 14,
    marginLeft: 4,
  },
  linkText: {
    fontSize: 14,
    color: '#1890FF',
    marginLeft: 4,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filterToggle: {
    fontSize: 14,
    color: '#1890FF',
  },
  filterDivider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 15,
  },
  filterSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
  filterButton: {
    backgroundColor: '#1890FF',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 15,
  },
  filterButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  cartItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  cartItemContent: {
    marginLeft: 10,
  },
  cartItemTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  cartItemDesc: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    color: '#FF4D4F',
    fontWeight: '500',
    marginTop: 4,
  },
  cartFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 15,
  },
  cartTotal: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cartTotalText: {
    fontSize: 14,
  },
  cartTotalPrice: {
    fontSize: 16,
    color: '#FF4D4F',
    fontWeight: 'bold',
  },
});

export default CheckboxExample;
