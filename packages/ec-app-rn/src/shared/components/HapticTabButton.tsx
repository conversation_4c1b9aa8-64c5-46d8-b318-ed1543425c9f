import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

// Haptic feedback options
const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

/**
 * Custom tab bar button with haptic feedback
 * This replaces the Expo-specific HapticTab component from ec-app-ex
 */
export default function HapticTabButton(props: BottomTabBarButtonProps) {
  const { onPress, ...restProps } = props;

  const handlePress = (e: any) => {
    // Trigger haptic feedback when tab is pressed
    ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
    
    // Call the original onPress handler
    if (onPress) {
      onPress(e);
    }
  };

  return (
    <TouchableOpacity
      {...restProps}
      onPress={handlePress}
      style={styles.tabButton}
      activeOpacity={0.7}
    />
  );
}

const styles = StyleSheet.create({
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
