import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export interface StepItem {
  /**
   * Title of the step
   */
  title: string;
  
  /**
   * Description of the step (optional)
   */
  description?: string;
  
  /**
   * Custom icon name from Ionicons (optional)
   */
  icon?: string;
  
  /**
   * Custom status (overrides the computed status based on current step)
   */
  status?: StepStatus;
  
  /**
   * Disable the step (cannot be clicked)
   */
  disabled?: boolean;
}

export type StepStatus = 'wait' | 'process' | 'finish' | 'error';
export type StepDirection = 'horizontal' | 'vertical';
export type StepSize = 'small' | 'default';

export interface StepperProps {
  /**
   * Array of step items
   */
  steps: StepItem[];
  
  /**
   * Current active step index (0-based)
   */
  current: number;
  
  /**
   * Direction of the stepper
   */
  direction?: StepDirection;
  
  /**
   * Size of the stepper
   */
  size?: StepSize;
  
  /**
   * Whether to show step numbers instead of icons
   */
  showStepNumber?: boolean;
  
  /**
   * Whether to allow clicking on steps to navigate
   */
  clickable?: boolean;
  
  /**
   * Callback when a step is clicked
   */
  onChange?: (current: number) => void;
  
  /**
   * Primary color for active/finished steps
   */
  primaryColor?: string;
  
  /**
   * Error color for steps with error status
   */
  errorColor?: string;
  
  /**
   * Custom style for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for step titles
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for step descriptions
   */
  descriptionStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for step icons
   */
  iconStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for step connectors
   */
  connectorStyle?: StyleProp<ViewStyle>;
}

const Stepper: React.FC<StepperProps> = ({
  steps,
  current,
  direction = 'horizontal',
  size = 'default',
  showStepNumber = false,
  clickable = false,
  onChange,
  primaryColor = '#FF4D4F',
  errorColor = '#FF4D4F',
  containerStyle,
  titleStyle,
  descriptionStyle,
  iconStyle,
  connectorStyle,
}) => {
  // Get status for a step
  const getStepStatus = (index: number, step: StepItem): StepStatus => {
    // If step has explicit status, use it
    if (step.status) {
      return step.status;
    }
    
    // Otherwise compute based on current index
    if (index < current) {
      return 'finish';
    } else if (index === current) {
      return 'process';
    } else {
      return 'wait';
    }
  };
  
  // Get color based on status
  const getColorByStatus = (status: StepStatus): string => {
    switch (status) {
      case 'finish':
      case 'process':
        return primaryColor;
      case 'error':
        return errorColor;
      case 'wait':
      default:
        return '#BFBFBF';
    }
  };
  
  // Get icon based on status and configuration
  const getStepIcon = (index: number, step: StepItem, status: StepStatus) => {
    // If showing numbers instead of icons
    if (showStepNumber) {
      return (index + 1).toString();
    }
    
    // If step has custom icon
    if (step.icon) {
      return step.icon;
    }
    
    // Default icons based on status
    switch (status) {
      case 'finish':
        return 'checkmark-outline';
      case 'error':
        return 'close-outline';
      case 'process':
      case 'wait':
      default:
        return (index + 1).toString(); // Fallback to number
    }
  };
  
  // Handle step click
  const handleStepClick = (index: number, step: StepItem) => {
    if (!clickable || step.disabled) {
      return;
    }
    
    if (onChange) {
      onChange(index);
    }
  };
  
  // Get size-based styles
  const getSizeStyles = () => {
    if (size === 'small') {
      return {
        iconSize: 20,
        iconTextSize: 12,
        titleSize: 12,
        descriptionSize: 10,
        connectorHeight: direction === 'vertical' ? 30 : 1,
        connectorWidth: direction === 'horizontal' ? 30 : 1,
      };
    }
    
    return {
      iconSize: 28,
      iconTextSize: 14,
      titleSize: 14,
      descriptionSize: 12,
      connectorHeight: direction === 'vertical' ? 40 : 1,
      connectorWidth: direction === 'horizontal' ? 40 : 1,
    };
  };
  
  const sizeStyles = getSizeStyles();
  
  // Render a single step
  const renderStep = (step: StepItem, index: number) => {
    const status = getStepStatus(index, step);
    const color = getColorByStatus(status);
    const icon = getStepIcon(index, step, status);
    const isLast = index === steps.length - 1;
    
    return (
      <View
        key={index}
        style={[
          direction === 'horizontal' ? styles.stepHorizontal : styles.stepVertical,
          step.disabled && styles.stepDisabled,
        ]}
      >
        <TouchableOpacity
          activeOpacity={clickable && !step.disabled ? 0.7 : 1}
          onPress={() => handleStepClick(index, step)}
          disabled={!clickable || step.disabled}
          style={styles.stepContent}
        >
          {/* Step Icon/Number */}
          <View
            style={[
              styles.iconContainer,
              {
                width: sizeStyles.iconSize,
                height: sizeStyles.iconSize,
                borderRadius: sizeStyles.iconSize / 2,
                backgroundColor: status === 'process' ? '#fff' : color,
                borderWidth: status === 'process' ? 1 : 0,
                borderColor: color,
              },
              iconStyle,
            ]}
          >
            {typeof icon === 'string' && icon.match(/^\d+$/) ? (
              <Text
                style={{
                  color: status === 'process' ? color : '#fff',
                  fontSize: sizeStyles.iconTextSize,
                  fontWeight: '500',
                }}
              >
                {icon}
              </Text>
            ) : (
              <Icon
                name={icon as string}
                size={sizeStyles.iconSize * 0.6}
                color={status === 'process' ? color : '#fff'}
              />
            )}
          </View>
          
          {/* Step Title and Description */}
          <View style={styles.textContainer}>
            <Text
              style={[
                styles.title,
                {
                  color: status === 'wait' ? '#999' : '#333',
                  fontSize: sizeStyles.titleSize,
                },
                titleStyle,
              ]}
            >
              {step.title}
            </Text>
            
            {step.description && (
              <Text
                style={[
                  styles.description,
                  {
                    color: '#999',
                    fontSize: sizeStyles.descriptionSize,
                  },
                  descriptionStyle,
                ]}
              >
                {step.description}
              </Text>
            )}
          </View>
        </TouchableOpacity>
        
        {/* Connector line between steps */}
        {!isLast && (
          <View
            style={[
              direction === 'horizontal' ? styles.connectorHorizontal : styles.connectorVertical,
              {
                backgroundColor: index < current ? color : '#E8E8E8',
                height: direction === 'horizontal' ? 1 : sizeStyles.connectorHeight,
                width: direction === 'vertical' ? 1 : sizeStyles.connectorWidth,
              },
              connectorStyle,
            ]}
          />
        )}
      </View>
    );
  };
  
  return (
    <View
      style={[
        styles.container,
        direction === 'horizontal' ? styles.horizontal : styles.vertical,
        containerStyle,
      ]}
    >
      {steps.map((step, index) => renderStep(step, index))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  horizontal: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  vertical: {
    flexDirection: 'column',
  },
  stepHorizontal: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stepVertical: {
    flexDirection: 'column',
    marginBottom: 20,
  },
  stepDisabled: {
    opacity: 0.5,
  },
  stepContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 8,
    flex: 1,
  },
  title: {
    fontWeight: '500',
  },
  description: {
    marginTop: 2,
  },
  connectorHorizontal: {
    flex: 1,
    height: 1,
    backgroundColor: '#E8E8E8',
    marginHorizontal: 8,
  },
  connectorVertical: {
    width: 1,
    backgroundColor: '#E8E8E8',
    marginLeft: 14,
    marginTop: 8,
  },
});

export default Stepper;
