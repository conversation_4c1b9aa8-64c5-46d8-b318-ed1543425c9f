import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import Stepper from './Stepper';

const StepperExample = () => {
  const [current, setCurrent] = useState(1);
  const [verticalCurrent, setVerticalCurrent] = useState(0);
  
  const horizontalSteps = [
    {
      title: '购物车',
      description: '查看商品',
    },
    {
      title: '确认订单',
      description: '确认收货信息',
    },
    {
      title: '支付',
      description: '选择支付方式',
    },
    {
      title: '完成',
      description: '订单完成',
    },
  ];
  
  const verticalSteps = [
    {
      title: '提交订单',
      description: '2025-06-13 18:30:20',
      icon: 'create-outline',
    },
    {
      title: '付款成功',
      description: '2025-06-13 18:35:46',
      icon: 'card-outline',
    },
    {
      title: '商家发货',
      description: '2025-06-14 10:15:32',
      icon: 'cube-outline',
    },
    {
      title: '等待收货',
      description: '预计6月16日送达',
      icon: 'bicycle-outline',
    },
    {
      title: '订单完成',
      description: '',
      icon: 'checkmark-circle-outline',
    },
  ];
  
  const errorSteps = [
    {
      title: '填写信息',
      status: 'finish',
    },
    {
      title: '上传资料',
      status: 'error',
    },
    {
      title: '审核',
      disabled: true,
    },
    {
      title: '完成',
      disabled: true,
    },
  ];
  
  const handleNext = () => {
    setCurrent(prev => Math.min(prev + 1, horizontalSteps.length - 1));
  };
  
  const handlePrev = () => {
    setCurrent(prev => Math.max(prev - 1, 0));
  };
  
  const handleVerticalNext = () => {
    setVerticalCurrent(prev => Math.min(prev + 1, verticalSteps.length - 1));
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>基础步骤条</Text>
      <View style={styles.section}>
        <Stepper steps={horizontalSteps} current={current} />
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, current === 0 && styles.disabledButton]}
            onPress={handlePrev}
            disabled={current === 0}
          >
            <Text style={styles.buttonText}>上一步</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, current === horizontalSteps.length - 1 && styles.disabledButton]}
            onPress={handleNext}
            disabled={current === horizontalSteps.length - 1}
          >
            <Text style={styles.buttonText}>下一步</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.title}>可点击的步骤条</Text>
      <View style={styles.section}>
        <Stepper
          steps={horizontalSteps}
          current={current}
          clickable
          onChange={setCurrent}
        />
      </View>
      
      <Text style={styles.title}>带图标的步骤条</Text>
      <View style={styles.section}>
        <Stepper
          steps={[
            { title: '登录', icon: 'log-in-outline' },
            { title: '验证', icon: 'shield-checkmark-outline' },
            { title: '支付', icon: 'wallet-outline' },
            { title: '完成', icon: 'checkmark-circle-outline' },
          ]}
          current={1}
        />
      </View>
      
      <Text style={styles.title}>迷你版步骤条</Text>
      <View style={styles.section}>
        <Stepper
          steps={horizontalSteps}
          current={current}
          size="small"
        />
      </View>
      
      <Text style={styles.title}>带序号的步骤条</Text>
      <View style={styles.section}>
        <Stepper
          steps={horizontalSteps}
          current={current}
          showStepNumber
        />
      </View>
      
      <Text style={styles.title}>自定义颜色</Text>
      <View style={styles.section}>
        <Stepper
          steps={horizontalSteps}
          current={current}
          primaryColor="#1890FF"
        />
      </View>
      
      <Text style={styles.title}>错误状态</Text>
      <View style={styles.section}>
        <Stepper
          steps={errorSteps}
          current={1}
        />
      </View>
      
      <Text style={styles.title}>垂直步骤条</Text>
      <View style={styles.verticalSection}>
        <Stepper
          steps={verticalSteps}
          current={verticalCurrent}
          direction="vertical"
        />
        
        <TouchableOpacity
          style={[styles.button, verticalCurrent === verticalSteps.length - 1 && styles.disabledButton]}
          onPress={handleVerticalNext}
          disabled={verticalCurrent === verticalSteps.length - 1}
        >
          <Text style={styles.buttonText}>下一状态</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.title}>应用场景：结账流程</Text>
      <View style={styles.section}>
        <Stepper
          steps={[
            { title: '购物车', description: '3件商品' },
            { title: '收货地址', description: '选择配送地址' },
            { title: '支付方式', description: '选择支付方式' },
            { title: '确认订单', description: '提交订单' },
          ]}
          current={2}
          primaryColor="#52C41A"
        />
        
        <View style={styles.checkoutContainer}>
          <Text style={styles.checkoutTitle}>选择支付方式</Text>
          
          <View style={styles.paymentOption}>
            <View style={styles.paymentRadio} />
            <Text style={styles.paymentText}>支付宝</Text>
          </View>
          
          <View style={[styles.paymentOption, styles.selectedPayment]}>
            <View style={[styles.paymentRadio, styles.selectedRadio]} />
            <Text style={styles.paymentText}>微信支付</Text>
          </View>
          
          <View style={styles.paymentOption}>
            <View style={styles.paymentRadio} />
            <Text style={styles.paymentText}>银行卡</Text>
          </View>
          
          <TouchableOpacity style={styles.checkoutButton}>
            <Text style={styles.checkoutButtonText}>下一步</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  verticalSection: {
    backgroundColor: '#fff',
    padding: 15,
    paddingBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    backgroundColor: '#FF4D4F',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
  },
  disabledButton: {
    backgroundColor: '#d9d9d9',
  },
  checkoutContainer: {
    marginTop: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  checkoutTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedPayment: {
    backgroundColor: '#FFF1F0',
  },
  paymentRadio: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 1,
    borderColor: '#d9d9d9',
    marginRight: 10,
  },
  selectedRadio: {
    borderColor: '#FF4D4F',
    borderWidth: 5,
  },
  paymentText: {
    fontSize: 14,
  },
  checkoutButton: {
    backgroundColor: '#FF4D4F',
    paddingVertical: 12,
    borderRadius: 4,
    marginTop: 20,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default StepperExample;
