import React from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { ProductCard, Product } from './index';

// Sample product data for demonstration
const sampleProducts: Product[] = [
  {
    id: '1',
    name: '高品质无线蓝牙耳机',
    description: '高音质立体声，降噪麦克风，长续航',
    price: 299.00,
    original_price: 399.00,
    rating: 4.8,
    badge: '热销',
    image_url: 'https://via.placeholder.com/300/3498db/FFFFFF?text=Headphones',
  },
  {
    id: '2',
    name: '智能手表健康监测',
    description: '心率监测，睡眠分析，多种运动模式',
    price: 599.00,
    original_price: 699.00,
    rating: 4.5,
    image_url: 'https://via.placeholder.com/300/2ecc71/FFFFFF?text=SmartWatch',
  },
  {
    id: '3',
    name: '便携式移动电源',
    description: '20000mAh大容量，快充技术，多设备兼容',
    price: 129.00,
    original_price: 159.00,
    rating: 4.7,
    badge: '限时',
    image_url: 'https://via.placeholder.com/300/e74c3c/FFFFFF?text=PowerBank',
  },
];

const ProductCardExample: React.FC = () => {
  const handleProductPress = (product: Product) => {
    console.log('Product pressed:', product.id);
    // Navigate to product details or perform other actions
  };

  // Custom render functions for demonstration
  const renderCustomBadge = (product: Product) => {
    if (!product.badge) return null;
    return (
      <View style={[styles.customBadge, { backgroundColor: product.badge === '热销' ? '#FF9500' : '#FF2D55' }]}>
        <Text style={styles.customBadgeText}>{product.badge}</Text>
      </View>
    );
  };

  const renderCustomRating = (product: Product) => {
    if (!product.rating) return null;
    // Custom star rating display
    return (
      <View style={styles.customRating}>
        <Text style={styles.ratingText}>评分: {product.rating.toFixed(1)}</Text>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Text style={styles.sectionTitle}>默认水平布局</Text>
      <ProductCard 
        product={sampleProducts[0]} 
        onPress={handleProductPress}
      />
      
      <Text style={styles.sectionTitle}>垂直布局</Text>
      <View style={styles.gridContainer}>
        <ProductCard 
          product={sampleProducts[1]} 
          layout="vertical"
          onPress={handleProductPress}
        />
        <ProductCard 
          product={sampleProducts[2]} 
          layout="vertical"
          onPress={handleProductPress}
        />
      </View>
      
      <Text style={styles.sectionTitle}>网格布局</Text>
      <View style={styles.gridContainer}>
        <ProductCard 
          product={sampleProducts[0]} 
          layout="grid"
          onPress={handleProductPress}
        />
        <ProductCard 
          product={sampleProducts[1]} 
          layout="grid"
          onPress={handleProductPress}
        />
      </View>
      
      <Text style={styles.sectionTitle}>紧凑布局</Text>
      <ProductCard 
        product={sampleProducts[2]} 
        layout="compact"
        onPress={handleProductPress}
      />
      
      <Text style={styles.sectionTitle}>自定义样式</Text>
      <ProductCard 
        product={sampleProducts[0]} 
        containerStyle={styles.customContainer}
        imageStyle={styles.customImage}
        titleStyle={styles.customTitle}
        priceStyle={styles.customPrice}
        onPress={handleProductPress}
      />
      
      <Text style={styles.sectionTitle}>自定义渲染函数</Text>
      <ProductCard 
        product={sampleProducts[1]} 
        renderBadge={renderCustomBadge}
        renderRating={renderCustomRating}
        onPress={handleProductPress}
      />
      
      <Text style={styles.sectionTitle}>隐藏部分元素</Text>
      <ProductCard 
        product={sampleProducts[2]} 
        showBadge={false}
        showRating={false}
        onPress={handleProductPress}
      />
      
      <Text style={styles.sectionTitle}>多行标题和描述</Text>
      <ProductCard 
        product={sampleProducts[0]} 
        titleNumberOfLines={2}
        descriptionNumberOfLines={3}
        onPress={handleProductPress}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  content: {
    padding: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  gridContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  customContainer: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  customImage: {
    borderRadius: 10,
  },
  customTitle: {
    color: '#0066CC',
    fontSize: 16,
  },
  customPrice: {
    color: '#009688',
    fontSize: 18,
  },
  customBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 'auto',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderTopRightRadius: 8,
    borderBottomLeftRadius: 8,
  },
  customBadgeText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  customRating: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#666',
  },
});

export default ProductCardExample;
