/**
 * ProductCard component types
 */

export interface Product {
  id: string;
  name?: string;
  title?: string;
  description?: string;
  desc?: string;
  price?: number;
  original_price?: number;
  rating?: number;
  badge?: string;
  image_url?: string;
  image?: string;
  category_id?: string;
  category?: string;
  created_at?: string;
  // Additional fields can be added here as needed
}

export type ProductCardLayout = 'horizontal' | 'vertical' | 'grid' | 'compact';

export interface ProductCardProps {
  /**
   * Product data to display
   */
  product: Product;
  
  /**
   * Layout style of the card
   * @default 'horizontal'
   */
  layout?: ProductCardLayout;
  
  /**
   * Whether to show the product badge
   * @default true
   */
  showBadge?: boolean;
  
  /**
   * Whether to show the product rating
   * @default true
   */
  showRating?: boolean;
  
  /**
   * Whether to show the original price
   * @default true
   */
  showOriginalPrice?: boolean;
  
  /**
   * Number of lines for the product title
   * @default 1
   */
  titleNumberOfLines?: number;
  
  /**
   * Number of lines for the product description
   * @default 1
   */
  descriptionNumberOfLines?: number;
  
  /**
   * Custom style for the card container
   */
  containerStyle?: any;
  
  /**
   * Custom style for the image
   */
  imageStyle?: any;
  
  /**
   * Custom style for the title
   */
  titleStyle?: any;
  
  /**
   * Custom style for the description
   */
  descriptionStyle?: any;
  
  /**
   * Custom style for the price
   */
  priceStyle?: any;
  
  /**
   * Custom style for the original price
   */
  originalPriceStyle?: any;
  
  /**
   * Custom style for the badge
   */
  badgeStyle?: any;
  
  /**
   * Custom style for the badge text
   */
  badgeTextStyle?: any;
  
  /**
   * Custom placeholder image URL
   * @default 'https://via.placeholder.com/150'
   */
  placeholderImageUrl?: string;
  
  /**
   * Callback when the card is pressed
   */
  onPress?: (product: Product) => void;
  
  /**
   * Custom render function for the badge
   */
  renderBadge?: (product: Product) => React.ReactNode;
  
  /**
   * Custom render function for the price
   */
  renderPrice?: (product: Product) => React.ReactNode;
  
  /**
   * Custom render function for the rating
   */
  renderRating?: (product: Product) => React.ReactNode;
  
  /**
   * Custom render function for additional content
   */
  renderAdditionalContent?: (product: Product) => React.ReactNode;
}
