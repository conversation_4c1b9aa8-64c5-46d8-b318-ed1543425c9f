import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { ProductCardProps, ProductCardLayout } from './types';

const { width } = Dimensions.get('window');

/**
 * A flexible and extensible product card component
 */
const ProductCard: React.FC<ProductCardProps> = ({
  product,
  layout = 'horizontal',
  showBadge = true,
  showRating = true,
  showOriginalPrice = true,
  titleNumberOfLines = 1,
  descriptionNumberOfLines = 1,
  containerStyle,
  imageStyle,
  titleStyle,
  descriptionStyle,
  priceStyle,
  originalPriceStyle,
  badgeStyle,
  badgeTextStyle,
  placeholderImageUrl = 'https://via.placeholder.com/150',
  onPress,
  renderBadge,
  renderPrice,
  renderRating,
  renderAdditionalContent,
}) => {
  // Handle different field names for product name
  const displayName = product.name || product.title || 'Unnamed Product';
  
  // Use a placeholder URL if no image is provided
  const imageSource = { uri: product.image_url || product.image || placeholderImageUrl };
  
  // Get styles based on layout
  const layoutStyles = getLayoutStyles(layout);
  
  const handlePress = () => {
    if (onPress) {
      onPress(product);
    }
  };

  return (
    <TouchableOpacity
      style={[layoutStyles.container, styles.card, containerStyle]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={layoutStyles.imageContainer}>
        <Image
          source={imageSource}
          style={[layoutStyles.image, styles.cardImage, imageStyle]}
          resizeMode="cover"
        />
        
        {/* Badge */}
        {showBadge && product.badge && (
          renderBadge ? (
            renderBadge(product)
          ) : (
            <View style={[styles.badge, badgeStyle]}>
              <Text style={[styles.badgeText, badgeTextStyle]}>{product.badge}</Text>
            </View>
          )
        )}
      </View>
      
      <View style={[layoutStyles.infoContainer, styles.cardInfo]}>
        {/* Title */}
        <Text 
          style={[styles.cardTitle, titleStyle]} 
          numberOfLines={titleNumberOfLines}
        >
          {displayName}
        </Text>
        
        {/* Description */}
        {(product.desc || product.description) && (
          <Text 
            style={[styles.cardDesc, descriptionStyle]} 
            numberOfLines={descriptionNumberOfLines}
          >
            {product.desc || product.description}
          </Text>
        )}
        
        {/* Price */}
        {renderPrice ? (
          renderPrice(product)
        ) : (
          <View style={styles.priceRow}>
            <Text style={[styles.price, priceStyle]}>
              ¥{product.price ? product.price.toFixed(2) : '0.00'}
            </Text>
            {showOriginalPrice && product.original_price && (
              <Text style={[styles.originalPrice, originalPriceStyle]}>
                ¥{product.original_price.toFixed(2)}
              </Text>
            )}
          </View>
        )}
        
        {/* Rating */}
        {showRating && typeof product.rating === 'number' && (
          renderRating ? (
            renderRating(product)
          ) : (
            <View style={styles.metaRow}>
              <Icon name="star" size={12} color="#FFD700" style={{ marginHorizontal: 2 }} />
              <Text style={styles.metaText}>{product.rating.toFixed(1)}</Text>
            </View>
          )
        )}
        
        {/* Additional content */}
        {renderAdditionalContent && renderAdditionalContent(product)}
      </View>
    </TouchableOpacity>
  );
};

// Get styles based on layout type
const getLayoutStyles = (layout: ProductCardLayout) => {
  switch (layout) {
    case 'vertical':
      return {
        container: {
          flexDirection: 'column',
          width: (width - 30) / 2, // For 2 columns with margin
        },
        imageContainer: {
          width: '100%',
        },
        image: {
          width: '100%',
          height: 150,
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
        },
        infoContainer: {
          padding: 8,
        },
      };
    case 'grid':
      return {
        container: {
          flexDirection: 'column',
          width: (width - 40) / 2, // For 2 columns with margin
        },
        imageContainer: {
          width: '100%',
        },
        image: {
          width: '100%',
          height: 150,
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
        },
        infoContainer: {
          padding: 8,
        },
      };
    case 'compact':
      return {
        container: {
          flexDirection: 'row',
          width: '100%',
        },
        imageContainer: {
          width: 70,
        },
        image: {
          width: 70,
          height: 70,
          borderRadius: 6,
        },
        infoContainer: {
          flex: 1,
          marginLeft: 10,
        },
      };
    case 'horizontal':
    default:
      return {
        container: {
          flexDirection: 'row',
          width: '100%',
        },
        imageContainer: {
          width: 90,
        },
        image: {
          width: 90,
          height: 90,
          borderRadius: 6,
        },
        infoContainer: {
          flex: 1,
          marginLeft: 10,
        },
      };
  }
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 10,
    marginVertical: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardImage: {
    backgroundColor: '#f0f0f0',
  },
  cardInfo: {
    justifyContent: 'space-between',
  },
  badge: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: '#FF3B30',
    borderTopLeftRadius: 4,
    borderBottomRightRadius: 4,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  cardDesc: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    color: '#E53935',
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 6,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 11,
    color: '#999',
  },
});

export default ProductCard;
