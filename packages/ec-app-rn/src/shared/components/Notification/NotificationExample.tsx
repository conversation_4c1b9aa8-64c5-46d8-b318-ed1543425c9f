import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { 
  NotificationProvider, 
  notificationManager,
  NotificationPosition,
  NotificationPlacement
} from './index';

const NotificationExample = () => {
  const [message, setMessage] = useState('这是一条通知消息');
  const [title, setTitle] = useState('通知标题');
  const [duration, setDuration] = useState('4000');
  const [position, setPosition] = useState<NotificationPosition>('top');
  const [placement, setPlacement] = useState<NotificationPlacement>('top');
  
  // Show notification with current settings
  const showNotification = (type: 'info' | 'success' | 'warning' | 'error') => {
    notificationManager.show({
      type,
      title,
      message,
      duration: parseInt(duration, 10),
      position,
      placement,
    });
  };
  
  // Show notification with action button
  const showNotificationWithAction = () => {
    notificationManager.show({
      type: 'info',
      title: '新优惠券可用',
      message: '您有一张新的优惠券可以使用',
      actionText: '立即查看',
      onAction: () => {
        notificationManager.success('跳转到优惠券页面');
      },
    });
  };
  
  // Show custom notification
  const showCustomNotification = () => {
    notificationManager.show({
      title: '自定义通知',
      message: '这是一条自定义样式的通知',
      color: '#722ED1',
      icon: 'gift-outline',
      duration: 5000,
    });
  };
  
  return (
    <NotificationProvider>
      <ScrollView style={styles.container}>
        <Text style={styles.title}>基础通知</Text>
        <View style={styles.section}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#1890FF' }]}
              onPress={() => showNotification('info')}
            >
              <Text style={styles.buttonText}>信息</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#52C41A' }]}
              onPress={() => showNotification('success')}
            >
              <Text style={styles.buttonText}>成功</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#FAAD14' }]}
              onPress={() => showNotification('warning')}
            >
              <Text style={styles.buttonText}>警告</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#FF4D4F' }]}
              onPress={() => showNotification('error')}
            >
              <Text style={styles.buttonText}>错误</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <Text style={styles.title}>快捷方法</Text>
        <View style={styles.section}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#1890FF' }]}
              onPress={() => notificationManager.info('这是一条信息通知')}
            >
              <Text style={styles.buttonText}>info()</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#52C41A' }]}
              onPress={() => notificationManager.success('操作成功')}
            >
              <Text style={styles.buttonText}>success()</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#FAAD14' }]}
              onPress={() => notificationManager.warning('请注意')}
            >
              <Text style={styles.buttonText}>warning()</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, { backgroundColor: '#FF4D4F' }]}
              onPress={() => notificationManager.error('操作失败')}
            >
              <Text style={styles.buttonText}>error()</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <Text style={styles.title}>自定义选项</Text>
        <View style={styles.section}>
          <View style={styles.inputRow}>
            <Text style={styles.inputLabel}>标题:</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="输入通知标题"
            />
          </View>
          
          <View style={styles.inputRow}>
            <Text style={styles.inputLabel}>消息:</Text>
            <TextInput
              style={styles.input}
              value={message}
              onChangeText={setMessage}
              placeholder="输入通知消息"
            />
          </View>
          
          <View style={styles.inputRow}>
            <Text style={styles.inputLabel}>持续时间:</Text>
            <TextInput
              style={styles.input}
              value={duration}
              onChangeText={setDuration}
              placeholder="持续时间(毫秒)"
              keyboardType="numeric"
            />
          </View>
          
          <View style={styles.inputRow}>
            <Text style={styles.inputLabel}>位置:</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radio, position === 'top' && styles.radioActive]}
                onPress={() => setPosition('top')}
              >
                <Text style={styles.radioText}>顶部</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.radio, position === 'bottom' && styles.radioActive]}
                onPress={() => setPosition('bottom')}
              >
                <Text style={styles.radioText}>底部</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.inputRow}>
            <Text style={styles.inputLabel}>放置:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={[styles.radio, placement === 'top' && styles.radioActive]}
                  onPress={() => setPlacement('top')}
                >
                  <Text style={styles.radioText}>顶部中央</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.radio, placement === 'topLeft' && styles.radioActive]}
                  onPress={() => setPlacement('topLeft')}
                >
                  <Text style={styles.radioText}>左上</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.radio, placement === 'topRight' && styles.radioActive]}
                  onPress={() => setPlacement('topRight')}
                >
                  <Text style={styles.radioText}>右上</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.radio, placement === 'bottom' && styles.radioActive]}
                  onPress={() => setPlacement('bottom')}
                >
                  <Text style={styles.radioText}>底部中央</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.radio, placement === 'bottomLeft' && styles.radioActive]}
                  onPress={() => setPlacement('bottomLeft')}
                >
                  <Text style={styles.radioText}>左下</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.radio, placement === 'bottomRight' && styles.radioActive]}
                  onPress={() => setPlacement('bottomRight')}
                >
                  <Text style={styles.radioText}>右下</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#1890FF' }]}
            onPress={() => showNotification('info')}
          >
            <Text style={styles.buttonText}>显示自定义通知</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.title}>带操作按钮的通知</Text>
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#1890FF' }]}
            onPress={showNotificationWithAction}
          >
            <Text style={styles.buttonText}>显示带操作按钮的通知</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.title}>自定义样式</Text>
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#722ED1' }]}
            onPress={showCustomNotification}
          >
            <Text style={styles.buttonText}>显示自定义样式通知</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.title}>应用场景示例</Text>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>购物场景</Text>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#52C41A' }]}
            onPress={() => {
              notificationManager.success('商品已成功加入购物车', {
                icon: 'cart-outline',
                actionText: '查看购物车',
                onAction: () => {
                  notificationManager.info('跳转到购物车页面');
                },
              });
            }}
          >
            <Text style={styles.buttonText}>加入购物车</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#1890FF', marginTop: 10 }]}
            onPress={() => {
              notificationManager.info('您的订单已提交', {
                title: '订单提交成功',
                icon: 'checkmark-circle-outline',
                duration: 5000,
                actionText: '查看订单',
                onAction: () => {
                  notificationManager.info('跳转到订单页面');
                },
              });
            }}
          >
            <Text style={styles.buttonText}>提交订单</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#FAAD14', marginTop: 10 }]}
            onPress={() => {
              notificationManager.warning('您的购物车中有商品即将售罄', {
                icon: 'alert-circle-outline',
                duration: 5000,
                actionText: '立即结算',
                onAction: () => {
                  notificationManager.info('跳转到结算页面');
                },
              });
            }}
          >
            <Text style={styles.buttonText}>库存告警</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>用户操作场景</Text>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#52C41A' }]}
            onPress={() => {
              notificationManager.success('收藏成功', {
                icon: 'heart',
                duration: 2000,
              });
            }}
          >
            <Text style={styles.buttonText}>收藏商品</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#FF4D4F', marginTop: 10 }]}
            onPress={() => {
              notificationManager.error('网络连接失败', {
                title: '连接错误',
                icon: 'wifi-outline',
                duration: 5000,
                actionText: '重试',
                onAction: () => {
                  notificationManager.info('正在重新连接...');
                },
              });
            }}
          >
            <Text style={styles.buttonText}>网络错误</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>营销场景</Text>
          
          <TouchableOpacity
            style={[styles.button, styles.fullButton, { backgroundColor: '#722ED1' }]}
            onPress={() => {
              notificationManager.show({
                title: '限时优惠',
                message: '全场商品8折，仅限今日',
                icon: 'pricetag-outline',
                color: '#722ED1',
                duration: 6000,
                actionText: '立即查看',
                onAction: () => {
                  notificationManager.info('跳转到促销页面');
                },
              });
            }}
          >
            <Text style={styles.buttonText}>促销通知</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </NotificationProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: '22%',
  },
  fullButton: {
    width: '100%',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
  },
  inputRow: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d9d9d9',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  radioGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  radio: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
    marginBottom: 8,
  },
  radioActive: {
    backgroundColor: '#1890FF',
  },
  radioText: {
    fontSize: 12,
    color: '#333',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
});

export default NotificationExample;
