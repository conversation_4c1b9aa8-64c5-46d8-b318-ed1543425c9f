import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Animated,
  TouchableOpacity,
  Dimensions,
  ViewStyle,
  TextStyle,
  StyleProp,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

// Get screen dimensions
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type NotificationPosition = 'top' | 'bottom';
export type NotificationPlacement = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'top' | 'bottom';

export interface NotificationProps {
  /**
   * Whether the notification is visible
   */
  visible?: boolean;
  
  /**
   * Type of notification
   */
  type?: NotificationType;
  
  /**
   * Title of the notification
   */
  title?: string;
  
  /**
   * Message content of the notification
   */
  message: string;
  
  /**
   * Duration in milliseconds before auto-dismissing (0 means no auto-dismiss)
   */
  duration?: number;
  
  /**
   * Position of the notification
   */
  position?: NotificationPosition;
  
  /**
   * Placement of the notification (more specific than position)
   */
  placement?: NotificationPlacement;
  
  /**
   * Callback when notification is closed
   */
  onClose?: () => void;
  
  /**
   * Whether to show close button
   */
  closable?: boolean;
  
  /**
   * Custom style for the notification container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the title text
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for the message text
   */
  messageStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom icon name from Ionicons
   */
  icon?: string;
  
  /**
   * Whether to show the default icon based on type
   */
  showIcon?: boolean;
  
  /**
   * Custom color for the notification
   */
  color?: string;
  
  /**
   * Whether to show a progress bar indicating time until auto-dismiss
   */
  showProgress?: boolean;
  
  /**
   * Z-index for the notification
   */
  zIndex?: number;
  
  /**
   * Custom action button text
   */
  actionText?: string;
  
  /**
   * Callback when action button is pressed
   */
  onAction?: () => void;
  
  /**
   * Whether to allow swiping to dismiss
   */
  swipeable?: boolean;
}

// Notification component
const Notification: React.FC<NotificationProps> = ({
  visible = true,
  type = 'info',
  title,
  message,
  duration = 4000,
  position = 'top',
  placement = 'top',
  onClose,
  closable = true,
  containerStyle,
  titleStyle,
  messageStyle,
  icon,
  showIcon = true,
  color,
  showProgress = true,
  zIndex = 1000,
  actionText,
  onAction,
  swipeable = true,
}) => {
  // Animation values
  const translateY = useRef(new Animated.Value(position === 'top' ? -200 : 200)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;
  
  // State for swipe gesture
  const [swipeY, setSwipeY] = useState(0);
  const [isDismissing, setIsDismissing] = useState(false);
  
  // Timeout ref for auto-dismiss
  const dismissTimeout = useRef<NodeJS.Timeout | null>(null);
  
  // Get type-based color
  const getTypeColor = () => {
    if (color) return color;
    
    switch (type) {
      case 'success':
        return '#52C41A';
      case 'warning':
        return '#FAAD14';
      case 'error':
        return '#FF4D4F';
      case 'info':
      default:
        return '#1890FF';
    }
  };
  
  // Get type-based icon
  const getTypeIcon = () => {
    if (icon) return icon;
    
    switch (type) {
      case 'success':
        return 'checkmark-circle-outline';
      case 'warning':
        return 'alert-circle-outline';
      case 'error':
        return 'close-circle-outline';
      case 'info':
      default:
        return 'information-circle-outline';
    }
  };
  
  // Get placement style
  const getPlacementStyle = (): ViewStyle => {
    switch (placement) {
      case 'topLeft':
        return { top: 20, left: 20, alignSelf: 'flex-start' };
      case 'topRight':
        return { top: 20, right: 20, alignSelf: 'flex-end' };
      case 'bottomLeft':
        return { bottom: 20, left: 20, alignSelf: 'flex-start' };
      case 'bottomRight':
        return { bottom: 20, right: 20, alignSelf: 'flex-end' };
      case 'top':
        return { top: 20, alignSelf: 'center' };
      case 'bottom':
        return { bottom: 20, alignSelf: 'center' };
      default:
        return { top: 20, alignSelf: 'center' };
    }
  };
  
  // Show animation
  const showNotification = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Start progress animation if duration > 0
    if (duration > 0 && showProgress) {
      Animated.timing(progressWidth, {
        toValue: 1,
        duration: duration,
        useNativeDriver: false,
      }).start();
    }
  };
  
  // Hide animation
  const hideNotification = () => {
    if (isDismissing) return;
    
    setIsDismissing(true);
    
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -200 : 200,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onClose) {
        onClose();
      }
      setIsDismissing(false);
    });
  };
  
  // Handle auto-dismiss
  useEffect(() => {
    if (visible && duration > 0) {
      dismissTimeout.current = setTimeout(() => {
        hideNotification();
      }, duration);
    }
    
    return () => {
      if (dismissTimeout.current) {
        clearTimeout(dismissTimeout.current);
      }
    };
  }, [visible, duration]);
  
  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      showNotification();
    } else {
      hideNotification();
    }
  }, [visible]);
  
  // Handle swipe gesture
  const handleSwipeStart = () => {
    // Clear auto-dismiss timeout when user starts swiping
    if (dismissTimeout.current) {
      clearTimeout(dismissTimeout.current);
    }
  };
  
  const handleSwipeMove = (gestureState: { dy: number }) => {
    if (!swipeable) return;
    
    const newSwipeY = position === 'top' ? gestureState.dy : -gestureState.dy;
    
    // Only allow swiping in the correct direction
    if ((position === 'top' && newSwipeY < 0) || (position === 'bottom' && newSwipeY > 0)) {
      setSwipeY(newSwipeY);
    }
  };
  
  const handleSwipeEnd = () => {
    if (!swipeable) return;
    
    // If swiped far enough, dismiss
    if (Math.abs(swipeY) > 50) {
      hideNotification();
    } else {
      // Otherwise reset position
      setSwipeY(0);
      
      // Restart auto-dismiss if applicable
      if (duration > 0) {
        dismissTimeout.current = setTimeout(() => {
          hideNotification();
        }, duration);
      }
    }
  };
  
  // Calculate transform based on position and swipe
  const getTransform = () => {
    return [
      { translateY: Animated.add(translateY, new Animated.Value(swipeY)) },
    ];
  };
  
  return (
    <Animated.View
      style={[
        styles.container,
        getPlacementStyle(),
        {
          opacity,
          transform: getTransform(),
          zIndex,
        },
        containerStyle,
      ]}
    >
      <View
        style={[
          styles.content,
          { borderLeftColor: getTypeColor(), borderLeftWidth: 4 },
        ]}
      >
        {showIcon && (
          <Icon
            name={getTypeIcon()}
            size={24}
            color={getTypeColor()}
            style={styles.icon}
          />
        )}
        
        <View style={styles.textContainer}>
          {title && (
            <Text style={[styles.title, titleStyle]}>
              {title}
            </Text>
          )}
          
          <Text style={[styles.message, messageStyle]}>
            {message}
          </Text>
          
          {actionText && (
            <TouchableOpacity
              onPress={onAction}
              style={styles.actionButton}
            >
              <Text style={[styles.actionText, { color: getTypeColor() }]}>
                {actionText}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        {closable && (
          <TouchableOpacity
            onPress={hideNotification}
            style={styles.closeButton}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <Icon
              name="close"
              size={16}
              color="#999"
            />
          </TouchableOpacity>
        )}
      </View>
      
      {showProgress && duration > 0 && (
        <Animated.View
          style={[
            styles.progressBar,
            {
              backgroundColor: getTypeColor(),
              width: progressWidth.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              }),
            },
          ]}
        />
      )}
    </Animated.View>
  );
};

// NotificationManager for handling multiple notifications
interface NotificationItem extends NotificationProps {
  id: string;
}

class NotificationManager {
  private static instance: NotificationManager;
  private notifications: NotificationItem[] = [];
  private listeners: ((notifications: NotificationItem[]) => void)[] = [];
  
  private constructor() {}
  
  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    
    return NotificationManager.instance;
  }
  
  // Add notification
  public show(props: NotificationProps): string {
    const id = Math.random().toString(36).substring(2, 9);
    
    const notification: NotificationItem = {
      ...props,
      id,
      visible: true,
      onClose: () => {
        // Call original onClose if provided
        if (props.onClose) {
          props.onClose();
        }
        
        // Remove notification after animation completes
        setTimeout(() => {
          this.remove(id);
        }, 300);
      },
    };
    
    this.notifications.push(notification);
    this.notifyListeners();
    
    return id;
  }
  
  // Remove notification
  public remove(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id);
    
    if (index !== -1) {
      this.notifications.splice(index, 1);
      this.notifyListeners();
    }
  }
  
  // Hide notification (trigger animation)
  public hide(id: string): void {
    const notification = this.notifications.find(n => n.id === id);
    
    if (notification) {
      notification.visible = false;
      this.notifyListeners();
    }
  }
  
  // Clear all notifications
  public clear(): void {
    this.notifications = [];
    this.notifyListeners();
  }
  
  // Add listener
  public addListener(listener: (notifications: NotificationItem[]) => void): void {
    this.listeners.push(listener);
  }
  
  // Remove listener
  public removeListener(listener: (notifications: NotificationItem[]) => void): void {
    const index = this.listeners.indexOf(listener);
    
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  // Notify listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      listener([...this.notifications]);
    });
  }
  
  // Shorthand methods for different notification types
  public info(message: string, options: Partial<NotificationProps> = {}): string {
    return this.show({
      message,
      type: 'info',
      ...options,
    });
  }
  
  public success(message: string, options: Partial<NotificationProps> = {}): string {
    return this.show({
      message,
      type: 'success',
      ...options,
    });
  }
  
  public warning(message: string, options: Partial<NotificationProps> = {}): string {
    return this.show({
      message,
      type: 'warning',
      ...options,
    });
  }
  
  public error(message: string, options: Partial<NotificationProps> = {}): string {
    return this.show({
      message,
      type: 'error',
      ...options,
    });
  }
}

// Export singleton instance
export const notificationManager = NotificationManager.getInstance();

// NotificationProvider component to render notifications
export interface NotificationProviderProps {
  /**
   * Maximum number of notifications to show at once
   */
  maxCount?: number;
  
  /**
   * Default position for notifications
   */
  defaultPosition?: NotificationPosition;
  
  /**
   * Default placement for notifications
   */
  defaultPlacement?: NotificationPlacement;
  
  /**
   * Default duration for notifications
   */
  defaultDuration?: number;
  
  /**
   * Children components
   */
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  maxCount = 3,
  defaultPosition = 'top',
  defaultPlacement = 'top',
  defaultDuration = 4000,
  children,
}) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  
  // Listen for notification changes
  useEffect(() => {
    const handleNotificationsChange = (updatedNotifications: NotificationItem[]) => {
      // Apply default props and limit to maxCount
      const processedNotifications = updatedNotifications
        .map(notification => ({
          ...notification,
          position: notification.position || defaultPosition,
          placement: notification.placement || defaultPlacement,
          duration: notification.duration !== undefined ? notification.duration : defaultDuration,
        }))
        .slice(0, maxCount);
      
      setNotifications(processedNotifications);
    };
    
    notificationManager.addListener(handleNotificationsChange);
    
    return () => {
      notificationManager.removeListener(handleNotificationsChange);
    };
  }, [maxCount, defaultPosition, defaultPlacement, defaultDuration]);
  
  return (
    <>
      {children}
      
      {notifications.map(notification => (
        <Notification
          key={notification.id}
          {...notification}
        />
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: Math.min(SCREEN_WIDTH - 40, 400),
    minHeight: 60,
    backgroundColor: '#fff',
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    padding: 12,
  },
  icon: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#666',
  },
  closeButton: {
    padding: 4,
  },
  progressBar: {
    height: 2,
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  actionButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default Notification;
