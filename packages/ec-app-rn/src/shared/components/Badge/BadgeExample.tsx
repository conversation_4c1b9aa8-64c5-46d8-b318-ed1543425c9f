import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image } from 'react-native';
import Badge from './Badge';
import Icon from 'react-native-vector-icons/Ionicons';

const BadgeExample = () => {
  const [count, setCount] = useState(5);
  
  const incrementCount = () => {
    setCount(prevCount => prevCount + 1);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>基础徽标</Text>
      <View style={styles.row}>
        <Badge content={5} containerStyle={styles.marginRight} />
        <Badge content="NEW" containerStyle={styles.marginRight} />
        <Badge dot containerStyle={styles.marginRight} />
        <Badge content={100} max={99} />
      </View>
      
      <Text style={styles.title}>不同颜色</Text>
      <View style={styles.row}>
        <Badge content={1} type="primary" containerStyle={styles.marginRight} />
        <Badge content={2} type="success" containerStyle={styles.marginRight} />
        <Badge content={3} type="warning" containerStyle={styles.marginRight} />
        <Badge content={4} type="danger" containerStyle={styles.marginRight} />
        <Badge content={5} type="info" />
      </View>
      
      <Text style={styles.title}>自定义颜色</Text>
      <View style={styles.row}>
        <Badge 
          content={8} 
          backgroundColor="#722ED1" 
          containerStyle={styles.marginRight} 
        />
        <Badge 
          content="HOT" 
          backgroundColor="#F5222D" 
          textColor="#FFFFFF" 
        />
      </View>
      
      <Text style={styles.title}>徽标位置</Text>
      <View style={styles.row}>
        <Badge content={1} position="top-right" containerStyle={styles.marginRight}>
          <View style={styles.box} />
        </Badge>
        
        <Badge content={2} position="top-left" containerStyle={styles.marginRight}>
          <View style={styles.box} />
        </Badge>
        
        <Badge content={3} position="bottom-right" containerStyle={styles.marginRight}>
          <View style={styles.box} />
        </Badge>
        
        <Badge content={4} position="bottom-left">
          <View style={styles.box} />
        </Badge>
      </View>
      
      <Text style={styles.title}>图标徽标</Text>
      <View style={styles.row}>
        <Badge content={count} containerStyle={styles.marginRight}>
          <TouchableOpacity onPress={incrementCount} style={styles.iconContainer}>
            <Icon name="notifications-outline" size={24} color="#333" />
          </TouchableOpacity>
        </Badge>
        
        <Badge dot type="danger" position="top-right" containerStyle={styles.marginRight}>
          <TouchableOpacity style={styles.iconContainer}>
            <Icon name="mail-outline" size={24} color="#333" />
          </TouchableOpacity>
        </Badge>
        
        <Badge content="99+" position="top-right">
          <TouchableOpacity style={styles.iconContainer}>
            <Icon name="chatbubble-outline" size={24} color="#333" />
          </TouchableOpacity>
        </Badge>
      </View>
      
      <Text style={styles.title}>自定义偏移</Text>
      <View style={styles.row}>
        <Badge 
          content={8} 
          position="top-right" 
          offset={{ x: 5, y: -5 }}
          containerStyle={styles.marginRight}
        >
          <View style={styles.box} />
        </Badge>
        
        <Badge 
          dot 
          position="bottom-left" 
          offset={{ x: -3, y: -3 }}
        >
          <Image 
            source={{ uri: 'https://i.pravatar.cc/100' }} 
            style={styles.avatar} 
          />
        </Badge>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  marginRight: {
    marginRight: 20,
  },
  box: {
    width: 40,
    height: 40,
    backgroundColor: '#D9D9D9',
    borderRadius: 4,
  },
  iconContainer: {
    padding: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
});

export default BadgeExample;
