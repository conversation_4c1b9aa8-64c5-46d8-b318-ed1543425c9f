import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';

export type BadgePosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center';
export type BadgeType = 'primary' | 'success' | 'warning' | 'danger' | 'info';

export interface BadgeProps {
  /**
   * Content to display inside the badge (number or text)
   */
  content?: number | string;
  
  /**
   * Whether to show the badge as a dot without content
   */
  dot?: boolean;
  
  /**
   * Maximum number to display (shows {max}+ if content exceeds max)
   */
  max?: number;
  
  /**
   * Badge type/color variant
   */
  type?: BadgeType;
  
  /**
   * Position of the badge when used as a wrapper
   */
  position?: BadgePosition;
  
  /**
   * Whether the badge is visible
   */
  visible?: boolean;
  
  /**
   * Custom background color
   */
  backgroundColor?: string;
  
  /**
   * Custom text color
   */
  textColor?: string;
  
  /**
   * Custom size (width/height) for dot badge
   */
  dotSize?: number;
  
  /**
   * Custom style for the badge container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the badge text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Children to wrap with the badge
   */
  children?: React.ReactNode;
  
  /**
   * Offset for the badge position (in points)
   */
  offset?: { x?: number; y?: number };
}

const Badge: React.FC<BadgeProps> = ({
  content,
  dot = false,
  max = 99,
  type = 'primary',
  position = 'top-right',
  visible = true,
  backgroundColor,
  textColor,
  dotSize = 8,
  containerStyle,
  textStyle,
  children,
  offset = { x: 0, y: 0 },
}) => {
  if (!visible) {
    return <>{children}</>;
  }
  
  // Determine badge content to display
  const displayContent = () => {
    if (dot) return null;
    
    if (typeof content === 'number' && content > max) {
      return `${max}+`;
    }
    
    return content;
  };
  
  // Get background color based on type
  const getBgColor = () => {
    if (backgroundColor) return backgroundColor;
    
    switch (type) {
      case 'success': return '#52C41A';
      case 'warning': return '#FAAD14';
      case 'danger': return '#FF4D4F';
      case 'info': return '#1890FF';
      default: return '#FF4D4F'; // primary/default
    }
  };
  
  // Get position styles
  const getPositionStyle = () => {
    const posStyles: ViewStyle = {
      position: 'absolute',
    };
    
    switch (position) {
      case 'top-right':
        posStyles.top = -4 + (offset.y || 0);
        posStyles.right = -4 + (offset.x || 0);
        break;
      case 'top-left':
        posStyles.top = -4 + (offset.y || 0);
        posStyles.left = -4 + (offset.x || 0);
        break;
      case 'bottom-right':
        posStyles.bottom = -4 + (offset.y || 0);
        posStyles.right = -4 + (offset.x || 0);
        break;
      case 'bottom-left':
        posStyles.bottom = -4 + (offset.y || 0);
        posStyles.left = -4 + (offset.x || 0);
        break;
      case 'center':
        posStyles.top = '50%';
        posStyles.left = '50%';
        posStyles.transform = [{ translateX: -10 }, { translateY: -10 }];
        break;
    }
    
    return posStyles;
  };
  
  // Standalone badge
  const renderBadge = () => {
    const badgeContent = displayContent();
    const bgColor = getBgColor();
    
    const badgeStyles = [
      styles.badge,
      { backgroundColor: bgColor },
      dot ? { width: dotSize, height: dotSize, borderRadius: dotSize / 2 } : null,
      !dot && !badgeContent ? styles.emptyBadge : null,
      containerStyle,
    ];
    
    return (
      <View style={badgeStyles}>
        {!dot && badgeContent !== null && (
          <Text
            style={[
              styles.text,
              textColor ? { color: textColor } : null,
              textStyle,
            ]}
            numberOfLines={1}
          >
            {badgeContent}
          </Text>
        )}
      </View>
    );
  };
  
  // If no children, render standalone badge
  if (!children) {
    return renderBadge();
  }
  
  // If children provided, render badge as wrapper
  return (
    <View style={styles.wrapper}>
      {children}
      <View style={[styles.positionWrapper, getPositionStyle()]}>
        {renderBadge()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
  },
  positionWrapper: {
    position: 'absolute',
  },
  badge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF4D4F',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  emptyBadge: {
    width: 10,
    height: 10,
    borderRadius: 5,
    padding: 0,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default Badge;
