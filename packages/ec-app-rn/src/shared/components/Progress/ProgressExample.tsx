import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import Progress from './Progress';

const ProgressExample = () => {
  const [percent, setPercent] = useState(30);
  const [circlePercent, setCirclePercent] = useState(75);
  
  // Demo for animated progress
  useEffect(() => {
    const interval = setInterval(() => {
      setCirclePercent(prev => {
        const next = prev >= 100 ? 0 : prev + 1;
        return next;
      });
    }, 100);
    
    return () => clearInterval(interval);
  }, []);
  
  const increase = () => {
    setPercent(prev => Math.min(100, prev + 10));
  };
  
  const decrease = () => {
    setPercent(prev => Math.max(0, prev - 10));
  };
  
  const formatWithIcon = (percent?: number) => {
    if (percent && percent >= 100) {
      return '完成!';
    }
    return `${percent}%`;
  };
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>基础进度条</Text>
      <View style={styles.section}>
        <Progress percent={30} />
      </View>
      
      <Text style={styles.title}>不同状态</Text>
      <View style={styles.section}>
        <Text style={styles.label}>正常：</Text>
        <Progress percent={percent} status="normal" containerStyle={styles.marginBottom} />
        
        <Text style={styles.label}>成功：</Text>
        <Progress percent={100} status="success" containerStyle={styles.marginBottom} />
        
        <Text style={styles.label}>异常：</Text>
        <Progress percent={70} status="exception" containerStyle={styles.marginBottom} />
        
        <Text style={styles.label}>活跃：</Text>
        <Progress percent={50} status="active" />
      </View>
      
      <Text style={styles.title}>自定义颜色和样式</Text>
      <View style={styles.section}>
        <Progress 
          percent={40} 
          strokeColor="#1890FF" 
          containerStyle={styles.marginBottom} 
        />
        
        <Progress 
          percent={60} 
          strokeColor="#13C2C2" 
          strokeWidth={12} 
          containerStyle={styles.marginBottom} 
        />
        
        <Progress 
          percent={80} 
          strokeColor="#722ED1" 
          strokeLinecap="square" 
        />
      </View>
      
      <Text style={styles.title}>不显示进度信息</Text>
      <View style={styles.section}>
        <Progress percent={50} showInfo={false} />
      </View>
      
      <Text style={styles.title}>自定义格式</Text>
      <View style={styles.section}>
        <Progress 
          percent={percent} 
          format={formatWithIcon} 
        />
      </View>
      
      <Text style={styles.title}>动态进度条</Text>
      <View style={styles.section}>
        <Progress percent={percent} containerStyle={styles.marginBottom} />
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={decrease}
          >
            <Text style={styles.buttonText}>-10%</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={increase}
          >
            <Text style={styles.buttonText}>+10%</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <Text style={styles.title}>步骤进度条</Text>
      <View style={styles.section}>
        <Progress 
          percent={60} 
          steps={5} 
          containerStyle={styles.marginBottom} 
        />
        
        <Progress 
          percent={40} 
          steps={5} 
          strokeColor="#1890FF" 
          stepGap={4} 
          strokeLinecap="square" 
        />
      </View>
      
      <Text style={styles.title}>环形进度条</Text>
      <View style={styles.circleSection}>
        <Progress 
          type="circle" 
          percent={75} 
          containerStyle={styles.marginRight} 
        />
        
        <Progress 
          type="circle" 
          percent={100} 
          status="success" 
          containerStyle={styles.marginRight} 
        />
        
        <Progress 
          type="circle" 
          percent={50} 
          status="exception" 
        />
      </View>
      
      <View style={styles.circleSection}>
        <Progress 
          type="circle" 
          percent={circlePercent} 
          strokeColor="#1890FF" 
          circleSize={80} 
          containerStyle={styles.marginRight} 
        />
        
        <Progress 
          type="circle" 
          percent={circlePercent} 
          strokeColor="#13C2C2" 
          circleSize={80} 
          strokeWidth={4} 
          containerStyle={styles.marginRight} 
        />
        
        <Progress 
          type="circle" 
          percent={circlePercent} 
          strokeColor="#722ED1" 
          circleSize={80} 
          showInfo={false} 
        />
      </View>
      
      <Text style={styles.title}>应用场景：用户等级</Text>
      <View style={styles.levelSection}>
        <View style={styles.levelHeader}>
          <Text style={styles.levelTitle}>白金会员</Text>
          <Text style={styles.levelPoints}>2,500 / 5,000 积分</Text>
        </View>
        
        <Progress 
          percent={50} 
          strokeColor="#FFB800" 
          showInfo={false} 
          containerStyle={styles.marginBottom} 
        />
        
        <Text style={styles.levelHint}>再获得2,500积分升级为钻石会员</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  circleSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
  },
  marginBottom: {
    marginBottom: 15,
  },
  marginRight: {
    marginRight: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  button: {
    backgroundColor: '#FF4D4F',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 4,
    marginHorizontal: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
  },
  levelSection: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  levelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  levelTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  levelPoints: {
    fontSize: 14,
    color: '#666',
  },
  levelHint: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default ProgressExample;
