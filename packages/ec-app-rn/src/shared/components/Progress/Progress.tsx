import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Animated,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';

export type ProgressType = 'line' | 'circle';
export type ProgressStatus = 'normal' | 'success' | 'exception' | 'active';
export type ProgressStrokeLinecap = 'round' | 'square';

export interface ProgressProps {
  /**
   * Type of progress bar
   */
  type?: ProgressType;
  
  /**
   * Percent of progress (0-100)
   */
  percent?: number;
  
  /**
   * Status of progress
   */
  status?: ProgressStatus;
  
  /**
   * Whether to show percentage text
   */
  showInfo?: boolean;
  
  /**
   * Custom format function for percentage text
   */
  format?: (percent?: number) => React.ReactNode;
  
  /**
   * Width of stroke for line progress
   */
  strokeWidth?: number;
  
  /**
   * Style of progress stroke
   */
  strokeLinecap?: ProgressStrokeLinecap;
  
  /**
   * Color of progress bar
   */
  strokeColor?: string | { from: string; to: string };
  
  /**
   * Color of unfilled part
   */
  trailColor?: string;
  
  /**
   * Width of progress bar (for line type)
   */
  width?: number | string;
  
  /**
   * Custom style for progress container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for progress text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Whether to animate progress changes
   */
  animated?: boolean;
  
  /**
   * Animation duration in ms
   */
  animationDuration?: number;
  
  /**
   * Size of circle progress (for circle type)
   */
  circleSize?: number;
  
  /**
   * Whether to show steps in progress bar
   */
  steps?: number;
  
  /**
   * Gap between steps
   */
  stepGap?: number;
}

const Progress: React.FC<ProgressProps> = ({
  type = 'line',
  percent = 0,
  status = 'normal',
  showInfo = true,
  format,
  strokeWidth = 8,
  strokeLinecap = 'round',
  strokeColor = '#FF4D4F',
  trailColor = '#f5f5f5',
  width = '100%',
  containerStyle,
  textStyle,
  animated = true,
  animationDuration = 300,
  circleSize = 120,
  steps,
  stepGap = 2,
}) => {
  // Ensure percent is between 0-100
  const boundedPercent = Math.max(0, Math.min(100, percent));
  
  // Animation value
  const progressAnim = useRef(new Animated.Value(0)).current;
  
  // Update animation when percent changes
  useEffect(() => {
    if (animated) {
      Animated.timing(progressAnim, {
        toValue: boundedPercent,
        duration: animationDuration,
        useNativeDriver: false,
      }).start();
    } else {
      progressAnim.setValue(boundedPercent);
    }
  }, [boundedPercent, animated, animationDuration, progressAnim]);
  
  // Get status color
  const getStatusColor = () => {
    if (typeof strokeColor === 'string') {
      switch (status) {
        case 'success':
          return '#52C41A';
        case 'exception':
          return '#FF4D4F';
        case 'active':
        case 'normal':
        default:
          return strokeColor;
      }
    }
    
    return strokeColor;
  };
  
  // Format percent text
  const formatPercentage = () => {
    if (format) {
      return format(boundedPercent);
    }
    return `${Math.round(boundedPercent)}%`;
  };
  
  // Render line progress
  const renderLineProgress = () => {
    const statusColor = getStatusColor();
    const progressWidth = typeof width === 'number' ? width : '100%';
    
    // For gradient colors
    const gradientColors = typeof strokeColor !== 'string' ? strokeColor : null;
    
    // Animated width
    const animatedWidth = progressAnim.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    });
    
    // Render steps if needed
    if (steps && steps > 0) {
      const stepWidth = (100 / steps) - (stepGap * (steps - 1) / steps);
      const stepItems = [];
      
      for (let i = 0; i < steps; i++) {
        const isActive = i * (100 / steps) < boundedPercent;
        stepItems.push(
          <View
            key={i}
            style={[
              styles.step,
              {
                width: `${stepWidth}%`,
                backgroundColor: isActive ? statusColor : trailColor,
                marginRight: i < steps - 1 ? stepGap : 0,
                borderRadius: strokeLinecap === 'round' ? strokeWidth / 2 : 0,
                height: strokeWidth,
              },
            ]}
          />
        );
      }
      
      return (
        <View style={[styles.stepsContainer, { width: progressWidth }]}>
          {stepItems}
        </View>
      );
    }
    
    return (
      <View
        style={[
          styles.lineContainer,
          { 
            height: strokeWidth,
            backgroundColor: trailColor,
            borderRadius: strokeLinecap === 'round' ? strokeWidth / 2 : 0,
            width: progressWidth,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.lineProgress,
            {
              width: animatedWidth,
              height: strokeWidth,
              borderRadius: strokeLinecap === 'round' ? strokeWidth / 2 : 0,
              backgroundColor: gradientColors ? undefined : statusColor,
            },
            status === 'active' && styles.activeProgress,
          ]}
        >
          {gradientColors && (
            <View
              style={[
                StyleSheet.absoluteFill,
                {
                  backgroundColor: statusColor,
                },
              ]}
            />
          )}
        </Animated.View>
      </View>
    );
  };
  
  // Render circle progress
  const renderCircleProgress = () => {
    const statusColor = getStatusColor();
    const size = circleSize;
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = progressAnim.interpolate({
      inputRange: [0, 100],
      outputRange: [circumference, 0],
    });
    
    return (
      <View style={[styles.circleContainer, { width: size, height: size }]}>
        {/* Background circle */}
        <View
          style={[
            StyleSheet.absoluteFill,
            styles.circleBackground,
            {
              borderWidth: strokeWidth,
              borderColor: trailColor,
              width: size,
              height: size,
              borderRadius: size / 2,
            },
          ]}
        />
        
        {/* Progress circle */}
        <Animated.View
          style={[
            styles.circleProgress,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              borderWidth: strokeWidth,
              borderColor: statusColor,
              transform: [
                { rotateZ: '-90deg' },
              ],
              borderTopColor: 'transparent',
              borderRightColor: 'transparent',
              opacity: boundedPercent === 0 ? 0 : 1,
            },
            {
              transform: [
                { rotateZ: `-${90 + (boundedPercent * 3.6)}deg` },
              ],
            },
          ]}
        />
        
        {/* Center content */}
        {showInfo && (
          <View style={styles.circleInfo}>
            <Text
              style={[
                styles.circleText,
                { color: statusColor },
                textStyle,
              ]}
            >
              {formatPercentage()}
            </Text>
          </View>
        )}
      </View>
    );
  };
  
  return (
    <View
      style={[
        styles.container,
        { flexDirection: type === 'line' ? 'row' : 'column' },
        containerStyle,
      ]}
    >
      {type === 'line' ? renderLineProgress() : renderCircleProgress()}
      
      {type === 'line' && showInfo && (
        <Text
          style={[
            styles.infoText,
            { color: getStatusColor() },
            textStyle,
          ]}
        >
          {formatPercentage()}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  lineContainer: {
    overflow: 'hidden',
  },
  lineProgress: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
  activeProgress: {
    position: 'relative',
  },
  infoText: {
    marginLeft: 8,
    fontSize: 14,
  },
  circleContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleBackground: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleProgress: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleInfo: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleText: {
    fontSize: 18,
    fontWeight: '500',
  },
  stepsContainer: {
    flexDirection: 'row',
  },
  step: {
    height: 8,
  },
});

export default Progress;
