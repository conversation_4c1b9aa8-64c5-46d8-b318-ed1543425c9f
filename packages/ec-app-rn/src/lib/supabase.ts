import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
// In a real app, these would be loaded from environment variables or a config file
// For this implementation, we'll use placeholder values that should be replaced with actual values
const supabaseUrl = 'https://xdgosfycbybzsjbkmqgm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhkZ29zZnljYnlienNqYmttcWdtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxODkxMTEsImV4cCI6MjA2Mjc2NTExMX0.NMIm0hxL2MAL3Qo927T7F4MnCciVkot7KXGMujxAPqs';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth helper functions
export const getSession = async () => {
  const { data, error } = await supabase.auth.getSession();
  return { session: data.session, error };
};

export const getUser = async () => {
  const { customer: data, error} = await getCustomerByUserId();
  return { user: data, error };
};

// Helper function to get customer by user ID
export const getCustomerByUserId = async (userId?: string) => {
  try {
    // If userId is not provided, get the current user
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
    }
    
    if (!userId) {
      return { customer: null, error: new Error('User not authenticated') };
    }
    
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    return { customer: data, error };
  } catch (error) {
    return { customer: null, error: error as Error };
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { user: data.user, session: data.session, error };
};

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  return { user: data.user, session: data.session, error };
};

export const resetPassword = async (email: string) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: 'ec-app-rn://reset-password',
  });
  return { error };
};

export const updatePassword = async (password: string) => {
  const { error } = await supabase.auth.updateUser({
    password,
  });
  return { error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

// E-commerce specific functions

// Products
export const getProducts = async ({ 
  categoryId = null,
  limit = 10, 
  offset = 0, 
  sortBy = 'created_at', 
  sortOrder = 'desc' 
} = {}) => {
  let query = supabase
    .from('products')
    .select('*, categories(*)')
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(offset, offset + limit - 1);
  
  if (categoryId) {
    query = query.eq('category_id', categoryId);
  }
  
  const { data, error, count } = await query;
  return { products: data, error, count };
};

export const getProductById = async (productId: string) => {
  const { data, error } = await supabase
    .from('products')
    .select('*, categories(*)')
    .eq('id', productId)
    .single();
  
  return { product: data, error };
};

export const getProductsByCategory = async (categorySlug: string, limit = 10, offset = 0) => {
  // First get the category ID from the slug
  const { data: category } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', categorySlug)
    .single();
  
  if (!category) return { products: [], error: new Error('Category not found') };
  
  // Then get products with that category ID
  return getProducts({ categoryId: category.id, limit, offset });
};

// Categories
export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name');
  
  return { categories: data, error };
};

// User Profile
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  return { profile: data, error };
};

// Orders
export const getUserOrders = async (userId: string, status?: string) => {
  try {
    // First check current logged in user
    const { customer: currentUser } = await getCustomerByUserId();
    
    // If no userId is provided, use the current logged in user's ID
    const effectiveUserId = userId || currentUser?.id;
    if (!effectiveUserId) {
      return { orders: [], error: new Error('No valid user ID') };
    }
    
    // Query orders using customer_id
    let { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('customer_id', effectiveUserId)
      .order('created_at', { ascending: false });
    
    if (error) {
      return { orders: [], error };
    }
    
    if (!data || data.length === 0) {
      return { orders: [], error: null };
    }
    
    // Now we need to get order items for each order
    const ordersWithItems = await Promise.all(
      data.map(async (order: any) => {
        // Query order items for each order
        const { data: orderItems, error: itemsError } = await supabase
          .from('order_items')
          .select('*')
          .eq('order_id', order.id);
        
        if (itemsError) {
          return { ...order, order_items: [] };
        }
        
        // Get product information for each order item
        const itemsWithProducts = await Promise.all(
          (orderItems || []).map(async (item: any) => {
            if (!item.product_id) {
              return {
                ...item,
                products: { name: 'Unknown Product', image_url: 'https://via.placeholder.com/80' }
              };
            }
            
            // Query product information
            const { data: product, error: productError } = await supabase
              .from('products')
              .select('*')
              .eq('id', item.product_id)
              .single();
            
            if (productError || !product) {
              return {
                ...item,
                products: { name: 'Unknown Product', image_url: 'https://via.placeholder.com/80' }
              };
            }
            
            return {
              ...item,
              products: product
            };
          })
        );
        
        return {
          ...order,
          order_items: itemsWithProducts
        };
      })
    );
    
    // Filter by status if specified
    let filteredOrders = ordersWithItems;
    if (status && status !== 'ALL') {
      filteredOrders = ordersWithItems.filter((order: any) => {
        const orderStatus = String(order.status || '').toLowerCase();
        const filterStatus = String(status).toLowerCase();
        return orderStatus === filterStatus;
      });
    }
    
    if (!filteredOrders || filteredOrders.length === 0) {
      return { orders: [], error: null };
    }
    
    // Process order data to ensure correct structure
    const processedOrders = filteredOrders.map((order: any) => {
      // Process each order item to ensure correct data format
      const processedItems = (order.order_items || []).map((item: any) => {
        const productInfo = item.products || { name: 'Unknown Product', image_url: 'https://via.placeholder.com/80' };
        
        return {
          id: item.id || `temp-${Math.random().toString(36).substring(7)}`,
          quantity: item.quantity || 1,
          price: item.price || 0,
          products: {
            name: productInfo.name || 'Unknown Product',
            image_url: productInfo.image_url || 'https://via.placeholder.com/80'
          }
        };
      });
      
      // Calculate total amount if not provided
      const totalAmount = order.total_amount || 
        processedItems.reduce((sum: number, item: any) => 
          sum + (item.price || 0) * (item.quantity || 1), 0);
      
      // Return processed order
      return {
        id: order.id,
        created_at: order.created_at || new Date().toISOString(),
        total_amount: totalAmount,
        status: order.status || 'pending_payment',
        order_items: processedItems,
        customer_id: order.customer_id || effectiveUserId
      };
    });
    
    return { orders: processedOrders, error: null };
  } catch (e) {
    return { orders: [], error: e as Error };
  }
};

// Cart
export const getCart = async (userId: string) => {
  const { data, error } = await supabase
    .from('cart_items')
    .select('*, products(*, categories(*))')
    .eq('user_id', userId);
  
  return { cartItems: data, error };
};

export const addToCart = async (userId: string, productId: string, quantity = 1) => {
  // Check if item already exists in cart
  const { data: existingItem } = await supabase
    .from('cart_items')
    .select('*')
    .eq('user_id', userId)
    .eq('product_id', productId)
    .single();
  
  if (existingItem) {
    // Update quantity
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity: existingItem.quantity + quantity })
      .eq('id', existingItem.id)
      .select();
    
    return { cartItem: data?.[0], error };
  } else {
    // Add new item
    const { data, error } = await supabase
      .from('cart_items')
      .insert({
        user_id: userId,
        product_id: productId,
        quantity
      })
      .select();
    
    return { cartItem: data?.[0], error };
  }
};

export const updateCartItemQuantity = async (cartItemId: string, quantity: number) => {
  const { data, error } = await supabase
    .from('cart_items')
    .update({ quantity })
    .eq('id', cartItemId)
    .select();
  
  return { cartItem: data?.[0], error };
};

export const removeFromCart = async (cartItemId: string) => {
  const { error } = await supabase
    .from('cart_items')
    .delete()
    .eq('id', cartItemId);
  
  return { error };
};

// Favorites/Wishlist
export const getFavorites = async (userId: string) => {
  const { data, error } = await supabase
    .from('favorites')
    .select('*, products(*)')
    .eq('customer_id', userId);
  
  return { favorites: data, error };
};

export const addToFavorites = async (userId: string, productId: string) => {
  const { data, error } = await supabase
    .from('favorites')
    .insert({
      customer_id: userId,
      product_id: productId
    })
    .select();
  
  return { favorite: data?.[0], error };
};

export const removeFromFavorites = async (favoriteId: string) => {
  const { error } = await supabase
    .from('favorites')
    .delete()
    .eq('id', favoriteId);
  
  return { error };
};

// Check if a product is in favorites
export const isProductInFavorites = async (userId: string, productId: string) => {
  const { data, error } = await supabase
    .from('favorites')
    .select('id')
    .eq('customer_id', userId)
    .eq('product_id', productId)
    .single();
  
  return { isFavorite: !!data, favoriteId: data?.id, error };
};
