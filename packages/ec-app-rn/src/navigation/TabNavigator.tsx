import React from 'react';
import { Platform, StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/FontAwesome';
import { Colors } from '../constants/Colors';
import { useColorScheme } from '../shared/hooks/useColorScheme';

// Import tab screens
import HomeScreen from '../features/home/<USER>/HomeScreen';
import CategoryScreen from '../features/product/screens/CategoryScreen';
import InterestScreen from '../features/profile/screens/InterestScreen';
import CartScreen from '../features/order/screens/CartScreen';
import ProfileScreen from '../features/profile/screens/ProfileScreen';

// Import components
import HapticTabButton from '../shared/components/HapticTabButton';
import TabBarBackground from '../shared/components/TabBarBackground';

// Define the tab navigator parameter list
export type TabParamList = {
  Home: undefined;
  Category: undefined;
  Interest: undefined;
  Cart: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

export default function TabNavigator() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: colors.tabIconSelected,
        tabBarInactiveTintColor: colors.tabIconDefault,
        headerShown: false,
        tabBarButton: (props) => <HapticTabButton {...props} />,
        tabBarBackground: () => <TabBarBackground />,
        tabBarStyle: Platform.select({
          ios: {
            backgroundColor: colors.tabBackground,
            borderTopColor: colors.tabBorder,
            borderTopWidth: 1,
            height: 49,
          },
          default: {
            backgroundColor: colors.tabBackground,
            borderTopColor: colors.tabBorder,
            borderTopWidth: 1,
            height: 49,
          },
        }),
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: '首页',
          tabBarIcon: ({ color, size }) => (
            <Icon name="home" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Category"
        component={CategoryScreen}
        options={{
          title: '分类',
          tabBarIcon: ({ color, size }) => (
            <Icon name="th-large" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Interest"
        component={InterestScreen}
        options={{
          title: '兴趣',
          tabBarIcon: ({ color, size }) => (
            <Icon name="heart" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Cart"
        component={CartScreen}
        options={{
          title: '购物车',
          tabBarIcon: ({ color, size }) => (
            <Icon name="shopping-cart" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: '我的',
          tabBarIcon: ({ color, size }) => (
            <Icon name="user" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  // Add any additional styles here if needed
});
