import React from 'react';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { Text } from 'react-native'; // Import Text for fallback
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigatorScreenParams } from '@react-navigation/native';
import { useColorScheme } from '../shared/hooks/useColorScheme';
import TabNavigator from './TabNavigator';
import ProductDetailsScreen from '../features/product/screens/ProductDetailsScreen';
import ProductsScreen from '../features/product/screens/ProductsScreen';
import LoginScreen from '../features/auth/screens/LoginScreen';
import SignupScreen from '../features/auth/screens/SignupScreen';
import ForgotPasswordScreen from '../features/auth/screens/ForgotPasswordScreen';
import WelcomeScreen from '../features/auth/screens/WelcomeScreen';
import CheckoutScreen from '../features/order/screens/CheckoutScreen';
import OrderSuccessScreen from '../features/order/screens/OrderSuccessScreen';
import OrdersScreen from '../features/order/screens/OrdersScreen';
import SettingsScreen from '../features/profile/screens/SettingsScreen';
import NotFoundScreen from '../app/screens/NotFoundScreen';
import InstantScreen from '../features/instant/screens/InstantScreen';
import ExploreScreen from '../features/product/screens/ExploreScreen';
import ComponentListScreen from '../features/profile/screens/ComponentListScreen';
import CardDetailScreen from '../features/profile/screens/CardDetailScreen';
import QuantitySelectorDemoScreen from '../features/profile/screens/QuantitySelectorDemoScreen';
import DialogDemoScreen from '../features/profile/screens/DialogDemoScreen';
import PopupDemoScreen from '../features/profile/screens/PopupDemoScreen';
import TagDemoScreen from '../features/profile/screens/TagDemoScreen';

// Import component example screens
import CarouselExample from '../shared/components/Carousel/CarouselExample';
import RatingExample from '../shared/components/Rating/RatingExample';
import BadgeExample from '../shared/components/Badge/BadgeExample';
import ButtonExample from '../shared/components/Button/ButtonExample';
import SwitchExample from '../shared/components/Switch/SwitchExample';
import ProgressExample from '../shared/components/Progress/ProgressExample';
import StepperExample from '../shared/components/Stepper/StepperExample';
import CheckboxExample from '../shared/components/Checkbox/CheckboxExample';
import LoadingExample from '../shared/components/Loading/LoadingExample';
import NotificationExample from '../shared/components/Notification/NotificationExample';
import ListExample from '../shared/components/List/ListExample';
import ProductCardExample from '../shared/components/ProductCard/ProductCardExample';
import { AddToCartButtonExample } from '../shared/components/AddToCartButton';
import { NavigationBarExample } from '../shared/components/NavigationBar';
import { MenuBarExample } from '../shared/components/MenuBar';
import ToastExample from '../shared/components/Toast/ToastExample';

// Define the root stack parameter list
export type TabParamList = {
  Home: undefined;
  Category: undefined;
  Interest: undefined;
  Cart: undefined;
  Profile: undefined;
};

export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Signup: undefined;
  ForgotPassword: undefined;
  Main: NavigatorScreenParams<TabParamList>;
  ProductDetails: { id: string };
  Products: { category?: string };
  Checkout: undefined;
  OrderSuccess: { orderId: string };
  Orders: undefined;
  Settings: undefined;
  ComponentList: undefined;
  CardDetail: undefined;
  QuantitySelectorDemo: undefined;
  DialogDemo: undefined;
  PopupDemo: undefined;
  TagDemo: undefined;
  NotFound: undefined;
  Instant: undefined;
  Explore: undefined;
  CarouselExample: undefined;
  RatingExample: undefined;
  BadgeExample: undefined;
  ButtonExample: undefined;
  SwitchExample: undefined;
  ProgressExample: undefined;
  StepperExample: undefined;
  CheckboxExample: undefined;
  LoadingExample: undefined;
  NotificationExample: undefined;
  ListExample: undefined;
  ToastExample: undefined;
  ProductCardExample: undefined;
  AddToCartButtonExample: undefined;
  NavigationBarExample: undefined;
  MenuBarExample: undefined;
};

// Create the root stack navigator
const Stack = createNativeStackNavigator<RootStackParamList>();

const linking = {
  prefixes: ['ecapp://'], // Your custom scheme
  config: {
    screens: {
      Welcome: 'welcome',
      Login: 'login',
      Signup: 'signup',
      ForgotPassword: 'forgot-password',
      Main: { // This is your TabNavigator
        path: 'main', // e.g., ecapp://main
        screens: { // Screens within TabNavigator
          Home: 'home', // e.g., ecapp://main/home
          Category: 'category',
          Interest: 'interest',
          Cart: 'cart',
          Profile: 'profile',
          PopupDemo: {
            screens: {
              PopupDemo: 'popup-demo',
            },
          },
          TagDemo: {
            screens: {
              TagDemo: 'tag-demo',
            },
          },
        },
      },
      ProductDetails: 'products/:id',
      Products: 'products',
      Checkout: 'checkout',
      OrderSuccess: 'order-success/:orderId',
      Orders: 'orders',
      Settings: 'settings',
      ComponentList: 'component-list',
      CardDetail: 'card-detail',
      QuantitySelectorDemo: 'quantity-selector-demo',
      DialogDemo: 'dialog-demo',
      PopupDemo: 'popup-demo',
      NotFound: '*',
      Instant: 'instant',
      Explore: 'explore',
      // Component example screens
      CarouselExample: 'carousel-example',
      RatingExample: 'rating-example',
      BadgeExample: 'badge-example',
      ButtonExample: 'button-example',
      SwitchExample: 'switch-example',
      ProgressExample: 'progress-example',
      StepperExample: 'stepper-example',
      CheckboxExample: 'checkbox-example',
      LoadingExample: 'loading-example',
      NotificationExample: 'notification-example',
      ListExample: 'list-example',
    },
  },
};

export default function Navigation() {
  const colorScheme = useColorScheme();

  return (
    <NavigationContainer
      linking={linking}
      fallback={<Text>Loading...</Text>} // Optional: A fallback while linking resolves
      theme={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack.Navigator initialRouteName="Main" screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Signup" component={SignupScreen} />
        <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        <Stack.Screen name="Main" component={TabNavigator} />
        <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
        <Stack.Screen name="Products" component={ProductsScreen} />
        <Stack.Screen name="Checkout" component={CheckoutScreen} />
        <Stack.Screen name="OrderSuccess" component={OrderSuccessScreen} />
        <Stack.Screen name="Orders" component={OrdersScreen} />
        <Stack.Screen name="Settings" component={SettingsScreen} />
        <Stack.Screen name="ComponentList" component={ComponentListScreen} />
        <Stack.Screen name="CardDetail" component={CardDetailScreen} />
        <Stack.Screen name="QuantitySelectorDemo" component={QuantitySelectorDemoScreen} />
        <Stack.Screen name="DialogDemo" component={DialogDemoScreen} />
        <Stack.Screen name="PopupDemo" component={PopupDemoScreen} />
        <Stack.Screen name="ToastExample" component={ToastExample} />
        <Stack.Screen name="TagDemo" component={TagDemoScreen} />
        <Stack.Screen name="NotFound" component={NotFoundScreen} options={{ title: 'Oops!' }} />
        <Stack.Screen name="Instant" component={InstantScreen} />
        <Stack.Screen name="Explore" component={ExploreScreen} />
        
        {/* Component example screens */}
        <Stack.Screen name="CarouselExample" component={CarouselExample} />
        <Stack.Screen name="RatingExample" component={RatingExample} />
        <Stack.Screen name="BadgeExample" component={BadgeExample} />
        <Stack.Screen name="ButtonExample" component={ButtonExample} />
        <Stack.Screen name="SwitchExample" component={SwitchExample} />
        <Stack.Screen name="ProgressExample" component={ProgressExample} />
        <Stack.Screen name="StepperExample" component={StepperExample} />
        <Stack.Screen name="CheckboxExample" component={CheckboxExample} />
        <Stack.Screen name="LoadingExample" component={LoadingExample} />
        <Stack.Screen name="NotificationExample" component={NotificationExample} />
        <Stack.Screen name="ListExample" component={ListExample} />
        <Stack.Screen name="ProductCardExample" component={ProductCardExample} />
        <Stack.Screen name="AddToCartButtonExample" component={AddToCartButtonExample} />
        <Stack.Screen name="NavigationBarExample" component={NavigationBarExample} />
        <Stack.Screen name="MenuBarExample" component={MenuBarExample} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
