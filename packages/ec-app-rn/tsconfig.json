{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "baseUrl": ".", // Keep this to ensure paths are resolved from project root
    "paths": {      // Optional: for aliased paths like @/components/*
      "@/*": ["src/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "App.tsx",
    "index.js",
    "custom.d.ts" // If you have a custom declarations file
  ],
  "exclude": [
    "node_modules",
    "babel.config.js",
    "metro.config.js",
    "jest.config.js"
  ]
}
