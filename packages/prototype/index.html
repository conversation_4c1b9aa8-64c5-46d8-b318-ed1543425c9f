<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商App原型展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/global.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .description {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 40px;
            color: #666;
        }
        
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }
        
        .screen-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .screen-label {
            margin-top: 10px;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>电商App原型展示</h1>
    <p class="description">
        这是一个集货架电商、兴趣电商和即时电商于一体的高保真原型。每个屏幕都按照iOS设计规范设计，并使用HTML、CSS和FontAwesome图标实现。
    </p>
    
    <div class="prototype-container">
        <!-- Welcome Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/welcome.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">欢迎页</div>
        </div>
        
        <!-- Login Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/login.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">登录页</div>
        </div>
        
        <!-- Home Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/home.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">首页</div>
        </div>
        
        <!-- Category Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/category.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">分类页</div>
        </div>
        
        <!-- Interest Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/interest.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">兴趣推荐</div>
        </div>
        
        <!-- Instant Shopping Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/instant.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">即时购物</div>
        </div>
        
        <!-- Product List Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/product_list.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">商品列表</div>
        </div>
        
        <!-- Product Detail Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/product_detail.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">商品详情</div>
        </div>
        
        <!-- Shopping Cart Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/cart.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">购物车</div>
        </div>
        
        <!-- Checkout Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/checkout.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">结算页</div>
        </div>
        
        <!-- Order Confirmation Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/order_confirmation.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">订单确认</div>
        </div>
        
        <!-- Profile Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/profile.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">个人中心</div>
        </div>
        
        <!-- Settings Screen -->
        <div class="screen-container">
            <div class="iphone-frame">
                <iframe src="screens/settings.html" frameborder="0" width="100%" height="100%"></iframe>
            </div>
            <div class="screen-label">设置</div>
        </div>
    </div>
</body>
</html>
