/* Interest Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 83px; /* Tab bar height */
}

.interest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #fff;
}

.interest-header h1 {
    font-size: 22px;
    font-weight: 600;
    color: #000;
}

.header-actions i {
    font-size: 20px;
    color: #333;
}

.interest-tags {
    display: flex;
    overflow-x: auto;
    padding: 12px 16px;
    background-color: #fff;
    margin-bottom: 12px;
    scrollbar-width: none; /* Firefox */
}

.interest-tags::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.tag {
    padding: 6px 16px;
    border-radius: 20px;
    background-color: #f0f0f0;
    font-size: 14px;
    color: #666;
    margin-right: 10px;
    white-space: nowrap;
}

.tag.active {
    background-color: #007AFF;
    color: white;
}

.personalized-banner {
    margin: 0 16px 16px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.personalized-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 16px;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    color: white;
}

.banner-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

.banner-content p {
    font-size: 14px;
    opacity: 0.9;
}

.interest-section {
    background-color: #fff;
    border-radius: 12px;
    margin: 0 16px 16px;
    padding: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.match-tag {
    font-size: 12px;
    color: #ff3b30;
    font-weight: 500;
}

.interest-products {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.interest-product-card {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
}

.product-image {
    width: 120px;
    height: 120px;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.like-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 30px;
    height: 30px;
    border-radius: 15px;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.like-button i {
    font-size: 16px;
    color: #999;
}

.like-button.active i {
    color: #ff3b30;
}

.product-info {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-info h4 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.product-desc {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.product-price {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.current-price {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
    margin-right: 8px;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.product-tags {
    display: flex;
    gap: 6px;
}

.product-tags .tag {
    padding: 2px 6px;
    font-size: 10px;
    margin-right: 0;
    background-color: #fff0f0;
    color: #ff3b30;
    border: 1px solid #ffcfcf;
}

.recently-viewed {
    background-color: #fff;
    border-radius: 12px;
    margin: 0 16px 16px;
    padding: 16px;
}

.recently-viewed h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.viewed-products {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
}

.viewed-products::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.viewed-product {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.viewed-product img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
