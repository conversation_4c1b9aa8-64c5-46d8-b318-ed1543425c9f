/* Category Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #fff;
    padding-bottom: 83px; /* Tab bar height */
}

.search-bar {
    margin: 12px 16px;
}

.category-container {
    display: flex;
    height: calc(100% - 60px); /* Subtract search bar height */
}

.category-sidebar {
    width: 90px;
    background-color: #f5f5f5;
    overflow-y: auto;
    height: 100%;
}

.sidebar-item {
    padding: 16px 0;
    text-align: center;
    font-size: 14px;
    color: #666;
    position: relative;
}

.sidebar-item.active {
    color: #007AFF;
    font-weight: 500;
    background-color: #fff;
}

.sidebar-item.active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background-color: #007AFF;
}

.category-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
}

.category-banner {
    width: 100%;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 16px;
}

.category-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-section {
    margin-bottom: 20px;
}

.subcategory-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.brand-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.brand-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.brand-logo {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    overflow: hidden;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    border: 1px solid #eee;
}

.brand-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.brand-item span {
    font-size: 12px;
    color: #666;
}

.subcategory-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.subcategory-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.subcategory-image {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
}

.subcategory-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-item span {
    font-size: 12px;
    color: #333;
    text-align: center;
}
