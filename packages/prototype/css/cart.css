/* Cart Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 120px; /* Bottom checkout bar + tab bar height */
}

.cart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.cart-header h1 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.header-actions {
    font-size: 14px;
    color: #007AFF;
}

.store-section {
    background-color: #fff;
    margin-bottom: 12px;
}

.store-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.store-checkbox, .item-checkbox, .all-checkbox {
    position: relative;
    width: 22px;
    height: 22px;
    margin-right: 12px;
}

.store-checkbox input, .item-checkbox input, .all-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.store-checkbox label, .item-checkbox label, .all-checkbox label {
    position: absolute;
    top: 0;
    left: 0;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    border: 1px solid #ddd;
    background-color: #fff;
}

.store-checkbox input:checked + label, 
.item-checkbox input:checked + label, 
.all-checkbox input:checked + label {
    background-color: #007AFF;
    border-color: #007AFF;
}

.store-checkbox input:checked + label::after, 
.item-checkbox input:checked + label::after, 
.all-checkbox input:checked + label::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
}

.store-info {
    flex: 1;
    display: flex;
    align-items: center;
}

.store-info i.fa-store {
    font-size: 16px;
    color: #333;
    margin-right: 8px;
}

.store-name {
    flex: 1;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.store-info i.fa-chevron-right {
    font-size: 12px;
    color: #999;
}

.cart-items {
    padding: 0 16px;
}

.cart-item {
    display: flex;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-options {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
}

.item-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-price {
    font-size: 16px;
    font-weight: 600;
    color: #ff3b30;
}

.quantity-selector {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    border: 1px solid #ddd;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #333;
}

.quantity-input {
    width: 36px;
    height: 24px;
    border: none;
    text-align: center;
    font-size: 14px;
    color: #333;
}

.store-promotion {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff7f7;
}

.promotion-tag {
    font-size: 12px;
    color: #fff;
    background-color: #ff3b30;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 8px;
}

.promotion-text {
    font-size: 12px;
    color: #ff3b30;
}

.recommendations {
    background-color: #fff;
    padding: 16px;
    margin-top: 12px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.recommendation-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none;
    padding-bottom: 8px;
}

.recommendation-list::-webkit-scrollbar {
    display: none;
}

.recommendation-item {
    flex: 0 0 auto;
    width: 100px;
}

.recommendation-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-name {
    font-size: 12px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-price {
    font-size: 14px;
    font-weight: 500;
    color: #ff3b30;
}

.checkout-bar {
    position: fixed;
    bottom: 49px; /* Tab bar height */
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.select-all {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.select-all span {
    font-size: 14px;
    color: #333;
}

.checkout-info {
    flex: 1;
}

.total-price {
    font-size: 14px;
    color: #333;
}

.total-price span {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
}

.discount-info {
    font-size: 12px;
    color: #ff3b30;
}

.checkout-btn {
    background-color: #ff3b30;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 24px;
    font-size: 14px;
    font-weight: 500;
}

/* Tab Bar Styles */
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    height: 49px;
    border-top: 1px solid #eee;
    z-index: 100;
}

.tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab-item i {
    font-size: 20px;
    color: #999;
    margin-bottom: 2px;
}

.tab-item span {
    font-size: 10px;
    color: #999;
}

.tab-item.active i,
.tab-item.active span {
    color: #007AFF;
}
