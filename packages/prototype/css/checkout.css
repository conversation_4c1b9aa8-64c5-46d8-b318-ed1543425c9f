/* Checkout Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 70px; /* Bottom payment bar height */
}

.checkout-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.back-button {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 18px;
}

.checkout-header h1 {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-right: 30px; /* To balance with back button */
}

.address-section, .delivery-section {
    background-color: #fff;
    margin-bottom: 12px;
}

.section-content {
    display: flex;
    align-items: center;
    padding: 16px;
}

.address-icon, .delivery-icon {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background-color: #f0f7ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.address-icon i, .delivery-icon i {
    font-size: 14px;
    color: #007AFF;
}

.address-details, .delivery-details {
    flex: 1;
}

.address-contact {
    display: flex;
    margin-bottom: 4px;
}

.contact-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-right: 12px;
}

.contact-phone {
    font-size: 14px;
    color: #666;
}

.address-text {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.delivery-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.delivery-time {
    font-size: 14px;
    font-weight: 500;
    color: #007AFF;
}

.section-arrow i {
    font-size: 14px;
    color: #999;
}

.store-section {
    background-color: #fff;
    margin-bottom: 12px;
    padding: 16px;
}

.store-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.store-header i {
    font-size: 16px;
    color: #333;
    margin-right: 8px;
}

.store-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.order-items {
    margin-bottom: 16px;
}

.order-item {
    display: flex;
    margin-bottom: 16px;
}

.order-item:last-child {
    margin-bottom: 0;
}

.item-image {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-options {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
}

.item-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.item-quantity {
    font-size: 14px;
    color: #999;
}

.fee-item, .discount-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
}

.fee-label, .discount-label {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.discount-tag {
    font-size: 12px;
    color: #fff;
    background-color: #ff3b30;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 8px;
}

.fee-value {
    font-size: 14px;
    color: #333;
}

.discount-value {
    font-size: 14px;
    color: #ff3b30;
}

.payment-section {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
}

.payment-methods {
    display: flex;
    flex-direction: column;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.payment-method:last-child {
    border-bottom: none;
}

.payment-icon {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.payment-icon i {
    font-size: 18px;
}

.payment-icon .fa-alipay {
    color: #00a0e9;
}

.payment-icon .fa-weixin {
    color: #07c160;
}

.payment-icon .fa-credit-card {
    color: #ff9500;
}

.payment-name {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.payment-check {
    color: #007AFF;
    font-size: 18px;
}

.payment-method.active {
    background-color: #f0f7ff;
}

.invoice-section {
    background-color: #fff;
    margin-bottom: 12px;
}

.invoice-label {
    font-size: 14px;
    color: #333;
}

.invoice-value {
    flex: 1;
    font-size: 14px;
    color: #666;
    text-align: right;
    margin-right: 12px;
}

.order-summary {
    background-color: #fff;
    padding: 16px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-label {
    font-size: 14px;
    color: #666;
}

.summary-value {
    font-size: 14px;
    color: #333;
}

.payment-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    height: 70px;
    padding: 0 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.total-section {
    flex: 1;
    display: flex;
    align-items: baseline;
}

.total-label {
    font-size: 14px;
    color: #333;
}

.total-price {
    font-size: 20px;
    font-weight: 600;
    color: #ff3b30;
    margin-left: 8px;
}

.pay-btn {
    background-color: #ff3b30;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 24px;
    font-size: 16px;
    font-weight: 500;
}
