/* Settings Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    background-color: #fff;
    padding: 0 16px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.back-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.back-button i {
    font-size: 18px;
    color: #007AFF;
}

.title {
    font-size: 17px;
    font-weight: 600;
    color: #333;
}

.placeholder {
    width: 24px;
}

.settings-section {
    margin-bottom: 12px;
}

.section-title {
    padding: 12px 16px;
    font-size: 14px;
    color: #666;
    background-color: #f5f5f5;
}

.settings-list {
    background-color: #fff;
}

.settings-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
    border-bottom: none;
}

.item-icon {
    width: 36px;
    height: 36px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.item-icon i {
    font-size: 18px;
    color: #fff;
}

.item-icon.blue {
    background-color: #007AFF;
}

.item-icon.orange {
    background-color: #FF9500;
}

.item-icon.green {
    background-color: #34C759;
}

.item-icon.red {
    background-color: #FF3B30;
}

.item-icon.purple {
    background-color: #AF52DE;
}

.item-icon.teal {
    background-color: #5AC8FA;
}

.item-icon.grey {
    background-color: #8E8E93;
}

.item-content {
    flex: 1;
}

.item-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 2px;
}

.item-subtitle {
    font-size: 14px;
    color: #999;
}

.item-arrow {
    color: #C7C7CC;
    font-size: 14px;
}

.toggle-switch {
    width: 51px;
    height: 31px;
    position: relative;
}

.switch-track {
    width: 51px;
    height: 31px;
    background-color: #E9E9EA;
    border-radius: 16px;
    position: relative;
    transition: background-color 0.2s;
}

.switch-thumb {
    width: 27px;
    height: 27px;
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transition: transform 0.2s;
}

/* For demonstration, you can add a class to show the toggle as active */
.switch-track.active {
    background-color: #34C759;
}

.switch-track.active .switch-thumb {
    transform: translateX(20px);
}

.logout-button {
    margin: 20px 16px;
    height: 50px;
    background-color: #fff;
    color: #FF3B30;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}
