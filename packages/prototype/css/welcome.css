/* Welcome Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.welcome-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    height: calc(100% - 44px); /* Subtract status bar height */
}

.welcome-logo {
    width: 80px;
    height: 80px;
    background-color: #007AFF;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: 0 10px 20px rgba(0, 122, 255, 0.2);
}

.welcome-logo i {
    font-size: 40px;
    color: white;
}

.welcome-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #000;
}

.welcome-subtitle {
    font-size: 18px;
    color: #666;
    margin-bottom: 40px;
}

.welcome-features {
    width: 100%;
    margin-bottom: 40px;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.feature-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(0, 122, 255, 0.1);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.feature-icon i {
    font-size: 24px;
    color: #007AFF;
}

.feature-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #000;
}

.feature-text p {
    font-size: 14px;
    color: #666;
}

.welcome-buttons {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.button {
    margin-bottom: 12px;
}
