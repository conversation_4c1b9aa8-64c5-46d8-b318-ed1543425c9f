/* Global CSS for E-commerce App */

/* Base reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  background-color: #f5f5f5;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/* iPhone frame styling */
.iphone-frame {
  width: 390px;
  height: 844px;
  background-color: #fff;
  border-radius: 44px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin: 20px;
  border: 12px solid #000;
  display: inline-block;
}

/* iOS Status Bar */
.status-bar {
  height: 44px;
  width: 100%;
  background-color: #000;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  font-size: 14px;
  position: relative;
  z-index: 1000;
}

.status-bar-time {
  font-weight: 600;
}

.status-bar-icons {
  display: flex;
  align-items: center;
}

.status-bar-icons i {
  margin-left: 6px;
}

/* Bottom Navigation Bar */
.tab-bar {
  height: 83px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 20px; /* For iPhone home indicator */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 10px;
  padding: 8px 0;
  width: 20%;
}

.tab-item.active {
  color: #007AFF;
}

.tab-item i {
  font-size: 22px;
  margin-bottom: 4px;
}

/* App content area */
.app-content {
  height: calc(100% - 127px); /* Status bar + Tab bar */
  overflow-y: auto;
  position: relative;
  background-color: #f5f5f5;
}

/* Common UI Components */
.header {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-bar {
  background-color: rgba(142, 142, 147, 0.12);
  border-radius: 10px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  margin: 8px 16px;
}

.search-bar i {
  color: #8e8e93;
  margin-right: 8px;
}

.search-input {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 16px;
  color: #000;
}

.search-input::placeholder {
  color: #8e8e93;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  margin: 8px 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 16px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 16px 16px;
}

.product-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #000;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.product-original-price {
  font-size: 12px;
  color: #8e8e93;
  text-decoration: line-through;
  margin-left: 4px;
}

.button {
  background-color: #007AFF;
  color: white;
  border-radius: 12px;
  padding: 14px 20px;
  font-weight: 600;
  text-align: center;
  border: none;
  width: 100%;
  font-size: 16px;
}

.button-outline {
  background-color: transparent;
  color: #007AFF;
  border: 1px solid #007AFF;
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.p-4 {
  padding: 16px;
}

.m-4 {
  margin: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.text-sm {
  font-size: 14px;
}

.text-lg {
  font-size: 18px;
}

.font-bold {
  font-weight: 700;
}

.text-gray {
  color: #8e8e93;
}

.rounded-full {
  border-radius: 9999px;
}

/* Index page specific styles */
.prototype-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 20px;
  background-color: #f0f0f0;
}

.prototype-title {
  width: 100%;
  text-align: center;
  margin: 20px 0;
  font-size: 24px;
  font-weight: bold;
}

.prototype-description {
  width: 100%;
  text-align: center;
  margin-bottom: 40px;
  color: #666;
}

.screen-label {
  text-align: center;
  margin-top: 10px;
  font-weight: bold;
  color: #333;
}
