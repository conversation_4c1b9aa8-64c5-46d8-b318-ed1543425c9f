/* Instant Shopping Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 83px; /* Tab bar height */
}

.instant-header {
    background-color: #fff;
    padding: 12px 16px;
}

.location {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 8px;
}

.location i {
    color: #007AFF;
    margin-right: 6px;
}

.location span {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.location .fa-chevron-down {
    font-size: 10px;
    margin-left: 4px;
    color: #999;
}

.delivery-time {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #ff3b30;
}

.delivery-time i {
    margin-right: 6px;
}

.search-bar {
    margin: 12px 16px;
}

.delivery-banner {
    margin: 0 16px 16px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.delivery-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.banner-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    color: white;
}

.banner-content h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.banner-content p {
    font-size: 16px;
    margin-bottom: 16px;
}

.timer {
    display: inline-flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.timer i {
    margin-right: 6px;
    color: #ffcc00;
}

.instant-categories {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 16px;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background-color: #f0f7ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.category-icon i {
    font-size: 24px;
    color: #007AFF;
}

.category-item span {
    font-size: 12px;
    color: #333;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 16px 16px 12px;
    color: #333;
}

.store-list {
    padding: 0 16px;
}

.store-card {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.store-image {
    width: 100px;
    height: 100px;
    position: relative;
}

.store-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.distance {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
}

.store-info {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.store-info h3 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
}

.store-rating {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.stars {
    display: flex;
    margin-right: 6px;
}

.stars i {
    color: #ffcc00;
    font-size: 12px;
}

.store-rating span {
    font-size: 12px;
    color: #666;
}

.delivery-info {
    display: flex;
    font-size: 12px;
    color: #666;
}

.time {
    margin-right: 12px;
}

.time i, .fee i {
    margin-right: 4px;
}

.popular-products {
    padding: 0 16px 16px;
    overflow-x: auto;
}

.product-scroll {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: none; /* Firefox */
}

.product-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.instant-product {
    flex: 0 0 auto;
    width: 150px;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    padding-bottom: 12px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-image {
    width: 100%;
    height: 120px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instant-product h4 {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 8px 12px 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-store {
    font-size: 12px;
    color: #666;
    margin: 0 12px 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-price {
    font-size: 16px;
    font-weight: 600;
    color: #ff3b30;
    margin: 0 12px;
}

.add-to-cart {
    position: absolute;
    bottom: 12px;
    right: 12px;
    width: 28px;
    height: 28px;
    border-radius: 14px;
    background-color: #007AFF;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    font-size: 14px;
}
