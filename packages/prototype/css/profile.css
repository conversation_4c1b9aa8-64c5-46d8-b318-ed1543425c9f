/* Profile Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 60px; /* Tab bar height */
}

.profile-header {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-actions i {
    font-size: 20px;
    color: #333;
    margin-left: 16px;
}

.user-info-section {
    background-color: #fff;
    padding: 16px;
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 70px;
    height: 70px;
    border-radius: 35px;
    overflow: hidden;
    margin-right: 16px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.user-id {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
}

.user-level {
    display: flex;
    align-items: center;
}

.level-tag {
    font-size: 12px;
    color: #ff9500;
    background-color: #fff5e5;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
}

.level-progress {
    flex: 1;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #ff9500;
    border-radius: 3px;
}

.edit-profile {
    color: #999;
    font-size: 16px;
}

.user-stats {
    display: flex;
    background-color: #fff;
    padding: 16px 0;
    margin-bottom: 12px;
}

.stat-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: "";
    position: absolute;
    right: 0;
    top: 10%;
    height: 80%;
    width: 1px;
    background-color: #f0f0f0;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #999;
}

.section-card {
    background-color: #fff;
    margin-bottom: 12px;
    padding: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.section-action {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.section-action i {
    margin-left: 4px;
    font-size: 12px;
}

.order-status {
    display: flex;
    justify-content: space-between;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.status-icon {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 20px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.status-icon i {
    font-size: 20px;
    color: #007AFF;
}

.status-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: #ff3b30;
    color: white;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-label {
    font-size: 12px;
    color: #666;
}

.wallet-items {
    display: flex;
    justify-content: space-between;
}

.wallet-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.wallet-value {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
    margin-bottom: 4px;
}

.wallet-label {
    font-size: 12px;
    color: #666;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.service-icon {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.service-icon i {
    font-size: 20px;
    color: #007AFF;
}

.service-label {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.recommendations {
    background-color: #fff;
    padding: 16px;
}

.recommendation-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.recommendation-item {
    display: flex;
    flex-direction: column;
}

.recommendation-image {
    width: 100%;
    height: 150px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-price {
    font-size: 16px;
    font-weight: 600;
    color: #ff3b30;
}

/* Tab Bar Styles */
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    height: 49px;
    border-top: 1px solid #eee;
    z-index: 100;
}

.tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab-item i {
    font-size: 20px;
    color: #999;
    margin-bottom: 2px;
}

.tab-item span {
    font-size: 10px;
    color: #999;
}

.tab-item.active i,
.tab-item.active span {
    color: #007AFF;
}
