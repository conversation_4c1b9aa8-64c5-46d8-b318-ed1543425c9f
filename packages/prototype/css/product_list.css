/* Product List Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
}

.product-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.back-button {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 18px;
}

.product-list-header h1 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
    text-align: center;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-actions i {
    font-size: 18px;
    color: #333;
    margin-left: 16px;
}

.filter-bar {
    display: flex;
    background-color: #fff;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 54px;
    z-index: 10;
}

.filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    position: relative;
}

.filter-item i {
    margin-left: 4px;
    font-size: 12px;
}

.filter-item.active {
    color: #007AFF;
    font-weight: 500;
}

.filter-item.active::after {
    content: "";
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: #007AFF;
}

.product-list {
    padding: 12px;
}

.product-item {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-image {
    width: 120px;
    height: 120px;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-tag {
    position: absolute;
    top: 8px;
    left: 0;
    background-color: #ff3b30;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.product-info {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-name {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-desc {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-price-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.product-price {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
    margin-right: 8px;
}

.product-original {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.product-rating {
    display: flex;
    align-items: center;
}

.product-rating i {
    color: #ffcc00;
    margin-right: 4px;
    font-size: 12px;
}

.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    color: #999;
    font-size: 14px;
}

.loading-more i {
    margin-right: 8px;
}
