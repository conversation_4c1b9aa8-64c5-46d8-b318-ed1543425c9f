/* Product Detail Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 60px; /* Bottom action bar height */
}

.product-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.back-button {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 18px;
}

.header-tabs {
    flex: 1;
    display: flex;
    justify-content: center;
}

.tab {
    padding: 0 12px;
    font-size: 14px;
    color: #666;
    position: relative;
}

.tab.active {
    color: #333;
    font-weight: 600;
}

.tab.active::after {
    content: "";
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-actions i {
    font-size: 18px;
    color: #333;
    margin-left: 16px;
}

.product-gallery {
    background-color: #fff;
}

.gallery-main {
    width: 100%;
    height: 300px;
    position: relative;
}

.gallery-main img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.gallery-indicators {
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.5);
}

.indicator.active {
    width: 16px;
    background-color: #fff;
}

.gallery-thumbs {
    display: flex;
    padding: 12px;
    gap: 8px;
    overflow-x: auto;
    scrollbar-width: none;
}

.gallery-thumbs::-webkit-scrollbar {
    display: none;
}

.thumb {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #eee;
}

.thumb.active {
    border: 2px solid #007AFF;
}

.thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info-card {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 8px;
}

.product-price-section {
    display: flex;
    align-items: baseline;
    margin-bottom: 12px;
}

.current-price {
    font-size: 24px;
    font-weight: 600;
    color: #ff3b30;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-left: 8px;
}

.discount-tag {
    font-size: 12px;
    color: #ff3b30;
    background-color: #fff0f0;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

.product-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    margin-bottom: 12px;
}

.product-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.tag {
    font-size: 12px;
    color: #ff3b30;
    background-color: #fff0f0;
    padding: 2px 8px;
    border-radius: 4px;
}

.product-promotion {
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
}

.promotion-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.promotion-tag {
    font-size: 12px;
    color: #fff;
    background-color: #ff3b30;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 8px;
}

.promotion-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.promotion-text {
    font-size: 14px;
    color: #333;
}

.promotion-content i {
    color: #999;
    font-size: 12px;
}

.product-selection-card {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 8px;
}

.selection-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.selection-item:last-child {
    border-bottom: none;
}

.selection-label {
    width: 60px;
    font-size: 14px;
    color: #666;
}

.selection-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selection-value {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.selection-value i {
    color: #007AFF;
    margin-right: 6px;
}

.selection-content i {
    color: #999;
    font-size: 12px;
}

.color-options {
    display: flex;
    gap: 8px;
    margin-right: 8px;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #ddd;
}

.color-option.active {
    border: 2px solid #007AFF;
}

.quantity-selector {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #333;
}

.quantity-input {
    width: 40px;
    height: 28px;
    border: 1px solid #ddd;
    text-align: center;
    margin: 0 4px;
    font-size: 14px;
}

.product-details-card,
.product-reviews-card,
.recommended-products-card {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 8px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.product-specs {
    display: flex;
    flex-wrap: wrap;
}

.spec-item {
    width: 50%;
    display: flex;
    margin-bottom: 12px;
}

.spec-label {
    width: 70px;
    font-size: 14px;
    color: #666;
}

.spec-value {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.reviews-header {
    margin-bottom: 16px;
}

.reviews-summary {
    display: flex;
    align-items: center;
}

.rating {
    font-size: 24px;
    font-weight: 600;
    color: #ff3b30;
    margin-right: 8px;
}

.rating-stars {
    display: flex;
    margin-right: 8px;
}

.rating-stars i {
    color: #ffcc00;
    font-size: 14px;
    margin-right: 2px;
}

.review-count {
    font-size: 14px;
    color: #666;
}

.review-tags {
    display: flex;
    overflow-x: auto;
    gap: 8px;
    padding-bottom: 12px;
    margin-bottom: 16px;
    scrollbar-width: none;
}

.review-tags::-webkit-scrollbar {
    display: none;
}

.review-tag {
    flex-shrink: 0;
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    padding: 6px 12px;
    border-radius: 16px;
}

.review-tag.active {
    color: #007AFF;
    background-color: #e6f2ff;
}

.review-item {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
}

.reviewer-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.reviewer-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 8px;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-name {
    font-size: 14px;
    color: #333;
    margin-right: 8px;
}

.review-rating i {
    color: #ffcc00;
    font-size: 12px;
}

.review-content {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 12px;
}

.review-images {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.review-image {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
}

.review-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.review-time {
    font-size: 12px;
    color: #999;
}

.more-reviews {
    text-align: center;
    padding: 12px 0;
    font-size: 14px;
    color: #007AFF;
}

.recommended-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none;
    padding-bottom: 8px;
}

.recommended-list::-webkit-scrollbar {
    display: none;
}

.recommended-item {
    flex: 0 0 auto;
    width: 100px;
}

.recommended-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.recommended-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommended-name {
    font-size: 12px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommended-price {
    font-size: 14px;
    font-weight: 500;
    color: #ff3b30;
}

.bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    height: 60px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.action-buttons {
    display: flex;
    width: 40%;
    border-right: 1px solid #f0f0f0;
}

.action-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.action-button i {
    font-size: 18px;
    color: #666;
    margin-bottom: 4px;
}

.action-button span {
    font-size: 10px;
    color: #666;
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 8px;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    background-color: #ff3b30;
    color: white;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.purchase-buttons {
    display: flex;
    width: 60%;
}

.add-to-cart-btn, .buy-now-btn {
    flex: 1;
    border: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-to-cart-btn {
    background-color: #ffcc00;
    color: #333;
}

.buy-now-btn {
    background-color: #ff3b30;
    color: white;
}
