/* Home Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 83px; /* Tab bar height */
}

.home-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
}

.location {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.location i {
    color: #007AFF;
    margin-right: 4px;
}

.location .fa-chevron-down {
    font-size: 10px;
    margin-left: 4px;
    color: #999;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-actions i {
    font-size: 20px;
    color: #333;
    margin-left: 16px;
}

.banner-container {
    padding: 12px 16px;
}

.banner {
    width: 100%;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-indicators {
    position: absolute;
    bottom: 12px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 6px;
}

.indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
}

.indicator.active {
    background-color: #fff;
    width: 16px;
    border-radius: 3px;
}

.quick-categories {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 12px;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background-color: #f0f7ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.category-icon i {
    font-size: 24px;
    color: #007AFF;
}

.category-item span {
    font-size: 12px;
    color: #333;
}

.section {
    background-color: #fff;
    margin-bottom: 12px;
    padding: 16px 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    display: flex;
    align-items: center;
}

.section-icon {
    font-size: 16px;
    margin-right: 6px;
    color: #007AFF;
}

.section-more {
    font-size: 14px;
    color: #999;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.section-more i {
    font-size: 12px;
    margin-left: 4px;
}

/* Instant Shopping Section */
.instant-delivery-card {
    margin: 0 16px 16px;
    background: linear-gradient(135deg, #007AFF, #00C6FF);
    border-radius: 12px;
    padding: 16px;
    color: #fff;
    display: flex;
    overflow: hidden;
    position: relative;
}

.instant-delivery-info {
    flex: 1;
    z-index: 1;
}

.instant-delivery-info h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

.instant-delivery-info p {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 16px;
}

.instant-delivery-button {
    background-color: rgba(255, 255, 255, 0.2);
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.instant-delivery-button i {
    margin-left: 4px;
    font-size: 12px;
}

.instant-delivery-image {
    width: 100px;
    height: 100px;
    position: absolute;
    right: -20px;
    bottom: -20px;
    opacity: 0.8;
}

.instant-delivery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.instant-products {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
    gap: 12px;
    scrollbar-width: none; /* Firefox */
}

.instant-products::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.instant-product {
    flex: 0 0 auto;
    width: 100px;
}

.instant-product-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.instant-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instant-product-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.instant-product-price {
    font-size: 14px;
    font-weight: 600;
    color: #ff3b30;
}

/* Interest-based Shopping Section */
.interest-products {
    padding: 0 16px;
}

.interest-product {
    display: flex;
    margin-bottom: 16px;
}

.interest-product-image {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
    position: relative;
}

.interest-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.interest-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #ff3b30;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
}

.interest-product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.interest-product-name {
    font-size: 16px;
    font-weight: 500;
    color: #000;
    margin-bottom: 4px;
}

.interest-product-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.interest-product-price {
    display: flex;
    align-items: center;
}

.interest-product-price .price {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
    margin-right: 8px;
}

.interest-product-price .original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 16px;
}

.product-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-image {
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
}

.product-info {
    padding: 8px;
}

.product-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-price {
    font-size: 16px;
    font-weight: 600;
    color: #ff3b30;
}

.product-original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    margin-left: 4px;
}
