/* Login Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.login-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: #fff;
    height: calc(100% - 44px); /* Subtract status bar height */
    position: relative;
}

.back-button {
    position: absolute;
    top: 20px;
    left: 0;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007AFF;
    font-size: 18px;
}

.login-header {
    margin-top: 60px;
    margin-bottom: 40px;
}

.login-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #000;
}

.login-subtitle {
    font-size: 16px;
    color: #666;
}

.login-form {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
}

.input-icon-wrapper {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    padding: 12px 0;
}

.input-icon-wrapper i {
    color: #999;
    font-size: 18px;
    margin-right: 12px;
}

.form-input {
    border: none;
    background: transparent;
    flex: 1;
    font-size: 16px;
    color: #000;
    padding: 0;
}

.form-input:focus {
    outline: none;
}

.toggle-password {
    color: #999;
    cursor: pointer;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: #007AFF;
    text-decoration: none;
    font-size: 14px;
}

.login-button {
    margin-bottom: 20px;
}

.divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    color: #999;
}

.divider::before,
.divider::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e0e0e0;
}

.divider span {
    padding: 0 10px;
    font-size: 14px;
}

.social-login {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.social-button {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    font-size: 24px;
    color: white;
    cursor: pointer;
}

.wechat {
    background-color: #07C160;
}

.alipay {
    background-color: #1677FF;
}

.apple {
    background-color: #000;
}

.register-prompt {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.register-prompt a {
    color: #007AFF;
    text-decoration: none;
    font-weight: 500;
}
