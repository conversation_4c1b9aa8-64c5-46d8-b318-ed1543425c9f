/* Order Confirmation Screen Styles */

.iphone-frame-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding-bottom: 20px;
}

.success-section {
    background-color: #fff;
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
}

.success-icon {
    font-size: 60px;
    color: #4cd964;
    margin-bottom: 16px;
}

.success-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.success-message {
    font-size: 14px;
    color: #666;
}

.order-info-section {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.order-number, .order-time, .payment-method, .payment-amount {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 12px;
    color: #999;
    margin-bottom: 4px;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.price {
    color: #ff3b30;
    font-weight: 600;
}

.delivery-section, .order-details-section {
    background-color: #fff;
    margin-bottom: 12px;
}

.section-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.delivery-info {
    padding: 16px;
}

.info-row {
    display: flex;
    margin-bottom: 12px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row .info-label {
    width: 80px;
    margin-bottom: 0;
}

.info-row .info-value {
    flex: 1;
    font-weight: normal;
}

.store-block {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.store-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.store-header i {
    font-size: 16px;
    color: #333;
    margin-right: 8px;
}

.store-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.order-items {
    margin-bottom: 16px;
}

.order-item {
    display: flex;
    margin-bottom: 16px;
}

.order-item:last-child {
    margin-bottom: 0;
}

.item-image {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.item-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-options {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
}

.item-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.item-quantity {
    font-size: 14px;
    color: #999;
}

.store-summary {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.summary-label {
    font-size: 14px;
    color: #666;
}

.summary-value {
    font-size: 14px;
    color: #333;
}

.discount {
    color: #ff3b30;
}

.summary-row.total {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.summary-row.total .summary-label,
.summary-row.total .summary-value {
    font-weight: 600;
}

.order-total {
    padding: 16px;
    background-color: #fff7f7;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.total-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.total-value {
    font-size: 20px;
    font-weight: 600;
    color: #ff3b30;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    margin-bottom: 12px;
}

.primary-btn, .secondary-btn {
    flex: 1;
    padding: 12px 0;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
}

.primary-btn {
    background-color: #007AFF;
    color: white;
    border: none;
    margin-left: 12px;
}

.secondary-btn {
    background-color: #fff;
    color: #007AFF;
    border: 1px solid #007AFF;
}

.recommendations {
    background-color: #fff;
    padding: 16px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.recommendation-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scrollbar-width: none;
    padding-bottom: 8px;
}

.recommendation-list::-webkit-scrollbar {
    display: none;
}

.recommendation-item {
    flex: 0 0 auto;
    width: 100px;
}

.recommendation-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
}

.recommendation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-name {
    font-size: 12px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recommendation-price {
    font-size: 14px;
    font-weight: 500;
    color: #ff3b30;
}
