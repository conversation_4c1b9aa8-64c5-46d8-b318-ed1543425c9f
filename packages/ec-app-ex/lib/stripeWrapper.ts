import { Platform } from 'react-native';

// 平台特定的 Stripe 功能导入
let stripeModule: any;

if (Platform.OS === 'web') {
  console.log('🌐 Loading Stripe web implementation');
  stripeModule = require('./stripe.web');
} else {
  console.log(`📱 Loading Stripe native implementation for ${Platform.OS}`);
  stripeModule = require('./stripe');
}

// 重新导出所有 Stripe 功能
export const initializeStripe = stripeModule.initializeStripe;
export const createPaymentIntent = stripeModule.createPaymentIntent;
export const createOrder = stripeModule.createOrder;
