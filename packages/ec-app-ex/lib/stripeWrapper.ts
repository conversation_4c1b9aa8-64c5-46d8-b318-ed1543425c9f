import { Platform } from 'react-native';

// 平台特定的 Stripe 功能导入
// 使用函数来延迟加载，避免在模块加载时就执行 require

const getStripeModule = () => {
  if (Platform.OS === 'web') {
    console.log('🌐 Loading Stripe web implementation');
    return require('./stripe.web');
  } else {
    console.log(`📱 Loading Stripe native implementation for ${Platform.OS}`);
    return require('./stripe');
  }
};

// 重新导出所有 Stripe 功能，使用延迟加载
export const initializeStripe = (...args: any[]) => {
  const stripeModule = getStripeModule();
  return stripeModule.initializeStripe(...args);
};

export const createPaymentIntent = (...args: any[]) => {
  const stripeModule = getStripeModule();
  return stripeModule.createPaymentIntent(...args);
};

export const createOrder = (...args: any[]) => {
  const stripeModule = getStripeModule();
  return stripeModule.createOrder(...args);
};
