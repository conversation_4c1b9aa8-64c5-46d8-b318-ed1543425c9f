// Web 平台的 Stripe 实现 - 使用空的实现

console.log('🌐 Loading stripe.web.ts - Web platform Stripe implementation');

// Web 平台的 Stripe 初始化（空实现）
export const initializeStripe = async () => {
  console.log('🌐 Web platform: Stripe initialization skipped');
  return Promise.resolve();
};

// Web 平台的支付意图创建（模拟实现）
export const createPaymentIntent = async (amount: number, currency: string = 'cny') => {
  console.log(`🌐 Web platform: Mock payment intent for ${amount} ${currency}`);
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 生成模拟的客户端密钥
  const timestamp = Date.now().toString();
  const randomPart = Math.random().toString(36).substring(2, 10);
  const mockClientSecret = `pi_${randomPart}${timestamp.substring(timestamp.length - 6)}_secret_web_${randomPart}`;
  
  return { clientSecret: mockClientSecret };
};

// Web 平台的订单创建
export const createOrder = async (orderData: any) => {
  console.log('🌐 Web platform: Creating order', orderData);
  
  // 导入 Supabase 函数
  const { createOrderInDb, clearUserCart } = await import('./supabase');
  
  try {
    const { order, items, error } = await createOrderInDb(orderData);
    
    if (error) {
      console.error('Error creating order in database:', error);
      throw error;
    }
    
    // 清除用户购物车
    if (orderData.userId) {
      const { error: cartError } = await clearUserCart(orderData.userId);
      if (cartError) {
        console.warn('Failed to clear user cart:', cartError);
      }
    }
    
    console.log('🌐 Web platform: Order created successfully', order?.id);
    return { order, items, error: null };
  } catch (error) {
    console.error('🌐 Web platform: Error creating order:', error);
    throw error;
  }
};
