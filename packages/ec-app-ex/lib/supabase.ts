import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Supabase configuration
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || '';
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || '';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth helper functions
export const getSession = async () => {
  const { data, error } = await supabase.auth.getSession();
  return { session: data.session, error };
};

export const getUser = async () => {
  //const { data, error } = await supabase.auth.getUser();
  const { customer: data, error} = await getCustomerByUserId();
  
  // 如果是未登录错误，不返回错误信息
  if (error?.message === 'No user is currently logged in') {
    console.log('用户未登录，静默处理');
    return { user: null, error: null };
  }
  
  return { user: data, error };
};

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { user: data.user, session: data.session, error };
};

export const signUpWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  return { user: data.user, session: data.session, error };
};

export const resetPassword = async (email: string) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: 'ec-app-ex://reset-password',
  });
  return { error };
};

export const updatePassword = async (password: string) => {
  const { error } = await supabase.auth.updateUser({
    password,
  });
  return { error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

// E-commerce specific functions

// Products
export const getProducts = async ({ 
  categoryId = null,
  limit = 10, 
  offset = 0, 
  sortBy = 'created_at', 
  sortOrder = 'desc' 
} = {}) => {
  let query = supabase
    .from('products')
    .select('*, categories(*)')
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(offset, offset + limit - 1);
  
  if (categoryId) {
    query = query.eq('category_id', categoryId);
  }
  
  const { data, error, count } = await query;
  return { products: data, error, count };
};

export const getProductById = async (productId: string) => {
  const { data, error } = await supabase
    .from('products')
    .select('*, categories(*)')
    .eq('id', productId)
    .single();
  
  return { product: data, error };
};

export const getProductsByCategory = async (categorySlug: string, limit = 10, offset = 0) => {
  // First get the category ID from the slug
  const { data: category } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', categorySlug)
    .single();
  
  if (!category) return { products: [], error: new Error('Category not found') };
  
  // Then get products with that category ID
  return getProducts({ categoryId: category.id, limit, offset });
};

// Categories
export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name');
  
  return { categories: data, error };
};

// User Profile
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  return { profile: data, error };
};

// Orders
export const getUserOrders = async (userId: string, status?: string) => {
  console.log('getUserOrders called with userId:', userId, 'status:', status);
  
  try {
    // 首先检查当前登录用户
    //const { data: { user: currentUser } } = await supabase.auth.getUser();
    const { customer: currentUser, error: userError } = await getCustomerByUserId();
    
    // 如果是未登录错误，静默处理
    if (userError?.message === 'No user is currently logged in') {
      console.log('用户未登录，静默处理');
      return { orders: [], error: null };
    }
    
    console.log('当前登录用户:', currentUser?.id);
    
    // 如果没有传入userId，使用当前登录用户的ID
    const effectiveUserId = userId || currentUser?.id;
    if (!effectiveUserId) {
      console.error('没有有效的用户ID');
      return { orders: [], error: new Error('没有有效的用户ID') };
    }
    
    // 检查订单表结构
    console.log('检查订单表结构...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('orders')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('获取订单表结构失败:', tableError);
    } else {
      console.log('订单表第一条记录:', tableInfo && tableInfo.length > 0 ? tableInfo[0] : '无数据');
      if (tableInfo && tableInfo.length > 0) {
        console.log('订单表字段:', Object.keys(tableInfo[0]));
      }
    }
    
    // 检查订单项表结构
    console.log('检查订单项表结构...');
    const { data: itemsInfo, error: itemsError } = await supabase
      .from('order_items')
      .select('*')
      .limit(1);
    
    if (itemsError) {
      console.error('获取订单项表结构失败:', itemsError);
    } else {
      console.log('订单项表第一条记录:', itemsInfo && itemsInfo.length > 0 ? itemsInfo[0] : '无数据');
      if (itemsInfo && itemsInfo.length > 0) {
        console.log('订单项表字段:', Object.keys(itemsInfo[0]));
      }
    }
    
    // 使用 customer_id 查询
    console.log(`使用 customer_id=${effectiveUserId} 查询订单`);
    let { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('customer_id', effectiveUserId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('查询订单出错:', error);
      return { orders: [], error };
    }
    
    console.log('查询到订单数量:', data?.length || 0);
    
    if (!data || data.length === 0) {
      console.log('没有找到订单数据');
      
      // 检查订单表中是否有任何数据
      const { data: anyOrders, error: anyOrdersError } = await supabase
        .from('orders')
        .select('id, customer_id, status')
        .limit(5);
      
      if (anyOrdersError) {
        console.error('查询示例订单失败:', anyOrdersError);
      } else {
        console.log('订单表示例数据:', anyOrders);
      }
      
      return { orders: [], error: null };
    }
    
    // 现在我们需要为每个订单获取订单项
    console.log('获取订单项数据...');
    const ordersWithItems = await Promise.all(
      data.map(async (order: any) => {
        // 查询每个订单的订单项
        const { data: orderItems, error: itemsError } = await supabase
          .from('order_items')
          .select('*')
          .eq('order_id', order.id);
        
        if (itemsError) {
          console.error(`获取订单 ${order.id} 的订单项失败:`, itemsError);
          return { ...order, order_items: [] };
        }
        
        console.log(`订单 ${order.id} 有 ${orderItems?.length || 0} 个订单项`);
        
        // 为每个订单项获取产品信息
        const itemsWithProducts = await Promise.all(
          (orderItems || []).map(async (item: any) => {
            if (!item.product_id) {
              console.log(`订单项 ${item.id} 没有 product_id`);
              return {
                ...item,
                products: { name: '未知商品', image_url: 'https://via.placeholder.com/80' }
              };
            }
            
            // 查询产品信息
            const { data: product, error: productError } = await supabase
              .from('products')
              .select('*')
              .eq('id', item.product_id)
              .single();
            
            if (productError || !product) {
              console.error(`获取产品 ${item.product_id} 信息失败:`, productError);
              return {
                ...item,
                products: { name: '未知商品', image_url: 'https://via.placeholder.com/80' }
              };
            }
            
            return {
              ...item,
              products: product
            };
          })
        );
        
        return {
          ...order,
          order_items: itemsWithProducts
        };
      })
    );
    
    // 如果指定了状态过滤，手动过滤结果
    let filteredOrders = ordersWithItems;
    if (status && status !== 'ALL') {
      console.log('按状态过滤订单:', status);
      filteredOrders = ordersWithItems.filter((order: any) => {
        // 状态可能是大写、小写或混合大小写，进行不区分大小写的比较
        const orderStatus = String(order.status || '').toLowerCase();
        const filterStatus = String(status).toLowerCase();
        return orderStatus === filterStatus;
      });
      console.log('过滤后的订单数量:', filteredOrders.length);
    }
    
    if (!filteredOrders || filteredOrders.length === 0) {
      console.log('没有找到符合条件的订单');
      return { orders: [], error: null };
    }
    
    // 处理订单数据，确保结构正确
    const processedOrders = filteredOrders.map((order: any) => {
      // 处理每个订单项，确保数据格式正确
      const processedItems = (order.order_items || []).map((item: any) => {
        const productInfo = item.products || { name: '未知商品', image_url: 'https://via.placeholder.com/80' };
        
        return {
          id: item.id || `temp-${Math.random().toString(36).substring(7)}`,
          quantity: item.quantity || 1,
          price: item.price || 0,
          products: {
            name: productInfo.name || '未知商品',
            image_url: productInfo.image_url || 'https://via.placeholder.com/80'
          }
        };
      });
      
      // 计算订单总金额（如果没有提供）
      const totalAmount = order.total_amount || 
        processedItems.reduce((sum: number, item: any) => 
          sum + (item.price || 0) * (item.quantity || 1), 0);
      
      // 返回处理后的订单
      return {
        id: order.id,
        created_at: order.created_at || new Date().toISOString(),
        total_amount: totalAmount,
        status: order.status || 'pending_payment',
        order_items: processedItems,
        customer_id: order.customer_id || effectiveUserId
      };
    });
    
    console.log('处理后的订单数量:', processedOrders.length);
    return { orders: processedOrders, error: null };
  } catch (e) {
    console.error('获取订单时发生错误:', e);
    return { orders: [], error: e as Error };
  }
};

// Cart
export const getCart = async (userId: string) => {
  const { data, error } = await supabase
    .from('cart_items')
    .select('*, products(*, categories(*))')
    .eq('user_id', userId);
  
  return { cartItems: data, error };
};

export const addToCart = async (userId: string, productId: string, quantity = 1) => {
  // Check if item already exists in cart
  const { data: existingItem } = await supabase
    .from('cart_items')
    .select('*')
    .eq('user_id', userId)
    .eq('product_id', productId)
    .single();
  
  if (existingItem) {
    // Update quantity
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity: existingItem.quantity + quantity })
      .eq('id', existingItem.id)
      .select();
    
    return { cartItem: data?.[0], error };
  } else {
    // Add new item
    const { data, error } = await supabase
      .from('cart_items')
      .insert({
        user_id: userId,
        product_id: productId,
        quantity
      })
      .select();
    
    return { cartItem: data?.[0], error };
  }
};

export const updateCartItemQuantity = async (cartItemId: string, quantity: number) => {
  const { data, error } = await supabase
    .from('cart_items')
    .update({ quantity })
    .eq('id', cartItemId)
    .select();
  
  return { cartItem: data?.[0], error };
};

export const removeFromCart = async (cartItemId: string) => {
  const { error } = await supabase
    .from('cart_items')
    .delete()
    .eq('id', cartItemId);
  
  return { error };
};

// Favorites/Wishlist
export const getFavorites = async (userId: string) => {
  const { data, error } = await supabase
    .from('favorites')
    .select('*, products(*)')
    .eq('customer_id', userId);
  
  return { favorites: data, error };
};

export const addToFavorites = async (userId: string, productId: string) => {
  const { data, error } = await supabase
    .from('favorites')
    .insert({
      user_id: userId,
      product_id: productId
    })
    .select();
  
  return { favorite: data?.[0], error };
};

export const removeFromFavorites = async (favoriteId: string) => {
  const { error } = await supabase
    .from('favorites')
    .delete()
    .eq('id', favoriteId);
  
  return { error };
};

// Reviews
export const getProductReviews = async (productId: string) => {
  const { data, error } = await supabase
    .from('reviews')
    .select('*, profiles(*)')
    .eq('product_id', productId)
    .order('created_at', { ascending: false });
  
  return { reviews: data, error };
};

export const addReview = async (userId: string, productId: string, rating: number, comment: string) => {
  const { data, error } = await supabase
    .from('reviews')
    .insert({
      user_id: userId,
      product_id: productId,
      rating,
      comment
    })
    .select();
  
  return { review: data?.[0], error };
};

// Get customer data by user ID
export const getCustomerByUserId = async (userId?: string) => {
  try {
    // If no userId is provided, get the current logged-in user
    if (!userId) {
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      userId = currentUser?.id;
      
      if (!userId) {
        return { customer: null, error: new Error('No user is currently logged in') };
      }
    }
    
    // Query the customers table using the user ID
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('auth_id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching customer data:', error);
      return { customer: null, error };
    }
    
    return { customer: data, error: null };
  } catch (e) {
    console.error('Exception when fetching customer data:', e);
    return { customer: null, error: e as Error };
  }
};

// Check if a product ID exists in the products table
export const checkProductExists = async (productId: string) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id')
      .eq('id', productId)
      .single();
    
    if (error) {
      console.error(`Product ID ${productId} not found:`, error);
      return false;
    }
    
    return !!data;
  } catch (e) {
    console.error(`Error checking product ID ${productId}:`, e);
    return false;
  }
};

// Create a new order in the database
export const createOrderInDb = async (orderData: any) => {
  try {
    console.log('Creating order in Supabase');
    console.log('订单数据结构:', JSON.stringify(orderData, null, 2));
    
    // Generate a unique order number
    const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // Create the order record
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        order_number: orderNumber,
        customer_id: orderData.userId,
        subtotal: orderData.orderSummary.productAmount,
        tax: 0, // Assuming tax is included or calculated separately
        shipping: orderData.orderSummary.shippingFee,
        total: orderData.orderSummary.totalAmount,
        status: 'confirmed',
        payment_status: 'paid',
        shipping_address: orderData.shippingAddress,
        billing_address: orderData.shippingAddress // Using shipping address as billing address if not provided separately
      })
      .select()
      .single();
    
    if (orderError) {
      console.error('Error creating order:', orderError);
      return { order: null, error: orderError };
    }
    
    console.log('Order created successfully:', order);
    
    // Insert order items
    if (orderData.items && orderData.items.length > 0) {
      try {
        console.log('订单项总数:', orderData.items.length);
        
        // 先检查所有产品ID是否有效
        console.log('检查所有产品ID是否有效...');
        const allProductIds = orderData.items.map((item: any) => {
          const possibleId = item.productId || item.product_id || (item.products && item.products.id) || item.id;
          console.log(`商品 ${item.name || '未知'} 的ID:`, possibleId, '类型:', typeof possibleId);
          return possibleId;
        }).filter(Boolean);
        
        console.log('所有可能的产品ID:', allProductIds);
        
        // 批量查询这些ID是否存在于products表中
        const { data: existingProducts } = await supabase
          .from('products')
          .select('id')
          .in('id', allProductIds);
        
        const validProductIds = existingProducts?.map(p => p.id) || [];
        console.log('数据库中存在的有效产品ID:', validProductIds);
        
        // 找出无效的产品ID
        const invalidProductIds = allProductIds.filter((id: string) => !validProductIds.includes(id));
        if (invalidProductIds.length > 0) {
          console.error('发现无效的产品ID:', invalidProductIds);
        }
        
        // 收集有效的订单项
        const productDetails = [];
        
        for (const item of orderData.items) {
          // 尝试找到产品ID
          let productId = null;
          
          // 尝试不同可能的产品ID字段
          if (item.productId) productId = item.productId;
          else if (item.product_id) productId = item.product_id;
          else if (item.products?.id) productId = item.products.id;
          else if (item.id && typeof item.id === 'string' && item.id.includes('-')) productId = item.id;
          
          console.log(`处理商品: ${item.name || '未知'}, 提取的ID: ${productId}`);
          
          if (!productId) {
            console.warn('未找到产品ID，跳过此商品:', item);
            continue;
          }
          
          // 检查此ID是否在有效ID列表中
          if (validProductIds.includes(productId)) {
            productDetails.push({
              product_id: productId,
              quantity: item.quantity || 1,
              price: item.price || 0,
              order_id: order.id
            });
            console.log(`已添加有效商品到订单: ${productId}`);
          } else {
            console.warn(`产品ID在数据库中不存在，跳过: ${productId}`);
            
            // 尝试直接查询此产品以获取更多信息
            const { data: productCheck } = await supabase
              .from('products')
              .select('id, name')
              .eq('id', productId)
              .single();
              
            if (productCheck) {
              console.log(`意外情况: 单独查询时产品存在: ${productId}, ${productCheck.name}`);
              productDetails.push({
                product_id: productId,
                quantity: item.quantity || 1,
                price: item.price || 0,
                order_id: order.id
              });
            } else {
              console.error(`确认产品不存在: ${productId}`);
            }
          }
        }
        
        if (productDetails.length === 0) {
          console.error('没有找到有效的产品，无法创建订单项');
          return { order, items: null, error: new Error('没有有效的产品') };
        }
        
        console.log('准备插入的订单项:', productDetails);
        
        // 插入订单项
        const { data: items, error: itemsError } = await supabase
          .from('order_items')
          .insert(productDetails)
          .select();
        
        if (itemsError) {
          console.error('创建订单项时出错:', itemsError);
          return { order, items: null, error: itemsError };
        }
        
        console.log('订单项创建成功:', items);
        return { order, items, error: null };
      } catch (itemError) {
        console.error('处理订单项时出错:', itemError);
        return { order, items: null, error: itemError as Error };
      }
    }
    
    return { order, items: null, error: null };
  } catch (e) {
    console.error('创建订单时发生异常:', e);
    return { order: null, items: null, error: e as Error };
  }
};

// Clear cart after successful order
export const clearUserCart = async (userId: string) => {
  try {
    console.log('Clearing cart for user:', userId);
    
    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId);
    
    return { error };
  } catch (e) {
    console.error('Exception when clearing cart:', e);
    return { error: e as Error };
  }
};

// 删除订单
export const deleteOrder = async (orderId: string) => {
  try {
    console.log('删除订单:', orderId);
    
    // 首先删除订单项
    const { error: orderItemsError } = await supabase
      .from('order_items')
      .delete()
      .eq('order_id', orderId);
    
    if (orderItemsError) {
      console.error('删除订单项时出错:', orderItemsError);
      return { success: false, error: orderItemsError };
    }
    
    // 然后删除订单本身
    const { error: orderError } = await supabase
      .from('orders')
      .delete()
      .eq('id', orderId);
    
    if (orderError) {
      console.error('删除订单时出错:', orderError);
      return { success: false, error: orderError };
    }
    
    return { success: true, error: null };
  } catch (error) {
    console.error('删除订单过程中发生异常:', error);
    return { success: false, error };
  }
};

// 搜索商品 - 增强版
export const searchProducts = async (
  searchTerm: string, 
  options: {
    limit?: number;
    offset?: number;
    categoryId?: string | null;
    minPrice?: number | null;
    maxPrice?: number | null;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    inStock?: boolean;
  } = {}
) => {
  try {
    const {
      limit = 10,
      offset = 0,
      categoryId = null,
      minPrice = null,
      maxPrice = null,
      sortBy = 'created_at',
      sortOrder = 'desc',
      inStock = false
    } = options;

    // 构建基本查询
    let query = supabase
      .from('products')
      .select('*, categories(*)', { count: 'exact' });

    // 如果有搜索词，添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      // 使用名称和描述进行搜索
      // 注意：根据数据库结构，只使用存在的列
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // 添加分类过滤
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    // 添加价格范围过滤
    if (minPrice !== null) {
      query = query.gte('price', minPrice);
    }
    if (maxPrice !== null) {
      query = query.lte('price', maxPrice);
    }

    // 添加库存过滤
    if (inStock) {
      query = query.gt('stock_quantity', 0);
    }

    // 添加排序
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // 添加分页
    query = query.range(offset, offset + limit - 1);

    // 执行查询
    const { data, error, count } = await query;

    if (error) {
      console.error('搜索商品时出错:', error);
      return { products: [], error, count: 0 };
    }

    return { products: data, error: null, count };
  } catch (error) {
    console.error('搜索商品过程中发生异常:', error);
    return { products: [], error, count: 0 };
  }
};

// 保存搜索历史
export const saveSearchHistory = async (userId: string, searchTerm: string) => {
  try {
    if (!searchTerm.trim() || !userId) return { error: null };

    // 检查是否已存在相同的搜索词
    const { data: existingSearch } = await supabase
      .from('search_history')
      .select('id')
      .eq('user_id', userId)
      .eq('search_term', searchTerm)
      .single();

    if (existingSearch) {
      // 如果存在，更新时间戳
      const { error } = await supabase
        .from('search_history')
        .update({ created_at: new Date() })
        .eq('id', existingSearch.id);
      
      return { error };
    } else {
      // 如果不存在，创建新记录
      const { error } = await supabase
        .from('search_history')
        .insert({ user_id: userId, search_term: searchTerm });
      
      return { error };
    }
  } catch (error) {
    console.error('保存搜索历史时出错:', error);
    return { error };
  }
};

// 获取搜索历史
export const getSearchHistory = async (userId: string, limit = 10) => {
  try {
    if (!userId) return { history: [], error: null };

    const { data, error } = await supabase
      .from('search_history')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    return { history: data || [], error };
  } catch (error) {
    console.error('获取搜索历史时出错:', error);
    return { history: [], error };
  }
};

// 清除搜索历史
export const clearSearchHistory = async (userId: string) => {
  try {
    if (!userId) return { error: null };

    const { error } = await supabase
      .from('search_history')
      .delete()
      .eq('user_id', userId);

    return { error };
  } catch (error) {
    console.error('清除搜索历史时出错:', error);
    return { error };
  }
};

// 获取热门搜索词
export const getPopularSearchTerms = async (limit = 10) => {
  try {
    const { data, error } = await supabase
      .rpc('get_popular_search_terms', { limit_num: limit });

    return { terms: data || [], error };
  } catch (error) {
    console.error('获取热门搜索词时出错:', error);
    return { terms: [], error };
  }
};

// 获取搜索建议
export const getSearchSuggestions = async (prefix: string, limit = 5) => {
  try {
    if (!prefix.trim()) return { suggestions: [], error: null };

    // 从产品名称中获取建议
    const { data, error } = await supabase
      .from('products')
      .select('name')
      .ilike('name', `${prefix}%`)
      .limit(limit);

    if (error) throw error;

    // 提取唯一的建议
    const suggestions = [...new Set(data?.map(item => item.name) || [])];

    return { suggestions, error: null };
  } catch (error) {
    console.error('获取搜索建议时出错:', error);
    return { suggestions: [], error };
  }
};
