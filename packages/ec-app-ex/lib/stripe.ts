import { initStripe } from '@stripe/stripe-react-native';

// Initialize Stripe with your publishable key
// Using a valid test publishable key format for development
export const initializeStripe = async () => {
  // This is a placeholder test key that follows <PERSON><PERSON>'s format
  // In a real app, you should use your actual Stripe publishable key
  // Ideally from environment variables
  const publishableKey = 'pk_test_51NXXXXXXXXXXXXXXXXXXXXXXmock';
  
  console.log('Initializing Stripe with publishable key');
  
  return await initStripe({
    publishableKey,
    merchantIdentifier: 'merchant.com.yourcompany.app', // Only needed for Apple Pay
    urlScheme: 'your-app-scheme', // Required for 3D Secure and bank redirects
  });
};

// Function to create a payment intent via your backend
export const createPaymentIntent = async (amount: number, currency: string = 'cny') => {
  try {
    // MOCK IMPLEMENTATION FOR TESTING
    // In a real app, you would call your backend API here
    console.log(`Creating mock payment intent for amount: ${amount} ${currency}`);
    
    // For testing purposes, we need to use a valid-looking client secret format
    // Real client secrets look like: pi_xxx_secret_xxx where xxx are alphanumeric strings
    // This is just for testing - in production you'd get a real one from your backend
    
    // Generate a timestamp-based ID with correct format
    const timestamp = Date.now().toString();
    const randomPart = Math.random().toString(36).substring(2, 10);
    
    // Format that looks like a real Stripe client secret
    // pi_1234567890_secret_abcdefghijklmn
    const mockClientSecret = `pi_${randomPart}${timestamp.substring(timestamp.length - 6)}_secret_live_${randomPart}`;
    
    console.log('Generated mock client secret:', mockClientSecret);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return { clientSecret: mockClientSecret };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
};

// Function to create an order in your backend after successful payment
import { createOrderInDb, clearUserCart } from './supabase';

export const createOrder = async (orderData: any) => {
  try {
    console.log('Creating order with data:', orderData);
    
    // Create the order in Supabase
    const { order, items, error } = await createOrderInDb(orderData);
    
    if (error) {
      console.error('Error creating order in database:', error);
      throw error;
    }
    
    // Clear the user's cart after successful order
    if (orderData.userId) {
      const { error: cartError } = await clearUserCart(orderData.userId);
      if (cartError) {
        console.warn('Failed to clear user cart:', cartError);
        // We don't throw here as the order was still created successfully
      }
    }
    
    // Return a successful response with the order details
    return {
      success: true,
      orderId: order.id,
      orderNumber: order.order_number,
      message: 'Order created successfully',
      orderDetails: { order, items }
    };
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};
