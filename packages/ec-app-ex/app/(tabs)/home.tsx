import React, { useState, useRef } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, FlatList, Dimensions, View as RNView, Text as RNText, NativeScrollEvent, NativeSyntheticEvent, TextInput, ActivityIndicator } from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { useColorScheme, Colors } from '@ec-nx/shared-logic';
import { router } from 'expo-router';
import { useSearchProducts } from '@/hooks/useSupabaseData';

const SCREEN_WIDTH = Dimensions.get('window').width;
const PADDING = 15;

// Mock data for carousel
const carouselData = [
  { id: '1', image: 'https://images.unsplash.com/photo-1570857502907-18048ee018d9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', title: 'SALE' }, 
  { id: '2', image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80', title: 'New Arrivals' },
  { id: '3', image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80', title: 'Special Offers' },
];

// Mock data for categories
const categoryData = [
  { id: '1', icon: 'bolt', title: '限时抢购' },
  { id: '2', icon: 'gift', title: '新品首发' },
  { id: '3', icon: 'percent', title: '折扣专区' },
  { id: '4', icon: 'fire', title: '热门榜单' },
  { id: '5', icon: 'th-large', title: '全部分类' }, 
];

// Mock data for Instant E-commerce products
const instantEcommerceData = [
  { id: '1', image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60', title: '健康沙拉', price: '¥29.9' },
  { id: '2', image: 'https://images.unsplash.com/photo-1579954115545-a95591f28bfc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60', title: '拿铁咖啡', price: '¥19.9' },
  { id: '3', image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60', title: '意式披萨', price: '¥59.9' },
];

// Mock data for "Guess You Like" products
const guessYouLikeData = [
  { id: '1', image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60', title: '无线蓝牙耳机', description: '主动降噪 | 40小时续航', price: '¥899', originalPrice: '¥1299', tag: '推荐', tagColor: '#FF6B6B' },
  { id: '2', image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=60', title: '轻便运动跑鞋', description: '透气网面 | 减震鞋底', price: '¥459', originalPrice: '¥699', tag: '热门', tagColor: '#FFA500' },
];

// Mock data for Featured Products
const featuredProductsData = [
  { id: '1', image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=60', title: '智能手表 Pro', price: '¥1299', originalPrice: '¥1599' },
  { id: '2', image: 'https://images.unsplash.com/photo-1580529944588-11b9160010e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=60', title: '10000mAh 快充宝', price: '¥129', originalPrice: '¥199' },
  { id: '3', image: 'https://images.unsplash.com/photo-1595950653106-6c9ebd614d34?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=60', title: '专业跑步鞋', price: '¥499', originalPrice: '¥799' },
  { id: '4', image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=60', title: '无线蓝牙音箱', price: '¥299', originalPrice: '¥399' },
];

interface CarouselItem { id: string; image: string; title: string; }
interface CategoryItem { id: string; icon: string; title: string; path?: string; }
interface ProductItem { id: string; image: string; title: string; price: string; originalPrice?: string; description?: string; tag?: string; tagColor?: string; }

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [currentSlide, setCurrentSlide] = useState(0);
  const flatListRef = useRef<FlatList<CarouselItem>>(null);
  const [searchInputText, setSearchInputText] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const { 
    products: searchResults, 
    loading: searchLoading, 
    error: searchError, 
    handleSearch, 
    clearSearch, 
    suggestions,
    suggestionsLoading,
    fetchSuggestions,
    searchHistory,
    clearSearchHistory,
    popularTerms,
    filters,
    updateFilters,
    totalCount
  } = useSearchProducts(20);
  
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const index = event.nativeEvent.contentOffset.x / slideSize;
    setCurrentSlide(Math.round(index));
  };

  const renderCarouselItem = ({ item }: { item: CarouselItem }) => (
    <RNView style={styles.carouselItemContainer}>
      <Image source={{ uri: item.image }} style={styles.carouselImage} contentFit="cover" />
    </RNView>
  );

  const renderCategoryItem = ({ item }: { item: CategoryItem }) => (
    <TouchableOpacity 
      style={styles.categoryItem} 
      onPress={() => {
        if (item.path) {
          router.push(item.path as any); // Type assertion for href
        } else {
          // TODO: Handle other category presses, e.g., navigate to a category screen
          console.log('Category pressed (no path):', item.title);
          if (item.id === '5') { // Example: All Categories
            // router.push('/categories'); // If such a screen exists
          }
        }
      }}
    >
      <RNView style={[styles.categoryIconContainer, { backgroundColor: colors.primaryTransparent }]}>
        <FontAwesome name={item.icon as any} size={22} color={colors.primary} />
      </RNView>
      <RNText style={[styles.categoryTitle, { color: colors.textMuted }]}>{item.title}</RNText>
    </TouchableOpacity>
  );

  const renderInstantEcommerceItem = ({ item }: { item: ProductItem }) => (
    <TouchableOpacity style={styles.instantProductItem}>
      <Image source={{ uri: item.image }} style={styles.instantProductImage} contentFit="cover" />
      <RNText style={[styles.instantProductTitle, { color: colors.text }]} numberOfLines={1}>{item.title}</RNText>
      <RNText style={[styles.instantProductPrice, { color: colors.primary }]}>{item.price}</RNText>
    </TouchableOpacity>
  );

  const renderGuessYouLikeItem = ({ item }: { item: ProductItem }) => (
    <TouchableOpacity style={[styles.guessYouLikeItem, { backgroundColor: colors.backgroundSoft }]}>
      {item.tag && (
        <RNView style={[styles.guessYouLikeTagBadge, { backgroundColor: item.tagColor || colors.primary }]}>
          <RNText style={styles.guessYouLikeTagText}>{item.tag}</RNText>
        </RNView>
      )}
      <Image source={{ uri: item.image }} style={styles.guessYouLikeImage} contentFit="cover" />
      <RNView style={styles.guessYouLikeContent}>
        <RNText style={[styles.guessYouLikeTitle, { color: colors.text }]} numberOfLines={1}>{item.title}</RNText>
        {item.description && <RNText style={[styles.guessYouLikeDescription, { color: colors.textMuted }]} numberOfLines={2}>{item.description}</RNText>}
        <RNView style={styles.priceContainerRow}>
          <RNText style={[styles.productPrice, { color: colors.error }]}>{item.price}</RNText>
          {item.originalPrice && <RNText style={[styles.productOriginalPrice, { color: colors.textMuted }]}>{item.originalPrice}</RNText>}
        </RNView>
      </RNView>
    </TouchableOpacity>
  );

  const renderSearchResultItem = ({ item }: { item: ProductItem }) => (
    <TouchableOpacity 
      style={[styles.searchResultItem, { backgroundColor: colors.backgroundSoft }]}
      onPress={() => router.push({pathname: '/product/[id]', params: {id: item.id}} as any)}
    >
      <Image source={{ uri: item.image }} style={styles.searchResultImage} contentFit="cover" />
      <RNView style={styles.searchResultContent}>
        <RNText style={[styles.searchResultTitle, { color: colors.text }]} numberOfLines={2}>{item.title}</RNText>
        <RNText style={[styles.searchResultPrice, { color: colors.primary }]}>{item.price}</RNText>
        {item.description && (
          <RNText style={[styles.searchResultDescription, { color: colors.textMuted }]} numberOfLines={2}>{item.description}</RNText>
        )}
      </RNView>
    </TouchableOpacity>
  );
  
  const renderFeaturedProductItem = ({ item }: { item: ProductItem }) => (
    <TouchableOpacity style={[styles.featuredProductItem, { backgroundColor: colors.backgroundSoft }]}>
      <Image source={{ uri: item.image }} style={styles.featuredProductImage} contentFit="cover" />
      <RNView style={styles.featuredProductInfo}>
        <RNText style={[styles.featuredProductTitle, { color: colors.text }]} numberOfLines={2}>{item.title}</RNText>
        <RNView style={styles.priceContainerRow}>
          <RNText style={[styles.productPrice, { color: colors.error }]}>{item.price}</RNText>
          {item.originalPrice && <RNText style={[styles.productOriginalPrice, { color: colors.textMuted }]}>{item.originalPrice}</RNText>}
        </RNView>
      </RNView>
    </TouchableOpacity>
  );

  const SectionHeader = ({ title, onMorePress }: { title: string; onMorePress?: () => void }) => (
    <RNView style={styles.sectionHeaderContainer}>
      <RNText style={[styles.sectionTitle, { color: colors.text }]}>{title}</RNText>
      <TouchableOpacity onPress={onMorePress}>
        <RNText style={[styles.sectionMore, { color: colors.textMuted }]}>更多 <FontAwesome name="angle-right" size={14} color={colors.textMuted} /></RNText>
      </TouchableOpacity>
    </RNView>
  );

  return (
    <RNView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <RNView style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity style={styles.locationContainer}>
          <FontAwesome name="map-marker" size={16} color={colors.text} />
          <RNText style={[styles.locationText, { color: colors.text }]}>上海市</RNText>
          <FontAwesome name="angle-down" size={16} color={colors.textMuted} />
        </TouchableOpacity>
        <RNView style={styles.headerIcons}>
          <TouchableOpacity style={styles.iconButton}><FontAwesome name="bell-o" size={22} color={colors.text} /></TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}><FontAwesome name="comment-o" size={22} color={colors.text} /></TouchableOpacity>
        </RNView>
      </RNView>

      <RNView style={[styles.searchBarOuterContainer, { backgroundColor: colors.background }]}>
        <RNView style={[styles.searchBar, { backgroundColor: colors.backgroundSoft }]}>
          <FontAwesome name="search" size={16} color={colors.textMuted} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="搜索商品"
            placeholderTextColor={colors.textMuted}
            value={searchInputText}
            onChangeText={(text) => {
              setSearchInputText(text);
              // 当输入长度大于1时获取搜索建议
              if (text.length > 1) {
                fetchSuggestions(text);
              }
            }}
            onSubmitEditing={() => {
              if (searchInputText.trim()) {
                handleSearch(searchInputText);
                setIsSearchActive(true);
              }
            }}
            returnKeyType="search"
          />
          {searchInputText ? (
            <TouchableOpacity 
              onPress={() => {
                setSearchInputText('');
                clearSearch();
                setIsSearchActive(false);
              }}
            >
              <FontAwesome name="times-circle" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity onPress={() => setShowFilters(!showFilters)}>
              <FontAwesome name="sliders" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          )}
        </RNView>
        
        {/* 搜索建议 */}
        {searchInputText.length > 1 && suggestions.length > 0 && !isSearchActive && (
          <RNView style={[styles.suggestionsContainer, { backgroundColor: colors.backgroundSoft }]}>
            {suggestions.map((suggestion, index) => (
              <TouchableOpacity 
                key={index} 
                style={styles.suggestionItem}
                onPress={() => {
                  setSearchInputText(suggestion);
                  handleSearch(suggestion);
                  setIsSearchActive(true);
                }}
              >
                <FontAwesome name="search" size={14} color={colors.textMuted} style={styles.suggestionIcon} />
                <RNText style={[styles.suggestionText, { color: colors.text }]}>{suggestion}</RNText>
              </TouchableOpacity>
            ))}
          </RNView>
        )}
        
        {/* 过滤器 */}
        {showFilters && (
          <RNView style={[styles.filtersContainer, { backgroundColor: colors.backgroundSoft }]}>
            <RNView style={styles.filterHeader}>
              <RNText style={[styles.filterTitle, { color: colors.text }]}>筛选</RNText>
              <TouchableOpacity onPress={() => setShowFilters(false)}>
                <FontAwesome name="times" size={18} color={colors.textMuted} />
              </TouchableOpacity>
            </RNView>
            
            {/* 价格范围 */}
            <RNView style={styles.filterSection}>
              <RNText style={[styles.filterSectionTitle, { color: colors.text }]}>价格范围</RNText>
              <RNView style={styles.priceRangeContainer}>
                <TextInput
                  style={[styles.priceInput, { color: colors.text, borderColor: colors.border }]}
                  placeholder="最低价"
                  placeholderTextColor={colors.textMuted}
                  keyboardType="numeric"
                  onChangeText={(text) => {
                    const price = text ? parseFloat(text) : null;
                    updateFilters({ minPrice: price });
                  }}
                />
                <RNText style={[styles.priceSeparator, { color: colors.text }]}>-</RNText>
                <TextInput
                  style={[styles.priceInput, { color: colors.text, borderColor: colors.border }]}
                  placeholder="最高价"
                  placeholderTextColor={colors.textMuted}
                  keyboardType="numeric"
                  onChangeText={(text) => {
                    const price = text ? parseFloat(text) : null;
                    updateFilters({ maxPrice: price });
                  }}
                />
              </RNView>
            </RNView>
            
            {/* 排序方式 */}
            <RNView style={styles.filterSection}>
              <RNText style={[styles.filterSectionTitle, { color: colors.text }]}>排序方式</RNText>
              <RNView style={styles.sortOptionsContainer}>
                <TouchableOpacity 
                  style={[
                    styles.sortOption, 
                    filters.sortBy === 'created_at' && filters.sortOrder === 'desc' && 
                    { backgroundColor: colors.primary, borderColor: colors.primary }
                  ]}
                  onPress={() => updateFilters({ sortBy: 'created_at', sortOrder: 'desc' })}
                >
                  <RNText 
                    style={[
                      styles.sortOptionText, 
                      { color: filters.sortBy === 'created_at' && filters.sortOrder === 'desc' ? '#fff' : colors.text }
                    ]}
                  >
                    最新
                  </RNText>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[
                    styles.sortOption, 
                    filters.sortBy === 'price' && filters.sortOrder === 'asc' && 
                    { backgroundColor: colors.primary, borderColor: colors.primary }
                  ]}
                  onPress={() => updateFilters({ sortBy: 'price', sortOrder: 'asc' })}
                >
                  <RNText 
                    style={[
                      styles.sortOptionText, 
                      { color: filters.sortBy === 'price' && filters.sortOrder === 'asc' ? '#fff' : colors.text }
                    ]}
                  >
                    价格低到高
                  </RNText>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[
                    styles.sortOption, 
                    filters.sortBy === 'price' && filters.sortOrder === 'desc' && 
                    { backgroundColor: colors.primary, borderColor: colors.primary }
                  ]}
                  onPress={() => updateFilters({ sortBy: 'price', sortOrder: 'desc' })}
                >
                  <RNText 
                    style={[
                      styles.sortOptionText, 
                      { color: filters.sortBy === 'price' && filters.sortOrder === 'desc' ? '#fff' : colors.text }
                    ]}
                  >
                    价格高到低
                  </RNText>
                </TouchableOpacity>
              </RNView>
            </RNView>
            
            {/* 库存选项 */}
            <RNView style={styles.filterSection}>
              <RNView style={styles.stockOptionContainer}>
                <TouchableOpacity 
                  style={styles.checkboxContainer}
                  onPress={() => updateFilters({ inStock: !filters.inStock })}
                >
                  <RNView 
                    style={[
                      styles.checkbox, 
                      { borderColor: colors.text },
                      filters.inStock && { backgroundColor: colors.primary, borderColor: colors.primary }
                    ]}
                  >
                    {filters.inStock && <FontAwesome name="check" size={12} color="#fff" />}
                  </RNView>
                  <RNText style={[styles.checkboxLabel, { color: colors.text }]}>只显示有库存</RNText>
                </TouchableOpacity>
              </RNView>
            </RNView>
          </RNView>
        )}
      </RNView>

      {isSearchActive ? (
        <RNView style={{ flex: 1 }}>
          {/* 搜索结果计数 */}
          {!searchLoading && !searchError && searchResults.length > 0 && (
            <RNText style={[styles.searchResultsCount, { color: colors.textMuted }]}>
              共找到 {totalCount} 个结果
            </RNText>
          )}
          
          {searchLoading ? (
            <RNView style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <RNText style={[styles.loadingText, { color: colors.text }]}>搜索中...</RNText>
            </RNView>
          ) : searchError ? (
            <RNView style={styles.errorContainer}>
              <FontAwesome name="exclamation-circle" size={40} color={colors.error} />
              <RNText style={[styles.errorText, { color: colors.error }]}>搜索出错</RNText>
              <TouchableOpacity 
                style={[styles.retryButton, { backgroundColor: colors.primary }]}
                onPress={() => handleSearch(searchInputText)}
              >
                <RNText style={styles.retryButtonText}>重试</RNText>
              </TouchableOpacity>
            </RNView>
          ) : searchResults.length === 0 ? (
            <RNView style={styles.emptyResultsContainer}>
              <FontAwesome name="search" size={40} color={colors.textMuted} />
              <RNText style={[styles.emptyResultsText, { color: colors.text }]}>没有找到相关商品</RNText>
              <TouchableOpacity 
                style={[styles.backButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  clearSearch();
                  setIsSearchActive(false);
                }}
              >
                <RNText style={styles.backButtonText}>返回首页</RNText>
              </TouchableOpacity>
            </RNView>
          ) : (
            <FlatList
              data={searchResults}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={[styles.searchResultItem, { backgroundColor: colors.backgroundSoft }]}
                  onPress={() => router.push({pathname: '/product-details/[productId]', params: {productId: item.id}} as any)}
                >
                  <Image source={{ uri: item.image }} style={styles.searchResultImage} contentFit="cover" />
                  <RNView style={styles.searchResultContent}>
                    <RNText style={[styles.searchResultTitle, { color: colors.text }]} numberOfLines={2}>{item.name}</RNText>
                    {item.description && (
                      <RNText style={[styles.searchResultDescription, { color: colors.textMuted }]} numberOfLines={2}>
                        {item.description}
                      </RNText>
                    )}
                    <RNView style={styles.priceContainerRow}>
                      <RNText style={[styles.productPrice, { color: colors.error }]}>{item.price}</RNText>
                      {item.original_price && (
                        <RNText style={[styles.productOriginalPrice, { color: colors.textMuted }]}>{item.original_price}</RNText>
                      )}
                    </RNView>
                  </RNView>
                </TouchableOpacity>
              )}
              keyExtractor={item => item.id}
              contentContainerStyle={{ padding: PADDING }}
              showsVerticalScrollIndicator={false}
              ItemSeparatorComponent={() => <RNView style={{ height: 10 }} />}
            />
          )}
        </RNView>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 20 }}>
          {/* 搜索历史记录 */}
          {searchHistory && searchHistory.length > 0 && (
            <RNView style={[styles.historyContainer, { backgroundColor: colors.backgroundSoft }]}>
              <RNView style={styles.historyHeader}>
                <RNText style={[styles.historyTitle, { color: colors.text }]}>最近搜索</RNText>
                <TouchableOpacity style={styles.clearHistoryButton} onPress={clearSearchHistory}>
                  <RNText style={{ color: colors.primary }}>清除</RNText>
                </TouchableOpacity>
              </RNView>
              <RNView style={styles.historyItemsContainer}>
                {searchHistory.map((item, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={[styles.historyItem, { backgroundColor: colors.backgroundSoft, borderColor: colors.border }]}
                    onPress={() => {
                      setSearchInputText(item.term);
                      handleSearch(item.term);
                      setIsSearchActive(true);
                    }}
                  >
                    <RNText style={[styles.historyItemText, { color: colors.text }]}>{item.term}</RNText>
                    <FontAwesome name="clock-o" size={12} color={colors.textMuted} />
                  </TouchableOpacity>
                ))}
              </RNView>
            </RNView>
          )}
          
          {/* 热门搜索词 */}
          {popularTerms && popularTerms.length > 0 && (
            <RNView style={[styles.historyContainer, { backgroundColor: colors.backgroundSoft }]}>
              <RNView style={styles.historyHeader}>
                <RNText style={[styles.historyTitle, { color: colors.text }]}>热门搜索</RNText>
              </RNView>
              <RNView style={styles.historyItemsContainer}>
                {popularTerms.map((term, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={[styles.historyItem, { backgroundColor: colors.backgroundSoft, borderColor: colors.border }]}
                    onPress={() => {
                      setSearchInputText(term);
                      handleSearch(term);
                      setIsSearchActive(true);
                    }}
                  >
                    <RNText style={[styles.historyItemText, { color: colors.text }]}>{term}</RNText>
                    <FontAwesome name="fire" size={12} color={colors.error} />
                  </TouchableOpacity>
                ))}
              </RNView>
            </RNView>
          )}
          
          <RNView style={styles.carouselContainer}>
            <FlatList
              ref={flatListRef}
              data={carouselData}
              renderItem={renderCarouselItem}
              keyExtractor={item => item.id}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onScroll={onScroll}
              style={styles.carousel}
              snapToInterval={SCREEN_WIDTH - (PADDING * 2)}
              decelerationRate="fast"
              contentContainerStyle={{ paddingHorizontal: PADDING }}
            />
            <RNView style={styles.paginationContainer}>
              {carouselData.map((_, index) => (
                <RNView
                  key={index}
                  style={[styles.paginationDot, currentSlide === index ? styles.paginationDotActive : {}, { backgroundColor: currentSlide === index ? colors.primary : colors.textMutedOpac } ]}
                />
              ))}
            </RNView>
          </RNView>

          <RNView style={[styles.categorySection, { backgroundColor: colors.background }]}>
            <FlatList
              data={categoryData}
              renderItem={renderCategoryItem}
              keyExtractor={item => item.id}
              numColumns={5}
              scrollEnabled={false}
              contentContainerStyle={styles.categoryList}
            />
          </RNView>

          <RNView style={[styles.section, { backgroundColor: colors.background }]}>
            <SectionHeader title="⚡ 即时电商" onMorePress={() => router.push('/instant')} />
            <RNView style={[styles.instantBanner, { backgroundColor: colors.primary }]}>
              <RNText style={styles.instantBannerTitle}>30分钟送达</RNText>
              <RNText style={styles.instantBannerSubtitle}>附近商品立即配送</RNText>
            </RNView>
            <FlatList
              data={instantEcommerceData}
              renderItem={renderInstantEcommerceItem}
              keyExtractor={item => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingLeft: PADDING }}
            />
          </RNView>

          <RNView style={[styles.section, { backgroundColor: colors.background }]}>
            <SectionHeader title="❤️ 猜你喜欢" />
            {guessYouLikeData.map(item => (
              <React.Fragment key={item.id}>
                {renderGuessYouLikeItem({ item })}
              </React.Fragment>
            ))}
          </RNView>

          <RNView style={[styles.section, { backgroundColor: colors.background }]}>
            <SectionHeader title="🛍️ 精选商品" />
            <FlatList
              data={featuredProductsData}
              renderItem={renderFeaturedProductItem}
              keyExtractor={item => item.id}
              numColumns={2}
              scrollEnabled={false}
              columnWrapperStyle={{ justifyContent: 'space-between' }}
              contentContainerStyle={{ paddingHorizontal: PADDING }}
            />
          </RNView>
        </ScrollView>
      )}
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: PADDING,
    paddingTop: 50,
    paddingBottom: 10,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 6,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 18,
  },
  searchBarOuterContainer: {
    paddingHorizontal: PADDING,
    paddingBottom: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    borderRadius: 20,
    paddingHorizontal: 15,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    height: 40,
    padding: 0,
  },
  // searchText style is replaced by searchInput
  searchResultsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  emptyResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyResultsText: {
    marginTop: 10,
    fontSize: 16,
    marginBottom: 20,
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  carouselContainer: {
    marginBottom: 5, 
  },
  carouselItemContainer: {
    width: SCREEN_WIDTH - (PADDING * 2),
    height: 160, 
    borderRadius: 10,
    overflow: 'hidden',
    marginRight: PADDING, 
  },
  carouselImage: {
    width: '100%',
    height: '100%',
  },
  carousel: {},
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    width: 10, 
    height: 10,
    borderRadius: 5,
  },
  categorySection: {
    paddingVertical: 15,
    paddingHorizontal: PADDING / 2, 
  },
  categoryList: {},
  categoryItem: {
    flex: 1, 
    alignItems: 'center',
    paddingVertical: 5, 
  },
  categoryIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    paddingVertical: 15,
    // paddingHorizontal: PADDING, 
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: PADDING,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  // 搜索建议样式
  suggestionsContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 10,
    borderRadius: 10,
    paddingVertical: 5,
    marginHorizontal: PADDING,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  suggestionIcon: {
    marginRight: 10,
  },
  suggestionText: {
    fontSize: 14,
  },
  // 搜索历史样式
  historyContainer: {
    marginTop: 10,
    marginBottom: 15,
    paddingHorizontal: PADDING,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearHistoryButton: {
    padding: 5,
  },
  historyItemsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  historyItemText: {
    fontSize: 13,
    marginRight: 5,
  },
  // 过滤器样式
  filtersContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 10,
    borderRadius: 10,
    padding: 15,
    marginHorizontal: PADDING,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  filterSection: {
    marginBottom: 15,
  },
  filterSectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  priceSeparator: {
    marginHorizontal: 10,
  },
  sortOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  sortOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  sortOptionText: {
    fontSize: 13,
  },
  stockOptionContainer: {
    marginTop: 5,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  checkboxLabel: {
    fontSize: 14,
  },
  // 搜索结果计数
  searchResultsCount: {
    paddingHorizontal: PADDING,
    paddingVertical: 10,
    fontSize: 14,
  },
  sectionMore: {
    fontSize: 13,
  },
  instantBanner: {
    paddingVertical: 12,
    paddingHorizontal: PADDING,
    borderRadius: 8,
    marginHorizontal: PADDING,
    marginBottom: 15,
    alignItems: 'flex-start',
  },
  instantBannerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  instantBannerSubtitle: {
    color: '#fff',
    fontSize: 12,
  },
  instantProductItem: {
    width: 100, 
    marginRight: 12,
    alignItems: 'center',
  },
  instantProductImage: {
    width: 90,
    height: 90,
    borderRadius: 8,
    marginBottom: 8,
  },
  instantProductTitle: {
    fontSize: 13,
    textAlign: 'center',
    marginBottom: 3,
  },
  instantProductPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  guessYouLikeItem: {
    flexDirection: 'row',
    marginHorizontal: PADDING,
    marginBottom: PADDING,
    padding: PADDING,
    borderRadius: 8,
    position: 'relative', 
  },
  guessYouLikeTagBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderTopLeftRadius: 8,
    borderBottomRightRadius: 8,
    zIndex: 1,
  },
  guessYouLikeTagText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  guessYouLikeImage: {
    width: 100,
    height: 100,
    borderRadius: 6,
    marginRight: PADDING,
  },
  guessYouLikeContent: {
    flex: 1,
    justifyContent: 'space-between', 
  },
  guessYouLikeTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  guessYouLikeDescription: {
    fontSize: 12,
    marginBottom: 6,
    lineHeight: 16,
  },
  priceContainerRow: {
    flexDirection: 'row',
    alignItems: 'baseline', 
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  productOriginalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  featuredProductItem: {
    width: (SCREEN_WIDTH - PADDING * 3) / 2, 
    marginBottom: PADDING,
    borderRadius: 8,
    overflow: 'hidden',
  },
  featuredProductImage: {
    width: '100%',
    height: (SCREEN_WIDTH - PADDING * 3) / 2 * 0.9, 
  },
  featuredProductInfo: {
    padding: 10,
  },
  featuredProductTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
  },
  searchResultItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    marginBottom: 8,
    borderRadius: 8,
  },
  searchResultImage: {
    width: 80,
    height: 80,
    borderRadius: 6,
    marginRight: 12,
  },
  searchResultContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  searchResultTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  searchResultPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  searchResultDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  searchResultsList: {
    flex: 1,
  },
  centeredMessageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 40,
  },
  centeredMessageText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
