import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  View as RNView,
  Text as RNText,
  FlatList,
} from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { useRouter } from 'expo-router';
import { useColorScheme } from '@ec-nx/shared-logic/hooks/useColorScheme';
import { supabase, getCart, removeFromCart, updateCartItemQuantity } from '@/lib/supabase';
import { useFocusEffect } from '@react-navigation/native';
import { User } from '@supabase/supabase-js';
// import { Link } from 'expo-router'; // Not used in this version

const SCREEN_WIDTH = Dimensions.get('window').width;
const PADDING = 15;

// Types
interface ProductItem {
  id: string;
  productId: string;
  name: string;
  image: string;
  specs: string;
  price: number;
  quantity: number;
  selected: boolean;
  category: { id: string; name: string; } | null;
}

interface Store {
  id: string;
  name: string;
  icon?: string; // Optional: 'shop' or similar FontAwesome icon name
  items: ProductItem[];
  selected: boolean; // For store-level select checkbox
  promotion?: string; // e.g., "已满1000元, 已减100元"
}

const recommendedProductsData = [
  {
    id: 'rec1',
    name: '头戴式无线耳机',
    image: 'https://images.unsplash.com/photo-1546435770-a82c918d8a01?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80', // Placeholder
    price: 899,
  },
  {
    id: 'rec2',
    name: '全面屏智能手机',
    image: 'https://images.unsplash.com/photo-1598327105666-6d76a2a11f0c?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80', // Placeholder
    price: 4999,
  },
  {
    id: 'rec3',
    name: '20000mAh充电宝',
    image: 'https://images.unsplash.com/photo-1589670301389-fg9e90e7099a?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80', // Placeholder
    price: 199,
  },
];

export default function CartScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [stores, setStores] = useState<Store[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

    const fetchCartData = async (currentUser: User | null) => {
    if (!currentUser) {
      setStores([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const { cartItems, error } = await getCart(currentUser.id);
      if (error) throw error;

      if (cartItems) {
                const storesData = cartItems.reduce((acc: Store[], item) => {
          const storeId = item.products.category_id || 'default';
          let store = acc.find(s => s.id === storeId);
          if (!store) {
            store = {
              id: storeId,
              name: item.products.categories?.name || 'Default Store',
              items: [],
              selected: true,
            };
            acc.push(store);
          }
          store.items.push({
            id: item.id,
            name: item.products.name,
            image: item.products.image_url,
            specs: item.products.specs || '',
            price: item.products.price,
            quantity: item.quantity,
            selected: true,
            productId: item.product_id,
            category: item.products.categories,
          });
          return acc;
        }, []);
        setStores(storesData);
      }
    } catch (error) {
      console.error('Failed to fetch cart data:', error);
    } finally {
      setLoading(false);
    }
  };

    useFocusEffect(
    React.useCallback(() => {
      const checkUserAndFetchData = async () => {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
        await fetchCartData(user);
      };
      checkUserAndFetchData();
    }, [])
  );

  // Calculate totals
  const selectedItemsCount = stores.reduce((acc, store) => 
    acc + store.items.filter(item => item.selected).reduce((itemAcc, item) => itemAcc + item.quantity, 0)
  , 0);

  const totalPrice = stores.reduce((acc, store) => 
    acc + store.items.filter(item => item.selected).reduce((itemAcc, item) => itemAcc + item.price * item.quantity, 0)
  , 0);
  
  // Mock discount, in a real app this would be calculated based on promotions
  const totalDiscount = stores.reduce((acc, store) => {
    if (store.promotion && store.items.every(item => item.selected)) { // Simplified: discount if all store items selected
        if (store.name.includes('Apple') && totalPrice >= 1000) return acc + 100;
        // Add other store-specific discount logic here
    }
    return acc;
  }, 0);

  const finalPrice = totalPrice - totalDiscount;

  const allItemsGloballySelected = stores.every(store => store.selected && store.items.every(item => item.selected));

    const handleDeleteSelected = async () => {
    const selectedItems = stores.flatMap(s => s.items.filter(i => i.selected));
    if (selectedItems.length === 0) return;

    const promises = selectedItems.map(item => removeFromCart(item.id));
    const results = await Promise.all(promises);

    const failed = results.filter(res => res.error);
    if (failed.length > 0) {
      console.error('Some items failed to delete:', failed);
      alert(`Failed to delete ${failed.length} items.`);
    }

    await fetchCartData(user);
  };

  const handleCheckout = () => {
    const selectedItemsForCheckout = stores
      .flatMap(store => store.items.filter(item => item.selected));

    if (selectedItemsForCheckout.length === 0) {
      return;
    }

    router.push({
      pathname: '/checkout',
      params: { selectedItems: JSON.stringify(selectedItemsForCheckout) },
    });
  };

  const toggleGlobalSelectAll = () => {
    const newSelectState = !allItemsGloballySelected;
    setStores(prevStores => 
      prevStores.map(store => ({
        ...store,
        selected: newSelectState,
        items: store.items.map(item => ({ ...item, selected: newSelectState }))
      }))
    );
  };

  const toggleStoreSelectAll = (storeId: string) => {
    setStores(prevStores =>
      prevStores.map(store => {
        if (store.id === storeId) {
          const newStoreSelectState = !store.selected;
          return {
            ...store,
            selected: newStoreSelectState,
            items: store.items.map(item => ({ ...item, selected: newStoreSelectState }))
          };
        }
        return store;
      })
    );
  };

  const toggleItemSelect = (storeId: string, itemId: string) => {
    setStores(prevStores => 
      prevStores.map(store => {
        if (store.id === storeId) {
          const newItems = store.items.map(item => 
            item.id === itemId ? { ...item, selected: !item.selected } : item
          );
          const allItemsInStoreSelected = newItems.every(item => item.selected);
          return { ...store, items: newItems, selected: allItemsInStoreSelected };
        }
        return store;
      })
    );
  };

  const handleQuantityChange = async (itemId: string, currentQuantity: number, action: 'increment' | 'decrement') => {
    if (!user) return;

    const newQuantity = action === 'increment' ? currentQuantity + 1 : currentQuantity - 1;

    try {
      if (newQuantity <= 0) {
        const { error } = await removeFromCart(itemId);
        if (error) throw error;
      } else {
        const { error } = await updateCartItemQuantity(itemId, newQuantity);
        if (error) throw error;
      }
      await fetchCartData(user);
    } catch (error) {
      console.error('Failed to update cart:', error);
    }
  };

  const renderStoreItem = ({ item: store }: { item: Store }) => (
    <RNView style={[styles.storeSection, { backgroundColor: colors.card }]}>
      <RNView style={styles.storeHeader}>
        <TouchableOpacity onPress={() => toggleStoreSelectAll(store.id)} style={styles.checkboxContainer}>
          <FontAwesome name={store.selected ? 'check-circle' : 'circle-o'} size={20} color={store.selected ? colors.primary : colors.textMuted} />
        </TouchableOpacity>
        {store.icon && <FontAwesome name={store.icon as any} size={18} color={colors.text} style={styles.storeIcon} />}
        <RNText style={[styles.storeName, { color: colors.text }]}>{store.name}</RNText>
        <FontAwesome name="angle-right" size={18} color={colors.textMuted} />
      </RNView>

      {store.items.map(product => (
        <RNView key={product.id} style={styles.productItem}>
          <TouchableOpacity onPress={() => toggleItemSelect(store.id, product.id)} style={styles.checkboxContainer}>
            <FontAwesome name={product.selected ? 'check-circle' : 'circle-o'} size={20} color={product.selected ? colors.primary : colors.textMuted} />
          </TouchableOpacity>
          <Image source={{ uri: product.image }} style={styles.productImage} contentFit="cover" />
          <RNView style={styles.productDetails}>
            <RNText style={[styles.productName, { color: colors.text }]} numberOfLines={2}>{product.name}</RNText>
            <RNText style={[styles.productSpecs, { color: colors.textMuted }]}>{product.specs}</RNText>
            <RNView style={styles.priceAndStepper}>
              <RNText style={[styles.productPrice, { color: colors.error }]}>¥{product.price}</RNText>
              <RNView style={styles.quantitySelector}>
                <TouchableOpacity
                  style={[styles.quantityButton, { borderColor: colors.border }]}
                  onPress={() => handleQuantityChange(product.id, product.quantity, 'decrement')}
                >
                  <FontAwesome name="minus" size={12} color={colors.text} />
                </TouchableOpacity>
                <RNText style={[styles.quantityText, { color: colors.text }]}>{product.quantity}</RNText>
                <TouchableOpacity
                  style={[styles.quantityButton, { borderColor: colors.border }]}
                  onPress={() => handleQuantityChange(product.id, product.quantity, 'increment')}
                >
                  <FontAwesome name="plus" size={12} color={colors.text} />
                </TouchableOpacity>
              </RNView>
            </RNView>
          </RNView>
        </RNView>
      ))}
      {store.promotion && (
        <RNView style={styles.promotionContainer}>
          <RNView style={[styles.promotionTag, { backgroundColor: colors.error }]}>
            <RNText style={styles.promotionTagText}>满减</RNText>
          </RNView>
          <RNText style={[styles.promotionText, { color: colors.text }]}>{store.promotion}</RNText>
        </RNView>
      )}
    </RNView>
  );

  const renderRecommendedItem = ({ item }: { item: { id: string; name: string; image: string; price: number } }) => (
    <TouchableOpacity style={[styles.recommendedItemCard, { backgroundColor: colors.card }]}>
      <Image source={{ uri: item.image }} style={styles.recommendedItemImage} contentFit="cover" />
      <RNText style={[styles.recommendedItemName, { color: colors.text }]} numberOfLines={2}>{item.name}</RNText>
      <RNText style={[styles.recommendedItemPrice, { color: colors.error }]}>¥{item.price}</RNText>
    </TouchableOpacity>
  );

  if (stores.length === 0) {
    return (
      <RNView style={[styles.emptyContainer, { backgroundColor: colors.background }]}>
        <FontAwesome name="shopping-cart" size={60} color={colors.textMuted} />
        <RNText style={[styles.emptyText, { color: colors.textMuted }]}>购物车还是空的</RNText>
        {/* Optional: Add a button to go shopping */}
      </RNView>
    );
  }

  return (
    <RNView style={[styles.container, { backgroundColor: colors.backgroundSecondary }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <RNView style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
        <RNText style={[styles.headerTitle, { color: colors.text }]}>购物车</RNText>
        <TouchableOpacity onPress={() => setIsEditMode(!isEditMode)} style={styles.manageButton}>
          <RNText style={{ color: colors.primary }}>{isEditMode ? '完成' : '管理'}</RNText>
        </TouchableOpacity>
      </RNView>

      <FlatList
        data={stores}
        renderItem={renderStoreItem}
        keyExtractor={store => store.id}
        ListFooterComponent={() => (
          <RNView style={[styles.recommendedSection, { backgroundColor: colors.backgroundSecondary }]}>
            <RNText style={[styles.recommendedTitle, { color: colors.text }]}>猜你喜欢</RNText>
            <FlatList
              data={recommendedProductsData}
              renderItem={renderRecommendedItem}
              keyExtractor={item => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.recommendedListContainer}
            />
          </RNView>
        )}
        contentContainerStyle={styles.listContentContainer}
      />

      <RNView style={[styles.footer, { backgroundColor: colors.background, borderTopColor: colors.border }]}>
        <TouchableOpacity onPress={toggleGlobalSelectAll} style={styles.selectAllContainer}>
          <FontAwesome name={allItemsGloballySelected ? 'check-circle' : 'circle-o'} size={20} color={allItemsGloballySelected ? colors.primary : colors.textMuted} />
          <RNText style={[styles.selectAllText, { color: colors.text }]}>全选</RNText>
        </TouchableOpacity>
        <RNView style={styles.totalInfoContainer}>
          <RNText style={[styles.totalPriceText, { color: colors.text }]}>
            合计: <RNText style={{ color: colors.error, fontWeight: 'bold' }}>¥{finalPrice.toFixed(2)}</RNText>
          </RNText>
          {totalDiscount > 0 && <RNText style={[styles.discountText, { color: colors.textMuted }]}>已优惠: ¥{totalDiscount.toFixed(2)}</RNText>}
        </RNView>
        <TouchableOpacity
            style={[
              styles.checkoutButton,
              { backgroundColor: selectedItemsCount > 0 ? colors.primary : colors.buttonDisabled },
            ]}
            disabled={selectedItemsCount === 0}
            onPress={handleCheckout}
          >
          <RNText style={styles.checkoutButtonText}>结算({selectedItemsCount})</RNText>
        </TouchableOpacity>
      </RNView>
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: PADDING,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  manageButton: {
    position: 'absolute',
    right: PADDING,
    height: '100%',
    justifyContent: 'center',
  },
  listContentContainer: {
    paddingBottom: PADDING,
  },
  storeSection: {
    marginHorizontal: PADDING,
    marginTop: PADDING,
    borderRadius: 8,
    paddingBottom: PADDING / 2,
    overflow: 'hidden',
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: PADDING * 0.8,
    paddingHorizontal: PADDING,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.light.border,
  },
  checkboxContainer: {
    paddingRight: PADDING * 0.75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storeIcon: {
    marginRight: PADDING / 2,
  },
  storeName: {
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: PADDING,
    paddingHorizontal: PADDING,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.light.backgroundSecondary,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 6,
    marginRight: PADDING,
  },
  productDetails: {
    flex: 1,
    justifyContent: 'space-between',
    height: 80,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  productSpecs: {
    fontSize: 12,
    marginBottom: 6,
    color: Colors.light.textMuted,
  },
  priceAndStepper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.error,
  },
  quantityButton: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 14,
    backgroundColor: Colors.light.backgroundSoft,
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: PADDING,
    minWidth: 20,
    textAlign: 'center',
    color: Colors.light.text,
  },
  promotionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: PADDING,
    paddingVertical: PADDING / 1.5,
    marginTop: PADDING / 2,
  },
  promotionTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: PADDING / 2,
    backgroundColor: Colors.light.error,
  },
  promotionTagText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  promotionText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: PADDING,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  selectAllContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    marginLeft: PADDING / 2,
    fontSize: 14,
    color: Colors.light.text,
  },
  totalInfoContainer: {
    flex: 1,
    alignItems: 'flex-end',
    marginHorizontal: PADDING,
  },
  totalPriceText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  discountText: {
    fontSize: 12,
    marginTop: 2,
    color: Colors.light.textMuted,
  },
  checkoutButton: {
    paddingHorizontal: PADDING * 1.5,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
  },
  checkoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  emptyText: {
    marginTop: PADDING,
    fontSize: 16,
    color: Colors.light.textMuted,
  },
  recommendedSection: {
    paddingVertical: PADDING,
    marginTop: PADDING,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  recommendedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: PADDING,
    paddingHorizontal: PADDING,
    color: Colors.light.text,
  },
  recommendedListContainer: {
    paddingHorizontal: PADDING,
  },
  recommendedItemCard: {
    width: SCREEN_WIDTH * 0.35,
    marginRight: PADDING * 0.75,
    borderRadius: 8,
    padding: PADDING / 1.5,
    overflow: 'hidden',
    backgroundColor: Colors.light.card,
  },
  recommendedItemImage: {
    width: '100%',
    height: SCREEN_WIDTH * 0.35,
    borderRadius: 6,
    marginBottom: PADDING / 2,
  },
  recommendedItemName: {
    fontSize: 13,
    marginBottom: 2,
    minHeight: 30,
  },
  recommendedItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});
