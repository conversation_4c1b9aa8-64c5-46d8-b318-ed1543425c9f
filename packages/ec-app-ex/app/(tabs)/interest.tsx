import React, { useState } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Dimensions,
  View as RNView,
  Text as RNText,
  SafeAreaView,
} from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { Colors } => from '@ec-nx/shared-logic/constants/Colors';
import { useColorScheme } => from '@ec-nx/shared-logic/hooks/useColorScheme';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const PADDING = 15;

// Mock Data
const filterTabsData = [
  { id: 'foryou', name: '为你推荐' },
  { id: 'digital', name: '数码' },
  { id: 'apparel', name: '服饰' },
  { id: 'food', name: '美食' },
  { id: 'home', name: '家居' },
  { id: 'beauty', name: '美妆' },
];

const bannerData = {
  image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1351&q=80', // Placeholder lifestyle image
  text: '基于你的兴趣\n我们为你精选了这些产品',
};

const productSectionsData = [
  {
    id: 'digital_lovers',
    title: '数码爱好者精选',
    matchPercentage: 98,
    products: [
      {
        id: 'watch_ultra',
        name: '智能手表 Ultra',
        description: 'GPS | 心率监测 | 长续航',
        price: 2199,
        originalPrice: 2499,
        image: 'https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=700&q=80',
        tags: ['热门', '新品'],
        isLiked: true,
      },
      {
        id: 'headphones_pro',
        name: '降噪耳机 Pro',
        description: '主动降噪 | Hi-Fi音质 | 20小时播放',
        price: 1299,
        originalPrice: 1599,
        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-1.2.1&auto=format&fit=crop&w=700&q=80',
        tags: ['推荐'],
        isLiked: false,
      },
    ],
  },
  {
    id: 'sports_fitness',
    title: '运动健身推荐',
    matchPercentage: 95,
    products: [
      {
        id: 'running_shoes',
        name: '专业跑步鞋',
        description: '轻量缓震 | 透气网面 | 防滑耐磨',
        price: 599,
        originalPrice: 799,
        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-1.2.1&auto=format&fit=crop&w=700&q=80',
        tags: ['畅销'],
        isLiked: true,
      },
      {
        id: 'sports_band',
        name: '智能运动手环',
        description: '心率监测 | 睡眠分析 | 14天续航',
        price: 199,
        originalPrice: 249,
        image: 'https://images.unsplash.com/photo-1520895666338-568967205175?ixlib=rb-1.2.1&auto=format&fit=crop&w=700&q=80',
        tags: ['限时'],
        isLiked: false,
      },
    ],
  },
];

const recentlyViewedData = [
  { id: 'rv1', image: 'https://images.unsplash.com/photo-1588099768523-f4e6a5679d88?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80' },
  { id: 'rv2', image: 'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80' },
  { id: 'rv3', image: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80' },
  { id: 'rv4', image: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80' },
];

// Types
type Product = typeof productSectionsData[0]['products'][0];

export default function InterestScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [activeFilterId, setActiveFilterId] = useState(filterTabsData[0].id);

  const RenderFilterTab = ({ item }: { item: typeof filterTabsData[0] }) => (
    <TouchableOpacity
      style={[
        styles.filterTab,
        item.id === activeFilterId && { backgroundColor: colors.primary },
        { borderColor: item.id === activeFilterId ? colors.primary : colors.border },
      ]}
      onPress={() => setActiveFilterId(item.id)}
    >
      <RNText style={[styles.filterTabText, item.id === activeFilterId && { color: colors.background }]}>
        {item.name}
      </RNText>
    </TouchableOpacity>
  );

  const RenderProductCard = ({ item }: { item: Product }) => {
    const [isLiked, setIsLiked] = useState(item.isLiked);
    return (
      <TouchableOpacity style={[styles.productCard, { backgroundColor: colors.card }]}>
        <Image source={{ uri: item.image }} style={styles.productImage} />
        <TouchableOpacity style={styles.likeButton} onPress={() => setIsLiked(!isLiked)}>
          <FontAwesome name={isLiked ? 'heart' : 'heart-o'} size={18} color={isLiked ? colors.secondary : colors.textTertiary} />
        </TouchableOpacity>
        <RNView style={styles.productInfo}>
          <RNText style={[styles.productName, { color: colors.text }]} numberOfLines={1}>{item.name}</RNText>
          <RNText style={[styles.productDescription, { color: colors.textSecondary }]} numberOfLines={2}>{item.description}</RNText>
          <RNView style={styles.priceContainer}>
            <RNText style={[styles.currentPrice, { color: colors.secondary }]}>¥{item.price}</RNText>
            <RNText style={[styles.originalPrice, { color: colors.textTertiary }]}>¥{item.originalPrice}</RNText>
          </RNView>
          <RNView style={styles.tagsContainer}>
            {item.tags.map(tag => (
              <RNView key={tag} style={[styles.tag, { backgroundColor: colors.primaryTransparent }]}>
                <RNText style={[styles.tagText, { color: colors.primary }]}>{tag}</RNText>
              </RNView>
            ))}
          </RNView>
        </RNView>
      </TouchableOpacity>
    );
  };

  const RenderRecentlyViewedItem = ({ item }: { item: typeof recentlyViewedData[0] }) => (
    <TouchableOpacity style={styles.recentlyViewedItem}>
      <Image source={{ uri: item.image }} style={styles.recentlyViewedImage} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      {/* Custom Header */}
      <RNView style={[styles.header, { borderBottomColor: colors.border }]}>
        <RNText style={[styles.headerTitle, { color: colors.text }]}>兴趣推荐</RNText>
        <TouchableOpacity>
          <FontAwesome name="sliders" size={22} color={colors.text} />
        </TouchableOpacity>
      </RNView>

      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {/* Filter Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterTabsContainer}>
          {filterTabsData.map(tab => <RenderFilterTab key={tab.id} item={tab} />)}
        </ScrollView>

        {/* Banner */}
        <RNView style={styles.bannerContainer}>
          <Image source={{ uri: bannerData.image }} style={styles.bannerImage} />
          <RNView style={styles.bannerTextContainer}>
            <RNText style={styles.bannerText}>{bannerData.text}</RNText>
          </RNView>
        </RNView>

        {/* Product Sections */}
        {productSectionsData.map(section => (
          <RNView key={section.id} style={styles.productSection}>
            <RNView style={styles.sectionHeader}>
              <RNText style={[styles.sectionTitle, { color: colors.text }]}>{section.title}</RNText>
              <RNText style={[styles.matchPercentage, { color: colors.secondary }]}>{section.matchPercentage}% 匹配</RNText>
            </RNView>
            {section.products.map(product => <RenderProductCard key={product.id} item={product} />)}
          </RNView>
        ))}

        {/* Recently Viewed */}
        <RNView style={styles.recentlyViewedSection}>
          <RNText style={[styles.sectionTitle, { color: colors.text, marginBottom: PADDING / 2 }]}>最近浏览</RNText>
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            data={recentlyViewedData}
            renderItem={RenderRecentlyViewedItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.recentlyViewedList}
          />
        </RNView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: PADDING,
    paddingVertical: PADDING / 1.5,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollViewContent: {
    paddingBottom: PADDING,
  },
  filterTabsContainer: {
    paddingHorizontal: PADDING,
    paddingVertical: PADDING,
  },
  filterTab: {
    paddingHorizontal: PADDING * 1.2,
    paddingVertical: PADDING / 2,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: PADDING / 1.5,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  bannerContainer: {
    marginHorizontal: PADDING,
    borderRadius: 10,
    overflow: 'hidden',
    height: SCREEN_WIDTH * 0.4,
    marginBottom: PADDING * 1.5,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  bannerTextContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: PADDING,
  },
  bannerText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 24,
  },
  productSection: {
    marginBottom: PADDING,
    paddingHorizontal: PADDING,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: PADDING,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  matchPercentage: {
    fontSize: 13,
    fontWeight: '600',
  },
  productCard: {
    flexDirection: 'row',
    borderRadius: 10,
    padding: PADDING,
    marginBottom: PADDING,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    // Elevation for Android
    elevation: 2,
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: PADDING,
  },
  likeButton: {
    position: 'absolute',
    top: PADDING + 5,
    left: 100 - PADDING / 2, // Position near the image corner
    backgroundColor: 'rgba(255,255,255,0.7)',
    padding: 5,
    borderRadius: 15,
  },
  productInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 12,
    marginBottom: 6,
    lineHeight: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 6,
  },
  currentPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '600',
  },
  recentlyViewedSection: {
    paddingHorizontal: PADDING,
    marginTop: PADDING / 2,
  },
  recentlyViewedList: {
    paddingTop: PADDING / 2,
  },
  recentlyViewedItem: {
    marginRight: PADDING / 1.5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  recentlyViewedImage: {
    width: 80,
    height: 80,
  },
});
