import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  View as RNView,
  Text as RNText,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { useColorScheme } from '@ec-nx/shared-logic/hooks/useColorScheme';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile, useUserOrders, useFavorites } from '@/hooks/useSupabaseData';
import { router } from 'expo-router';

const SCREEN_WIDTH = Dimensions.get('window').width;
const CARD_PADDING = 15;

// Mock user data based on prototype
const userData = {
  name: '张三',
  avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
  id: '8675309',
  level: 'Lv.4',
  levelProgress: 0.6, // Example progress for Lv.4
  stats: {
    collections: 32,
    following: 128,
    footprints: 1024,
    fans: 96,
  },
};

// Mock order data based on prototype
const myOrdersData = [
  { id: '1', title: '待付款', icon: 'credit-card' as const, badge: 2, status: 'pending-payment' },
  { id: '2', title: '待发货', icon: 'archive' as const, badge: 1, status: 'pending-shipment' },
  { id: '3', title: '待收货', icon: 'truck' as const, badge: 0, status: 'pending-reception' },
  { id: '4', title: '待评价', icon: 'commenting-o' as const, badge: 0, status: 'pending-review' },
  { id: '5', title: '退款/售后', icon: 'refresh' as const, badge: 0, status: 'refund' },
];

// Mock wallet data based on prototype
const myWalletData = {
  balance: '520',
  points: '3200',
  coupons: '12',
  giftCards: '8',
};

// Mock services data based on prototype
const myServicesData = [
  { id: '1', title: '我的收藏', icon: 'heart-o' as const },
  { id: '2', title: '关注店铺', icon: 'building-o' as const },
  { id: '3', title: '浏览历史', icon: 'history' as const },
  { id: '4', title: '收货地址', icon: 'map-marker' as const },
  { id: '5', title: '客服中心', icon: 'headphones' as const },
  { id: '6', title: '邀请有礼', icon: 'gift' as const },
  { id: '7', title: '领券中心', icon: 'ticket' as const },
  { id: '8', title: '帮助中心', icon: 'question-circle-o' as const },
];

// Mock recommendations data based on prototype
const recommendationsData = [
  { id: '1', image: 'https://i.imgur.com/gQkH09q.png', name: '真无线蓝牙耳机 Pro', price: '¥1299' },
  { id: '2', image: 'https://i.imgur.com/r33qN5a.png', name: '智能手表 Ultra', price: '¥2199' },
  { id: '3', image: 'https://i.imgur.com/sDk2o2s.png', name: '头戴式无线耳机', price: '¥899' },
  { id: '4', image: 'https://i.imgur.com/x7KqZ2A.png', name: '20000mAh 快充移动电源', price: '¥199' },
];

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const styles = getStyles(colors);
  
  // Authentication state
  const { user, logout } = useAuth();
  
  // User profile data
  const { profile, loading: profileLoading, error: profileError, refresh: refreshProfile } = 
    useUserProfile(user?.id);
    
  // User orders data
  const { orders, loading: ordersLoading, error: ordersError } = 
    useUserOrders(user?.id);
    
  // User favorites data
  const { favorites, loading: favoritesLoading, error: favoritesError } = 
    useFavorites(user?.id);
  
  // Loading states
  const isLoading = profileLoading || ordersLoading || favoritesLoading;
  
  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      Alert.alert('成功', '您已成功退出登录');
    } catch (error) {
      Alert.alert('错误', '退出登录失败，请稍后再试');
    }
  };
  
  // Navigate to login
  const navigateToLogin = () => {
    router.push('/login');
  };

  // Navigate to orders list page
  const handleNavigateToOrders = (status?: string) => {
    // Note: This assumes an order list page exists at `app/orders.tsx` or `app/(tabs)/orders.tsx`.
    // The page should be able to handle a 'status' query parameter.
    const path = status ? `/orders?status=${status}` : '/orders';
    router.push(path as any);
  };

  const renderStatItem = (label: string, value: number | string) => (
    <TouchableOpacity style={styles.statItemContainer} key={label}>
      <RNText style={styles.statValue}>{value}</RNText>
      <RNText style={styles.statLabel}>{label}</RNText>
    </TouchableOpacity>
  );

  const renderOrderItem = (item: { id: string; title: string; icon: any; badge: number; status: string }) => (
    <TouchableOpacity style={styles.orderItemContainer} key={item.id} onPress={() => handleNavigateToOrders(item.status)}>
      {item.badge > 0 && (
        <RNView style={styles.orderItemBadge}>
          <RNText style={styles.badgeText}>{item.badge}</RNText>
        </RNView>
      )}
      <FontAwesome name={item.icon} size={24} color={colors.text} />
      <RNText style={styles.orderItemLabel}>{item.title}</RNText>
    </TouchableOpacity>
  );

  const renderServiceItem = (item: { id: string; title: string; icon: any }) => (
    <TouchableOpacity style={styles.serviceItemContainer} key={item.id}>
      <FontAwesome name={item.icon as any} size={26} color={colors.primary} />
      <RNText style={styles.serviceItemLabel}>{item.title}</RNText>
    </TouchableOpacity>
  );

  const renderRecommendationItem = (item: typeof recommendationsData[0]) => (
    <TouchableOpacity style={styles.recommendationItemContainer} key={item.id}>
      <Image source={{ uri: item.image }} style={styles.recommendationItemImage} />
      <RNText style={styles.recommendationItemName}>{item.name}</RNText>
      <RNText style={styles.recommendationItemPrice}>¥{item.price}</RNText>
    </TouchableOpacity>
  );

  // If not logged in, show login prompt
  if (!user) {
    return (
      <RNView style={styles.loginPromptContainer}>
        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        <Image 
          source={{ uri: 'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80' }} 
          style={styles.loginPromptImage} 
        />
        <RNText style={styles.loginPromptTitle}>欢迎来到EC商城</RNText>
        <RNText style={styles.loginPromptText}>登录后可以查看您的个人信息、订单、收藏等</RNText>
        <TouchableOpacity style={styles.loginButton} onPress={navigateToLogin}>
          <RNText style={styles.loginButtonText}>立即登录/注册</RNText>
        </TouchableOpacity>
      </RNView>
    );
  }
  
  // Show loading state
  if (isLoading) {
    return (
      <RNView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <RNText style={styles.loadingText}>加载中...</RNText>
      </RNView>
    );
  }
  
  // User data from Supabase or fallback to defaults
  const userData = {
    name: profile?.full_name || profile?.username || (user.email ? user.email.split('@')[0] : '') || '用户',
    avatar: profile?.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
    id: (profile?.id ? String(profile.id).substring(0, 8) : null) || (user?.id ? String(user.id).substring(0, 8) : 'N/A'),
    level: profile?.level ? (typeof profile.level === 'number' ? `Lv.${profile.level}` : String(profile.level)) : 'Lv.1',
    levelProgress: profile?.level_progress || 0.2,
    stats: {
      collections: favorites?.length || 0,
      following: profile?.following_count || 0,
      footprints: profile?.footprints_count || 0,
      fans: profile?.fans_count || 0,
    },
  };
  
  // Order counts by status
  const orderCounts = {
    pending_payment: 0,
    pending_shipment: 0,
    pending_receipt: 0,
    pending_review: 0,
    refund: 0
  };
  
  // Count orders by status
  if (orders) {
    orders.forEach(order => {
      if (order.status === 'pending_payment') orderCounts.pending_payment++;
      else if (order.status === 'pending_shipment') orderCounts.pending_shipment++;
      else if (order.status === 'pending_receipt') orderCounts.pending_receipt++;
      else if (order.status === 'pending_review') orderCounts.pending_review++;
      else if (order.status === 'refund' || order.status === 'after_sale') orderCounts.refund++;
    });
  }
  
  // Create dynamic order data based on real order counts
  const userOrdersData = [
    { id: '1', title: '待付款', icon: 'credit-card' as const, badge: orderCounts.pending_payment, status: 'pending_payment' },
    { id: '2', title: '待发货', icon: 'archive' as const, badge: orderCounts.pending_shipment, status: 'pending_shipment' },
    { id: '3', title: '待收货', icon: 'truck' as const, badge: orderCounts.pending_receipt, status: 'pending_receipt' },
    { id: '4', title: '待评价', icon: 'commenting-o' as const, badge: orderCounts.pending_review, status: 'pending_review' },
    { id: '5', title: '退款/售后', icon: 'refresh' as const, badge: orderCounts.refund, status: 'refund' },
  ];

  return (
    <RNView style={styles.screenContainer}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <RNView style={styles.customHeader}>
        <TouchableOpacity 
          style={styles.headerIconContainer}
          onPress={() => router.push('/settings')}
        >
          <FontAwesome name="cog" size={24} color={colors.text} />
        </TouchableOpacity>
        <RNView style={styles.headerRightIcons}>
          <TouchableOpacity style={styles.headerIconContainer}>
            <FontAwesome name="bell-o" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={[styles.headerIconContainer, { marginLeft: 15 }]}>
            <FontAwesome name="commenting-o" size={24} color={colors.text} />
          </TouchableOpacity>
        </RNView>
      </RNView>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* User Info Section */}
        <RNView style={styles.userInfoSection}>
          <Image source={{ uri: userData.avatar }} style={styles.avatar} />
          <RNView style={styles.userInfoTextContainer}>
            <RNText style={styles.userNameText}>{userData.name}</RNText>
            <RNText style={styles.userIdText}>ID: {userData.id}</RNText>
            <RNView style={styles.userLevelContainer}>
              <RNText style={styles.userLevelText}>{userData.level}</RNText>
            </RNView>
            <RNView style={styles.levelProgressContainer}>
              <RNView style={[styles.levelProgressBar, { width: `${userData.levelProgress * 100}%` }]} />
            </RNView>
          </RNView>
          <FontAwesome name="angle-right" size={22} color={colors.textSecondary} style={styles.userInfoArrow} />
        </RNView>

        {/* User Stats Section */}
        <RNView style={styles.userStatsSection}>
          {renderStatItem('收藏', userData.stats.collections)}
          {renderStatItem('关注', userData.stats.following)}
          {renderStatItem('足迹', userData.stats.footprints)}
          {renderStatItem('粉丝', userData.stats.fans)}
        </RNView>

        {/* My Orders Section */}
        <RNView style={styles.cardContainer}>
          <RNView style={styles.cardHeader}>
            <RNText style={styles.cardTitle}>我的订单</RNText>
            <TouchableOpacity style={styles.viewAllButton} onPress={() => handleNavigateToOrders()}>
              <RNText style={styles.viewAllText}>查看全部</RNText>
              <FontAwesome name="angle-right" size={16} color={colors.textSecondary} style={{ marginLeft: 4 }} />
            </TouchableOpacity>
          </RNView>
          <RNView style={styles.ordersGridContainer}>
            {userOrdersData.map(renderOrderItem)}
          </RNView>
        </RNView>

        {/* My Wallet Section */}
        <RNView style={styles.cardContainer}>
          <RNView style={styles.cardHeader}>
            <RNText style={styles.cardTitle}>我的钱包</RNText>
            <TouchableOpacity style={styles.viewAllButton} onPress={() => handleNavigateToOrders()}>
              <RNText style={styles.viewAllText}>查看全部</RNText>
              <FontAwesome name="angle-right" size={16} color={colors.textSecondary} style={{ marginLeft: 4 }} />
            </TouchableOpacity>
          </RNView>
          <RNView style={styles.walletGridContainer}>
            <RNView style={styles.walletItemContainer}>
              <RNText style={styles.walletItemValue}>¥{myWalletData.balance}</RNText>
              <RNText style={styles.walletItemTitle}>余额</RNText>
            </RNView>
            <RNView style={styles.walletItemContainer}>
              <RNText style={styles.walletItemValue}>{myWalletData.points}</RNText>
              <RNText style={styles.walletItemTitle}>积分</RNText>
            </RNView>
            <RNView style={styles.walletItemContainer}>
              <RNText style={styles.walletItemValue}>{myWalletData.coupons}</RNText>
              <RNText style={styles.walletItemTitle}>优惠券</RNText>
            </RNView>
            <RNView style={styles.walletItemContainer}>
              <RNText style={styles.walletItemValue}>{myWalletData.giftCards}</RNText>
              <RNText style={styles.walletItemTitle}>礼品卡</RNText>
            </RNView>
          </RNView>
        </RNView>

        {/* My Services Section */}
        <RNView style={styles.cardContainer}>
          <RNView style={styles.cardHeader}>
            <RNText style={styles.cardTitle}>我的服务</RNText>
          </RNView>
          <RNView style={styles.servicesGridContainer}>
            {myServicesData.map(renderServiceItem)}
          </RNView>
        </RNView>

        {/* Recommended For You Section */}
        <RNView style={styles.recommendationsSectionContainer}>
          <RNView style={styles.cardHeaderNoBorder}>
            <RNText style={styles.cardTitle}>为你推荐</RNText>
          </RNView>
          <RNView style={styles.recommendationsGridContainer}>
            {recommendationsData.map(renderRecommendationItem)}
          </RNView>
        </RNView>
      </ScrollView>
    </RNView>
  );
}

const getStyles = (colors: typeof Colors.light) => StyleSheet.create({
  // Login prompt styles
  loginPromptContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    padding: 20,
  },
  loginPromptImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 20,
  },
  loginPromptTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 10,
  },
  loginPromptText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 30,
  },
  loginButton: {
    backgroundColor: colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 10,
    alignItems: 'center',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.textSecondary,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: colors.background, // Light gray background for the whole page
  },
  customHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: CARD_PADDING,
    paddingTop: Platform.OS === 'android' ? 50 : 44, // Fixed value instead of StatusBar.currentHeight
    paddingBottom: 10,
    backgroundColor: colors.background, // Match page background or make transparent
  },
  headerIconContainer: {
    padding: 5,
  },
  headerRightIcons: {
    flexDirection: 'row',
  },
  scrollContentContainer: {
    backgroundColor: colors.background,
    paddingBottom: 30,
  },
  // User Info
  userInfoSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: CARD_PADDING,
    marginHorizontal: CARD_PADDING,
    marginTop: 10,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
  },
  userInfoTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  userNameText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 2,
  },
  userIdText: {
    fontSize: 13,
    color: colors.textSecondary,
  },
  userLevelContainer: {
    alignItems: 'flex-start',
    marginLeft: 'auto',
    paddingLeft: 10, // Space before arrow
  },
  userLevelText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.text,
  },
  levelProgressContainer: {
    height: 6,
    width: 60, // Width of the progress bar
    backgroundColor: colors.backgroundSecondary, // Light gray for track
    borderRadius: 3,
    marginTop: 6,
    overflow: 'hidden',
  },
  levelProgressBar: {
    height: '100%',
    backgroundColor: '#FFA500', // Orange progress
    borderRadius: 3,
  },
  userInfoArrow: {
    marginLeft: 8,
  },
  // User Stats
  userStatsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.background,
    paddingVertical: CARD_PADDING,
    marginHorizontal: CARD_PADDING,
    marginTop: CARD_PADDING,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  statItemContainer: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 13,
    color: colors.textSecondary,
    marginTop: 4,
  },
  // Common Card Styles
  cardContainer: {
    backgroundColor: colors.background,
    marginHorizontal: CARD_PADDING,
    marginTop: CARD_PADDING,
    borderRadius: 12,
    padding: CARD_PADDING,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: CARD_PADDING,
    paddingBottom: CARD_PADDING / 2,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  cardHeaderNoBorder: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: CARD_PADDING,
    paddingHorizontal: CARD_PADDING, // For recommendations title alignment
    marginTop: CARD_PADDING, // For recommendations title alignment
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 13,
    color: colors.textSecondary,
  },
  // My Orders
  ordersGridContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  orderItemContainer: {
    alignItems: 'center',
    width: (SCREEN_WIDTH - (CARD_PADDING * 4)) / 5, // 5 items in a row
    position: 'relative',
  },
  orderItemBadge: {
    position: 'absolute',
    top: -5,
    right: 5, 
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    paddingHorizontal: 5,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  orderItemLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 8,
  },
  // My Wallet
  walletGridContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  walletItemContainer: {
    alignItems: 'center',
  },
  walletItemValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.error, // Red color for values
  },
  walletItemTitle: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  // My Services
  servicesGridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  serviceItemContainer: {
    width: (SCREEN_WIDTH - (CARD_PADDING * 4)) / 4 - 5, // 4 items per row, adjust spacing
    alignItems: 'center',
    marginBottom: CARD_PADDING + 5,
  },
  serviceItemLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  // Recommendations
  recommendationsSectionContainer: {
    // No card style for the main container, title is separate
    // backgroundColor: colors.backgroundPage, // Matches page background
  },
  recommendationsGridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: CARD_PADDING,
  },
  recommendationItemContainer: {
    width: (SCREEN_WIDTH - (CARD_PADDING * 3)) / 2 - 2, // 2 items per row, adjust spacing
    backgroundColor: colors.background,
    borderRadius: 8,
    marginBottom: CARD_PADDING,
    padding: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  recommendationItemImage: {
    width: '100%',
    height: (SCREEN_WIDTH - (CARD_PADDING * 3)) / 2 - 20, // Square-ish image
    borderRadius: 6,
    backgroundColor: colors.backgroundSecondary, // Placeholder bg
  },
  recommendationItemName: {
    fontSize: 13,
    color: colors.text,
    marginTop: 8,
    minHeight: 32, // For two lines
  },
  recommendationItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.error,
    marginTop: 4,
  },
});
