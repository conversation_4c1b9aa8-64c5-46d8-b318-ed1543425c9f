import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Dimensions,
  View as RNView,
  Text as RNText,
  TextInput,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { useColorScheme, Colors } from '@ec-nx/shared-logic';
import { getCategories, getProductsByCategory, getProducts } from '@/lib/supabase';
import { router } from 'expo-router';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const PADDING = 15;
const LEFT_SIDEBAR_WIDTH = 90;

// Type definitions
type Category = {
  id: string;
  name: string;
  description?: string;
  slug: string;
};

type Product = {
  id: string;
  name: string;
  description?: string;
  price: number;
  image_url: string;
  category_id: string;
  created_at: string;
  updated_at: string;
  stock_quantity?: number;
  discount_percent?: number;
  rating?: number;
};

// Add a recommended category at the beginning
const recommendedCategory = { id: 'rec', name: '推荐', slug: 'recommended' };

// Mock data for the 'Recommended' category content
const recommendedContent = {
  bannerImage: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80', // Placeholder SALE banner
  hotBrands: [
    { id: 'apple', name: 'Apple', image: 'https://images.unsplash.com/photo-1576633587382-13ddf37ac517?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80' }, // Placeholder Apple
    { id: 'samsung', name: 'Samsung', image: 'https://images.unsplash.com/photo-1610945415295-d95246aa2d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80' }, // Placeholder Samsung
    { id: 'xiaomi', name: 'Xiaomi', image: 'https://images.unsplash.com/photo-1598550463204-3192954e0c97?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80' }, // Placeholder Xiaomi
  ],
  sections: [
    {
      id: 'mobile_comms',
      title: '手机通讯',
      items: [
        { id: 'phone', name: '手机', image: 'https://images.unsplash.com/photo-1523206489230-c012c64b2b48?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'case', name: '手机壳', image: 'https://images.unsplash.com/photo-1598327105666-658454391590?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'charger', name: '充电器', image: 'https://images.unsplash.com/photo-1585901452913-2405df79e89b?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'cable', name: '数据线', image: 'https://images.unsplash.com/photo-1604009531779-akae4827e912?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
      ],
    },
    {
      id: 'computer_office',
      title: '电脑办公',
      items: [
        { id: 'laptop', name: '笔记本', image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'tablet', name: '平板电脑', image: 'https://images.unsplash.com/photo-1585425790996-09175ec0876d?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'keyboard', name: '键盘', image: 'https://images.unsplash.com/photo-1587829741301-dc798b83add3?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
        { id: 'mouse', name: '鼠标', image: 'https://images.unsplash.com/photo-1615663245652-8de361839194?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&q=80' },
      ],
    },
  ],
};

// For other categories, we can have a simpler structure or more specific data later
const otherCategoryContent = (categoryName: string) => ({
  title: categoryName,
  items: Array.from({ length: 9 }).map((_, i) => ({
    id: `item_${i}`,
    name: `${categoryName} 商品 ${i + 1}`,
    image: `https://source.unsplash.com/random/150x150?product&sig=${i}${categoryName.charCodeAt(0)}`,
  })),
});

type MainCategoryItemProps = { item: { id: string; name: string; slug?: string }; isActive: boolean; onPress: () => void };
type BrandItemProps = { item: { id: string; name: string; image: string } };
type ProductItemProps = { item: Product | { id: string; name: string; image: string } };

export default function CategoryScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [categories, setCategories] = useState<Category[]>([]);
  const [allCategories, setAllCategories] = useState<(Category | typeof recommendedCategory)[]>([recommendedCategory]);
  const [activeCategoryId, setActiveCategoryId] = useState(recommendedCategory.id);
  const [activeCategory, setActiveCategory] = useState<Category | typeof recommendedCategory>(recommendedCategory);
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // State for category products
  const [categoryProducts, setCategoryProducts] = useState<Record<string, Product[]>>({});
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [productError, setProductError] = useState<Error | null>(null);
  
  // State for featured products (for recommended section)
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);

  // Fetch categories from Supabase
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const { categories: fetchedCategories, error } = await getCategories();
        
        if (error) throw error;
        
        setCategories(fetchedCategories || []);
        setAllCategories([recommendedCategory, ...(fetchedCategories || [])]);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch categories'));
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
    
    // Also fetch featured products for the recommended section
    const fetchFeaturedProducts = async () => {
      try {
        const { products, error } = await getProducts({ limit: 9, sortBy: 'created_at', sortOrder: 'desc' });
        if (error) throw error;
        setFeaturedProducts(products || []);
      } catch (err) {
        console.error('Error fetching featured products:', err);
      }
    };
    
    fetchFeaturedProducts();
  }, []);
  
  // Update active category when activeCategoryId changes
  useEffect(() => {
    const category = allCategories.find(cat => cat.id === activeCategoryId);
    if (category) {
      setActiveCategory(category);
      
      // If it's not the recommended category and we don't have its products yet, fetch them
      if (category.id !== 'rec' && !categoryProducts[category.id]) {
        fetchCategoryProducts(category);
      }
    }
  }, [activeCategoryId, allCategories]);
  
  // Function to fetch products for a specific category
  const fetchCategoryProducts = async (category: Category) => {
    if (category.id === 'rec') return;
    
    try {
      setLoadingProducts(true);
      const { products, error } = await getProductsByCategory(category.slug, 12, 0);
      
      if (error) throw error;
      
      setCategoryProducts(prev => ({
        ...prev,
        [category.id]: products || []
      }));
    } catch (err) {
      console.error(`Error fetching products for category ${category.name}:`, err);
      setProductError(err instanceof Error ? err : new Error(`Failed to fetch products for ${category.name}`));
    } finally {
      setLoadingProducts(false);
    }
  };

  const RenderMainCategoryItem = ({ item, isActive, onPress }: MainCategoryItemProps) => (
    <TouchableOpacity
      style={[
        styles.mainCategoryItem,
        isActive && { backgroundColor: colors.backgroundSecondary, borderLeftColor: colors.primary },
        { backgroundColor: isActive ? colors.backgroundSecondary : colors.background },
      ]}
      onPress={onPress}
    >
      <RNText style={[styles.mainCategoryText, isActive && { color: colors.primary, fontWeight: 'bold' }]}>
        {item.name}
      </RNText>
    </TouchableOpacity>
  );

  const RenderBrandItem = ({ item }: BrandItemProps) => (
    <TouchableOpacity 
      style={styles.brandItemContainer}
      onPress={() => router.push(`/products/brand-${item.id}`)}
    >
      <Image source={{ uri: item.image }} style={styles.brandImage} />
      <RNText style={[styles.brandName, { color: colors.textSecondary }]}>{item.name}</RNText>
    </TouchableOpacity>
  );

  const RenderProductItem = ({ item }: ProductItemProps) => {
    // Handle both mock product items and real products from Supabase
    const imageUrl = 'image' in item ? item.image : (item.image_url || 'https://via.placeholder.com/150');
    const productId = item.id;
    
    return (
      <TouchableOpacity 
        style={styles.productItemContainer}
        onPress={() => router.push(`/product-details/${productId}`)}
      >
        <Image source={{ uri: imageUrl }} style={styles.productImage} />
        <RNText style={[styles.productName, { color: colors.text }]} numberOfLines={1}>{item.name}</RNText>
        {'price' in item && (
          <RNText style={[styles.productPrice, { color: colors.primary }]}>
            ¥{item.price.toFixed(2)}
          </RNText>
        )}
      </TouchableOpacity>
    );
  };

  // Only switch the active category when a tab is clicked, no navigation
  const handleCategoryPress = (category: { id: string; slug?: string }) => {
    setActiveCategoryId(category.id);
    // Fetch products for this category if not already loaded
    if (category.id !== 'rec' && !categoryProducts[category.id]) {
      fetchCategoryProducts(category as Category);
    }
  };

  const renderContent = () => {
    if (!activeCategory) return null;

    if (activeCategory.id === 'rec') {
      return (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.rightContentScrollView}>
          <Image source={{ uri: recommendedContent.bannerImage }} style={styles.bannerImage} />
          
          <RNView style={styles.sectionContainer}>
            <RNView style={styles.sectionHeaderContainer}>
              <RNText style={[styles.sectionTitle, { color: colors.text }]}>热门品牌</RNText>
              <TouchableOpacity onPress={() => router.push('/products/all-brands')}>
                <RNText style={[styles.viewAllText, { color: colors.primary }]}>查看全部 <FontAwesome name="angle-right" size={12} color={colors.primary} /></RNText>
              </TouchableOpacity>
            </RNView>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.hotBrandsScroll}>
              {recommendedContent.hotBrands.map(brand => <RenderBrandItem key={brand.id} item={brand} />)}
            </ScrollView>
          </RNView>

          {/* Featured Products Section */}
          <RNView style={styles.sectionContainer}>
            <RNView style={styles.sectionHeaderContainer}>
              <RNText style={[styles.sectionTitle, { color: colors.text }]}>热门商品</RNText>
              <TouchableOpacity onPress={() => router.push('/products/featured')}>
                <RNText style={[styles.viewAllText, { color: colors.primary }]}>查看全部 <FontAwesome name="angle-right" size={12} color={colors.primary} /></RNText>
              </TouchableOpacity>
            </RNView>
            {featuredProducts.length === 0 ? (
              <RNView style={styles.loadingProductsContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
              </RNView>
            ) : (
              <FlatList
                data={featuredProducts.slice(0, 6)} // Show only first 6 products as a preview
                renderItem={({ item }) => <RenderProductItem item={item} />}
                keyExtractor={item => item.id}
                numColumns={3}
                scrollEnabled={false} // The parent ScrollView handles scrolling
                columnWrapperStyle={styles.productRow}
              />
            )}
          </RNView>

          {/* Category Sections */}
          {recommendedContent.sections.map(section => (
            <RNView key={section.id} style={styles.sectionContainer}>
              <RNView style={styles.sectionHeaderContainer}>
                <RNText style={[styles.sectionTitle, { color: colors.text }]}>{section.title}</RNText>
                <TouchableOpacity onPress={() => router.push(`/products/section-${section.id}`)}>
                  <RNText style={[styles.viewAllText, { color: colors.primary }]}>查看全部 <FontAwesome name="angle-right" size={12} color={colors.primary} /></RNText>
                </TouchableOpacity>
              </RNView>
              <FlatList
                data={section.items}
                renderItem={({ item }) => <RenderProductItem item={item} />}
                keyExtractor={item => item.id}
                numColumns={3}
                scrollEnabled={false}
                columnWrapperStyle={styles.productRow}
              />
            </RNView>
          ))}
        </ScrollView>
      );
    } else {
      // Display real products for the selected category
      const products = categoryProducts[activeCategory.id] || [];
      
      return (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.rightContentScrollView}>
          <RNView style={styles.sectionContainer}>
            <RNView style={styles.sectionHeaderContainer}>
              <RNText style={[styles.sectionTitle, { color: colors.text }]}>
                {activeCategory.name} 商品
              </RNText>
              <TouchableOpacity onPress={() => router.push(`/products/${activeCategory.slug}`)}>
                <RNText style={[styles.viewAllText, { color: colors.primary }]}>查看全部 <FontAwesome name="angle-right" size={12} color={colors.primary} /></RNText>
              </TouchableOpacity>
            </RNView>
            
            {loadingProducts ? (
              <RNView style={styles.loadingProductsContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
              </RNView>
            ) : productError ? (
              <RNView style={styles.errorContainer}>
                <RNText style={{ color: colors.error }}>Failed to load products</RNText>
              </RNView>
            ) : products.length === 0 ? (
              <RNView style={styles.emptyStateContainer}>
                <RNText style={{ color: colors.textSecondary }}>No products found in this category</RNText>
              </RNView>
            ) : (
              <FlatList
                data={products.slice(0, 6)} // Show only first 6 products as a preview
                renderItem={({ item }) => <RenderProductItem item={item} />}
                keyExtractor={item => item.id}
                numColumns={3}
                scrollEnabled={false}
                columnWrapperStyle={styles.productRow}
              />
            )}
          </RNView>
        </ScrollView>
      );
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <RNView style={[styles.searchBarContainer, { backgroundColor: colors.background }]}>
        <FontAwesome name="search" size={18} color={colors.textTertiary} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, { color: colors.text, backgroundColor: colors.backgroundSecondary }]}
          placeholder="搜索商品"
          placeholderTextColor={colors.textTertiary}
          value={searchText}
          onChangeText={setSearchText}
        />
      </RNView>

      <RNView style={styles.contentContainer}>
        {/* Left Sidebar: Main Categories */}
        <RNView style={[styles.leftSidebar, { backgroundColor: colors.background }]}>
          {loading ? (
            <RNView style={styles.loadingContainer}>
              <RNText style={{ color: colors.textSecondary }}>Loading...</RNText>
            </RNView>
          ) : error ? (
            <RNView style={styles.errorContainer}>
              <RNText style={{ color: colors.error }}>Failed to load categories</RNText>
            </RNView>
          ) : (
            <FlatList
              data={allCategories}
              renderItem={({ item }) => (
                <RenderMainCategoryItem
                  item={item}
                  isActive={item.id === activeCategoryId}
                  onPress={() => handleCategoryPress(item)}
                />
              )}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
            />
          )}
        </RNView>

        {/* Right Content Area */}
        <RNView style={[styles.rightContent, { backgroundColor: colors.backgroundSecondary }]}>
          {renderContent()}
        </RNView>
      </RNView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: PADDING,
  },
  loadingProductsContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    padding: PADDING,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: PADDING,
  },
  emptyStateContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    padding: PADDING,
  },
  safeArea: {
    flex: 1,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: PADDING,
    paddingVertical: PADDING / 2,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border, // Use a fixed color or adapt
  },
  searchIcon: {
    marginRight: PADDING / 2,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    paddingHorizontal: PADDING,
    fontSize: 15,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  leftSidebar: {
    width: LEFT_SIDEBAR_WIDTH,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border, // Use a fixed color or adapt
  },
  mainCategoryItem: {
    paddingVertical: PADDING,
    paddingHorizontal: PADDING / 2,
    alignItems: 'center',
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
  },
  mainCategoryText: {
    fontSize: 13,
    textAlign: 'center',
  },
  rightContent: {
    flex: 1,
  },
  rightContentScrollView: {
    paddingBottom: PADDING,
  },
  bannerImage: {
    width: SCREEN_WIDTH - LEFT_SIDEBAR_WIDTH,
    height: (SCREEN_WIDTH - LEFT_SIDEBAR_WIDTH) * 0.4, // Adjust aspect ratio as needed
    resizeMode: 'cover',
  },
  sectionContainer: {
    paddingHorizontal: PADDING,
    marginTop: PADDING,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: PADDING / 1.5,
  },
  hotBrandsScroll: {
    paddingBottom: PADDING / 2, 
  },
  brandItemContainer: {
    alignItems: 'center',
    marginRight: PADDING,
    width: 70, 
  },
  brandImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: PADDING / 3,
  },
  brandName: {
    fontSize: 12,
    textAlign: 'center',
  },
  productRow: {
    justifyContent: 'space-between',
    marginBottom: PADDING / 2,
  },
  productItemContainer: {
    width: (SCREEN_WIDTH - LEFT_SIDEBAR_WIDTH - PADDING * 2 - (PADDING/2 * 2)) / 3, // 3 items per row with spacing
    alignItems: 'center',
    marginBottom: PADDING / 2,
  },
  productImage: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 8,
    marginBottom: PADDING / 3,
  },
  productName: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
    paddingHorizontal: 5,
  },
  productPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 3,
    textAlign: 'center',
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: PADDING / 1.5,
  },
  viewAllText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

// ... (rest of the code remains the same)
