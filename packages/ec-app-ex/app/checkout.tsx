import React, { useState, useEffect, useMemo } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, useColorScheme, ActivityIndicator, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { supabase, getProductById, getUserProfile } from '@/lib/supabase';
import { Colors } from '@ec-nx/shared-logic';
import StripePayment from '@/components/StripePayment';
import BankCardManager from '@/components/BankCardManager';
import { createOrder } from '@/lib/stripe';
import * as Crypto from 'expo-crypto';
import * as supabaseClient from '@/lib/supabase';


const paymentMethods = [
  { id: 'card', name: '银行卡支付', icon: 'credit-card', iconLibrary: FontAwesome, color: '#635BFF' }, // Card payment
  { id: 'alipay', name: '支付宝', icon: 'check-circle', iconLibrary: FontAwesome, color: '#00A0E9' }, // Alipay blue
  { id: 'wechatpay', name: '微信支付', icon: 'wechat', iconLibrary: FontAwesome, color: '#07C160' }, // WeChat green
];

const invoiceInfo = '电子发票 | 个人';
const deliveryTime = '今日 16:00-18:00'; // This is a placeholder





export default function CheckoutScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { selectedItems: selectedItemsString } = useLocalSearchParams();
  
  const [address, setAddress] = useState<any>(null);
  const [orderItems, setOrderItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [orderId, setOrderId] = useState('');
  const [selectedCardId, setSelectedCardId] = useState<string>();  // For storing selected card ID
  const themeColors = Colors[colorScheme ?? 'light'];

  const selectedItems = useMemo(() => {
    if (!selectedItemsString) {
      return [];
    }
    try {
      const parsedItems = JSON.parse(selectedItemsString as string);
      console.log('解析后的 selectedItems:', JSON.stringify(parsedItems, null, 2));
      
      // 检查第一个商品的ID格式
      if (parsedItems.length > 0) {
        const firstItem = parsedItems[0];
        console.log('第一个商品的ID信息:', {
          id: firstItem.id,
          type: typeof firstItem.id,
          product_id: firstItem.product_id,
          products_id: firstItem.products?.id,
          allKeys: Object.keys(firstItem)
        });
      }
      
      return parsedItems;
    } catch (e) {
      console.error("Failed to parse selectedItems from params:", e, "Raw string:", selectedItemsString);
      return [];
    }
  }, [selectedItemsString]);

  useEffect(() => {
    const processData = async () => {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { profile } = await getUserProfile(user.id);
          setAddress(profile);
        }

        if (selectedItems && selectedItems.length > 0) {
      console.log('Selected items raw data:', JSON.stringify(selectedItems, null, 2));
      
      // Debug the first item to understand its structure
      if (selectedItems.length > 0) {
        const firstItem = selectedItems[0];
        console.log('First selected item structure:', {
          id: firstItem.id,
          product_id: firstItem.product_id,
          products: firstItem.products,
          keys: Object.keys(firstItem)
        });
      }
      
      const storesData = selectedItems.reduce((acc: any[], item: any) => {
        const storeId = item.category?.id || 'default';
        let store = acc.find((s: any) => s.id === storeId);
        if (!store) {
          store = {
            id: storeId,
            storeName: item.category?.name || 'Default Store',
            storeIcon: 'shopping-bag',
            items: [],
            deliveryFee: 8, // Placeholder
            discount: null, // Placeholder
          };
          acc.push(store);
        }
        // The item from the cart now contains all necessary info.
        store.items.push(item);
        return acc;
      }, []);
      setOrderItems(storesData);
        }
      } catch (error) {
        console.error('Failed to process checkout data:', error);
      } finally {
        setLoading(false);
      }
    };

    processData();
  }, [selectedItems]);

  const orderSummary = useMemo(() => {
    return orderItems.reduce(
      (acc, store) => {
        const storeSubtotal = store.items.reduce((sub: number, item: any) => sub + (item.price * item.quantity), 0);
        acc.productAmount += storeSubtotal;
        acc.shippingFee += store.deliveryFee;
        if (store.discount) {
          acc.discountAmount += store.discount.amount;
        }
        return acc;
      },
      { productAmount: 0, discountAmount: 0, shippingFee: 0 }
    );
  }, [orderItems]);

  const totalPayable = orderSummary.productAmount - orderSummary.discountAmount + orderSummary.shippingFee;

  const generateOrderId = async () => {
    const timestamp = new Date().getTime();
    const randomBytes = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      `order-${timestamp}-${Math.random()}`
    );
    return `ORD-${timestamp.toString().slice(-8)}-${randomBytes.slice(0, 8)}`;
  };

  const handlePaymentSuccess = async (paymentIntentId: string) => {
    try {
      setIsProcessingPayment(true);
      console.log('Payment successful with paymentIntentId:', paymentIntentId);
      
      // Generate a unique order ID if not already generated
      const newOrderId = orderId || await generateOrderId();
      console.log('Using order ID:', newOrderId);
      if (!orderId) setOrderId(newOrderId);
      
      // Get user data
      const { user: userData } = await supabaseClient.getUser();
      console.log('Creating order for user:', userData?.id || 'anonymous user');
      
      // Format shipping address as JSONB for Supabase
      const shippingAddressJson = address ? {
        name: address.full_name || '',
        phone: address.phone || '',
        address_line1: address.address || '',
        address_line2: address.address_detail || '',
        city: address.city || '',
        state: address.province || '',
        postal_code: address.postal_code || '',
        country: 'CN'
      } : null;
      
      // Create the order data
      const orderData = {
        userId: userData?.id,
        items: orderItems.flatMap((store, storeIndex) => store.items.map((item: any, itemIndex: number) => {
          // Log the complete item structure to understand what we're working with
          console.log('Complete item structure:', JSON.stringify(item, null, 2));
          
          // Extract product ID using a more comprehensive approach
          let productId;
          
          // For items directly from products table
          // if (item.id && typeof item.id === 'string' && item.id.includes('-')) {
          //   productId = item.id;
          // }
          // // For items from cart with joined products
          // else
          if (item.productId) {
            productId = item.productId;
          }
          // For items with nested products object
          else if (item.products && item.products.id) {
            productId = item.products.id;
          }
          
          console.log('Item mapping:', {
            itemId: item.id,
            extractedProductId: productId,
            name: item.name || (item.products && item.products.name)
          });
          
          return {
            productId: productId,
            quantity: item.quantity || 1,
            price: item.price || (item.products && item.products.price) || 0,
            name: item.name || (item.products && item.products.name) || 'Unknown Product',
            image: item.image_url || item.image || (item.products && item.products.image_url),
            key: `${storeIndex}-${itemIndex}` // 添加唯一的key属性
          };
        })),
        paymentDetails: {
          method: selectedPaymentMethod,
          paymentIntentId,
          cardId: selectedCardId, // Include the selected card ID if using card payment
          amount: totalPayable,
          currency: 'CNY',
          status: 'completed'
        },
        shippingAddress: shippingAddressJson,
        orderSummary: {
          productAmount: orderSummary.productAmount,
          discountAmount: orderSummary.discountAmount,
          shippingFee: orderSummary.shippingFee,
          totalAmount: totalPayable
        }
      };
      
      console.log('Saving order to Supabase...');
      try {
        // Save the order to the database
        const result = await createOrder(orderData);
        console.log('Order created successfully:', result);
        
        // Navigate to the success page with the new order ID from the database
        console.log('Navigating to order success page...');
        router.replace({
          pathname: '/order-success',
          params: { 
            orderId: result.orderId,
            orderNumber: result.orderNumber
          }
        });
      } catch (orderError) {
        console.error('Error creating order in database:', orderError);
        Alert.alert(
          '订单处理错误', 
          '支付已成功，但保存订单时出现问题。请联系客服并提供支付ID: ' + paymentIntentId
        );
        setIsProcessingPayment(false);
      }
    } catch (error) {
      console.error('Unexpected error in payment success handler:', error);
      Alert.alert('错误', '处理订单时出现问题，请重试。');
      setIsProcessingPayment(false);
    }
  };

  const handlePaymentError = (error: Error) => {
    console.error('Payment error:', error);
    Alert.alert('Payment Failed', error.message || 'There was a problem processing your payment. Please try again.');
    setIsProcessingPayment(false);
  };

  const handleOtherPaymentMethods = () => {
    Alert.alert(
      '支付方式',
      '目前只支持银行卡支付方式，请选择银行卡支付',
      [{ text: '确定', onPress: () => setSelectedPaymentMethod('card') }]
    );
  };

  const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: themeColors.background },
    loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    scrollView: { flex: 1 },
    contentContainer: { paddingBottom: 150 },
    section: { backgroundColor: themeColors.card, borderRadius: 8, marginHorizontal: 15, marginTop: 10, padding: 15 },
    header: { flexDirection: 'row', alignItems: 'center', padding: 15, borderBottomWidth: 1, borderBottomColor: themeColors.border, backgroundColor: themeColors.card },
    headerTitle: { flex: 1, textAlign: 'center', fontSize: 18, fontWeight: 'bold', color: themeColors.text },
    addressContainer: { flexDirection: 'row', alignItems: 'center' },
    addressIcon: { marginRight: 10 },
    addressTextContainer: { flex: 1 },
    addressNamePhone: { fontSize: 16, fontWeight: 'bold', color: themeColors.text },
    addressDetail: { fontSize: 14, color: themeColors.textMuted, marginTop: 4 },
    arrowIcon: { marginLeft: 10 },
    deliveryTimeContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    deliveryTimeLabel: { fontSize: 14, color: themeColors.text },
    deliveryTimeValueContainer: { flexDirection: 'row', alignItems: 'center' },
    deliveryTimeValue: { fontSize: 14, color: themeColors.text, marginRight: 5 },
    storeHeader: { flexDirection: 'row', alignItems: 'center', paddingBottom: 10, borderBottomWidth: 1, borderBottomColor: themeColors.border },
    storeIcon: { marginRight: 8 },
    storeName: { fontSize: 16, fontWeight: 'bold', color: themeColors.text },
    productItem: { flexDirection: 'row', marginTop: 15 },
    productImage: { width: 80, height: 80, borderRadius: 6, backgroundColor: themeColors.imageBackground, marginRight: 10 },
    productInfo: { flex: 1 },
    productName: { fontSize: 14, fontWeight: '500', color: themeColors.text },
    productSpec: { fontSize: 12, color: themeColors.textMuted, marginTop: 4 },
    cardManagerContainer: { marginTop: 15, paddingTop: 15, borderTopWidth: 1, borderTopColor: themeColors.border },
    productPriceAndQuantity: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end', marginTop: 8 },
    productPrice: { fontSize: 16, fontWeight: 'bold', color: themeColors.error },
    productQuantity: { fontSize: 14, color: themeColors.textMuted },
    storeFooter: { marginTop: 15, paddingTop: 10, borderTopWidth: 1, borderTopColor: themeColors.border },
    storeFooterInfo: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5 },
    storeFooterText: { fontSize: 14, color: themeColors.text },
    storeFooterValue: { fontSize: 14, color: themeColors.text, fontWeight: '500' },
    discountText: { color: themeColors.error },
    paymentMethodTitle: { fontSize: 16, fontWeight: 'bold', color: themeColors.text, marginBottom: 10 },
    paymentOption: { flexDirection: 'row', alignItems: 'center', paddingVertical: 15, borderBottomWidth: 1, borderBottomColor: themeColors.border },
    paymentOptionLast: { borderBottomWidth: 0 },
    paymentIcon: { marginRight: 15 },
    paymentName: { flex: 1, fontSize: 16, color: themeColors.text },
    invoiceInfoContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    invoiceTextLabel: { fontSize: 14, color: themeColors.text },
    invoiceTextValueContainer: { flexDirection: 'row', alignItems: 'center' },
    invoiceTextValue: { fontSize: 14, color: themeColors.text, marginRight: 5 },
    summaryFooterContainer: { position: 'absolute', bottom: 60, left: 0, right: 0, backgroundColor: themeColors.card, padding: 15, borderTopWidth: 1, borderTopColor: themeColors.border },
    summaryRow: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
    summaryLabel: { fontSize: 14, color: themeColors.text },
    summaryValue: { fontSize: 14, color: themeColors.text, fontWeight: '500' },
    summaryDiscount: { color: themeColors.error },
    bottomBar: { position: 'absolute', bottom: 0, left: 0, right: 0, height: 60, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 15, backgroundColor: themeColors.card, borderTopWidth: 1, borderTopColor: themeColors.border },
    totalPayableText: { fontSize: 14, color: themeColors.text },
    totalPayableAmount: { fontSize: 18, fontWeight: 'bold', color: themeColors.error },
    payButton: {
      backgroundColor: themeColors.primary,
      paddingHorizontal: 20,
      paddingVertical: 10,
      borderRadius: 5,
    },
    payButtonDisabled: {
      backgroundColor: themeColors.textMuted,
    },
    payButtonText: { color: themeColors.buttonText, fontSize: 16, fontWeight: 'bold' },
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={themeColors.primary} />
        <Text style={{ color: themeColors.text, marginTop: 10 }}>Loading Checkout...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <FontAwesome name="chevron-left" size={18} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>确认订单</Text>
        <View style={{ width: 18 }} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <TouchableOpacity style={styles.section}>
          <View style={styles.addressContainer}>
            <FontAwesome name="map-marker" size={20} color={themeColors.primary} style={styles.addressIcon} />
            <View style={styles.addressTextContainer}>
              {address ? (
                <>
                  <Text style={styles.addressNamePhone}>{address.full_name || 'N/A'} {address.phone || ''}</Text>
                  <Text style={styles.addressDetail}>{address.address || 'No address provided. Please update your profile.'}</Text>
                </>
              ) : (
                <Text style={styles.addressDetail}>No address found.</Text>
              )}
            </View>
            <FontAwesome name="chevron-right" size={16} color={themeColors.textMuted} style={styles.arrowIcon} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.section}>
          <View style={styles.deliveryTimeContainer}>
            <Text style={styles.deliveryTimeLabel}>送达时间</Text>
            <View style={styles.deliveryTimeValueContainer}>
              <Text style={styles.deliveryTimeValue}>{deliveryTime}</Text>
              <FontAwesome name="chevron-right" size={16} color={themeColors.textMuted} style={styles.arrowIcon} />
            </View>
          </View>
        </TouchableOpacity>

        {orderItems.map((storeOrder, index) => (
          <View key={`store-${index}`} style={styles.section}>
            <View style={styles.storeHeader}>
              <FontAwesome name={storeOrder.storeIcon as any} size={18} color={themeColors.text} style={styles.storeIcon} />
              <Text style={styles.storeName}>{storeOrder.storeName}</Text>
            </View>
            {storeOrder.items.map((item: any, itemIndex: number) => (
              <View key={`item-${itemIndex}`} style={styles.productItem}>
                <Image source={{ uri: item.image }} style={styles.productImage} />
                <View style={styles.productInfo}>
                  <Text style={styles.productName} numberOfLines={2}>{item.name}</Text>
                  <Text style={styles.productSpec}>{item.specs}</Text>
                  <View style={styles.productPriceAndQuantity}>
                    <Text style={styles.productPrice}>¥{item.price.toFixed(2)}</Text>
                    <Text style={styles.productQuantity}>x{item.quantity}</Text>
                  </View>
                </View>
              </View>
            ))}
            <View style={styles.storeFooter}>
              <View style={styles.storeFooterInfo}>
                <Text style={styles.storeFooterText}>配送费</Text>
                <Text style={styles.storeFooterValue}>¥{storeOrder.deliveryFee.toFixed(2)}</Text>
              </View>
              {storeOrder.discount && (
                <View style={styles.storeFooterInfo}>
                  <Text style={[styles.storeFooterText, {color: themeColors.error}]}>{storeOrder.discount.description}</Text>
                  <Text style={[styles.storeFooterValue, styles.discountText]}>-¥{storeOrder.discount.amount.toFixed(2)}</Text>
                </View>
              )}
            </View>
          </View>
        ))}

        <View style={styles.section}>
          <Text style={styles.paymentMethodTitle}>支付方式</Text>
          {paymentMethods.map((method, index) => (
            <TouchableOpacity 
              key={method.id} 
              style={[styles.paymentOption, index === paymentMethods.length - 1 ? styles.paymentOptionLast : {}]}
              onPress={() => setSelectedPaymentMethod(method.id)}
            >
              <method.iconLibrary name={method.icon as any} size={20} color={selectedPaymentMethod === method.id ? themeColors.primary : method.color} style={styles.paymentIcon} />
              <Text style={styles.paymentName}>{method.name}</Text>
              {selectedPaymentMethod === method.id && (
                <FontAwesome key={`check-${method.id}`} name="check-circle" size={20} color={themeColors.primary} />
              )}
            </TouchableOpacity>
          ))}
          
          {/* Show bank card manager when card payment method is selected */}
          {selectedPaymentMethod === 'card' && (
            <View style={styles.cardManagerContainer}>
              <BankCardManager 
                onCardSelect={(cardId) => setSelectedCardId(cardId)}
                selectedCardId={selectedCardId}
              />
            </View>
          )}
        </View>

        <TouchableOpacity style={styles.section}>
          <View style={styles.invoiceInfoContainer}>
            <Text style={styles.invoiceTextLabel}>发票信息</Text>
            <View style={styles.invoiceTextValueContainer}>
              <Text style={styles.invoiceTextValue}>{invoiceInfo}</Text>
              <FontAwesome name="chevron-right" size={16} color={themeColors.textMuted} style={styles.arrowIcon} />
            </View>
          </View>
        </TouchableOpacity>
      </ScrollView>

      <View style={styles.summaryFooterContainer}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>商品金额</Text>
            <Text style={styles.summaryValue}>¥{orderSummary.productAmount.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>优惠金额</Text>
            <Text style={[styles.summaryValue, styles.summaryDiscount]}>-¥{orderSummary.discountAmount.toFixed(2)}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>运费</Text>
            <Text style={styles.summaryValue}>¥{orderSummary.shippingFee.toFixed(2)}</Text>
          </View>
      </View>

      <View style={styles.bottomBar}>
        <Text style={styles.totalPayableText}>
          实付款: <Text style={styles.totalPayableAmount}>¥{totalPayable.toFixed(2)}</Text>
        </Text>
        {selectedPaymentMethod === 'card' ? (
          <StripePayment
            amount={totalPayable}
            onPaymentSuccess={(paymentIntentId) => handlePaymentSuccess(paymentIntentId)}
            onPaymentError={(error) => handlePaymentError(error)}
            selectedCardId={selectedCardId}
          >
            <TouchableOpacity 
              style={[styles.payButton, isProcessingPayment && styles.payButtonDisabled]} 
              disabled={isProcessingPayment}
            >
              <Text style={styles.payButtonText}>{isProcessingPayment ? '处理中...' : '立即支付'}</Text>
            </TouchableOpacity>
          </StripePayment>
        ) : (
          <TouchableOpacity 
            style={styles.payButton}
            onPress={handleOtherPaymentMethods}
          >
            <Text style={styles.payButtonText}>立即支付</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
