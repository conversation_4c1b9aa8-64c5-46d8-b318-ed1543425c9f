import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  useColorScheme,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { getProductsByCategory, getCategories } from '@/lib/supabase';

// Product type definition
type Product = {
  id: string;
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  image_url: string;
  category_id: string;
  created_at: string;
  updated_at: string;
  stock_quantity?: number;
  discount_percent?: number;
  rating?: number;
  sales_count?: number;
  specs?: string;
  categories?: {
    id: string;
    name: string;
    slug: string;
  };
  // For UI display
  tag?: {
    text: string;
    color: string;
  };
};

// Category type definition
type Category = {
  id: string;
  name: string;
  description?: string;
  slug: string;
};

// Map to store category names by slug
const categoryTitles: { [key: string]: string } = {};

const sortOptions = [
  { id: 'comprehensive', label: '综合', icon: null },
  { id: 'sales', label: '销量', icon: 'sort' as const }, // 'sort', 'sort-amount-asc', 'sort-amount-desc'
  { id: 'price', label: '价格', icon: 'sort' as const },
];

export default function ProductListScreen() {
  const router = useRouter();
  const { categorySlug } = useLocalSearchParams<{ categorySlug: string }>();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activeSort, setActiveSort] = useState('comprehensive');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [pageTitle, setPageTitle] = useState('产品列表');
  const [totalCount, setTotalCount] = useState(0);
  const [offset, setOffset] = useState(0);
  const limit = 10;

  // Fetch category name when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { categories } = await getCategories();
        if (categories) {
          const categoryMap: { [key: string]: string } = {};
          categories.forEach((category: Category) => {
            categoryMap[category.slug] = category.name;
          });
          Object.assign(categoryTitles, categoryMap);
          
          if (categorySlug && categoryMap[categorySlug]) {
            setPageTitle(categoryMap[categorySlug]);
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    fetchCategories();
  }, []);

  // Fetch products when category slug or sort options change
  useEffect(() => {
    if (categorySlug) {
      fetchProducts(0);
    }
  }, [categorySlug, activeSort, sortDirection]);
  
  // Function to fetch products with pagination
  const fetchProducts = async (newOffset: number) => {
    if (newOffset === 0) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }
    
    try {
      // Map UI sort options to database fields
      let sortBy = 'created_at';
      if (activeSort === 'sales') sortBy = 'sales_count';
      if (activeSort === 'price') sortBy = 'price';
      
      const result = await getProductsByCategory(
        categorySlug as string,
        limit,
        newOffset
      );
      
      const { products, error } = result;
      
      if (error) throw error;
      
      // Process products to add UI-specific properties
      const processedProducts = (products || []).map((product: Product) => {
        // Add tags based on product properties
        let tag = undefined;
        
        if (product.discount_percent && product.discount_percent > 20) {
          tag = { text: '优惠', color: '#34C759' };
        } else if (product.sales_count && product.sales_count > 1000) {
          tag = { text: '热销', color: '#FF3B30' };
        } else if (new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
          tag = { text: '新品', color: '#FF9500' };
        }
        
        return {
          ...product,
          tag
        };
      });
      
      if (newOffset === 0) {
        setProducts(processedProducts);
      } else {
        setProducts(prev => [...prev, ...processedProducts]);
      }
      
      // Set total count if available
      if ('count' in result && result.count !== null) {
        setTotalCount(result.count);
      }
      
      setOffset(newOffset);
    } catch (err) {
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleSort = (sortId: string) => {
    if (activeSort === sortId && (sortId === 'sales' || sortId === 'price')) {
      setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
    } else if (sortId === 'sales' || sortId === 'price') {
      setSortDirection('desc'); // Default to desc for new sort field
    } else {
      // For comprehensive sort, use created_at desc as default
      setSortDirection('desc');
    }
    setActiveSort(sortId);
    // The actual sorting will be handled in the fetchProducts function
  };
  
  // Load more products when reaching the end of the list
  const handleLoadMore = () => {
    if (!loadingMore && products.length < totalCount) {
      fetchProducts(offset + limit);
    }
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <TouchableOpacity style={styles.productItem} onPress={() => router.push(`/product-details/${item.id}`)}>
      {item.tag && (
        <View style={[styles.tag, { backgroundColor: item.tag.color }]}>
          <Text style={styles.tagText}>{item.tag.text}</Text>
        </View>
      )}
      <Image source={{ uri: item.image_url || 'https://via.placeholder.com/150x150.png?text=No+Image' }} style={styles.productImage} />
      <View style={styles.productContent}>
        <Text style={styles.productName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.productSpecs} numberOfLines={1}>{item.specs}</Text>
        <View style={styles.priceContainer}>
          <Text style={styles.currentPrice}>¥{item.price.toFixed(2)}</Text>
          {item.original_price && item.original_price > item.price && (
            <Text style={styles.originalPrice}>¥{item.original_price.toFixed(2)}</Text>
          )}
        </View>
        <View style={styles.salesRatingContainer}>
          <Text style={styles.salesText}>已售{item.sales_count ? `${item.sales_count}件` : '0件'}</Text>
          <View style={styles.ratingContainer}>
            <FontAwesome name="star" size={12} color="#FFD700" />
            <Text style={styles.ratingText}>{item.rating || '5.0'}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const loadMoreProducts = () => {
    if (loadingMore) return;
    setLoadingMore(true);
    // Simulate loading more products
    setTimeout(() => {
      // const newProducts = ... fetch more products
      // setProducts(prev => [...prev, ...newProducts]);
      setLoadingMore(false);
    }, 1500);
  };

  const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: themeColors.background },
    container: { flex: 1 },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: themeColors.background,
      borderBottomWidth: 1,
      borderBottomColor: themeColors.border,
    },
    headerIcon: {
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      flex: 1,
      fontSize: 18,
      fontWeight: 'bold',
      color: themeColors.text,
      textAlign: 'center',
    },
    filterBar: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 10,
      backgroundColor: themeColors.background,
      borderBottomWidth: 1,
      borderBottomColor: themeColors.border,
    },
    sortOption: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 20,
    },
    sortLabel: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    activeSortLabel: {
      color: themeColors.primary,
      fontWeight: '500',
    },
    sortIcon: {
      marginLeft: 4,
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 'auto',
    },
    productList: {
      padding: 8,
    },
    productItem: {
      backgroundColor: themeColors.card,
      borderRadius: 10,
      marginBottom: 10,
      flexDirection: 'row',
      padding: 10,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    productImage: {
      width: 100,
      height: 100,
      borderRadius: 8,
      marginRight: 10,
      backgroundColor: themeColors.imageBackground,
    },
    productContent: {
      flex: 1,
      justifyContent: 'space-between',
    },
    productName: { fontSize: 16, fontWeight: '600', color: themeColors.text, marginBottom: 4 },
    productSpecs: { fontSize: 12, color: themeColors.textTertiary, marginBottom: 6 },
    priceContainer: { flexDirection: 'row', alignItems: 'flex-end', marginBottom: 6 },
    currentPrice: { fontSize: 18, fontWeight: 'bold', color: themeColors.error, marginRight: 8 },
    originalPrice: { fontSize: 12, color: themeColors.textTertiary, textDecorationLine: 'line-through' },
    salesRatingContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    salesText: { fontSize: 12, color: themeColors.textSecondary },
    ratingContainer: { flexDirection: 'row', alignItems: 'center' },
    ratingText: { fontSize: 12, color: themeColors.textSecondary, marginLeft: 3 },
    tag: {
      position: 'absolute',
      top: 0,
      left: 0,
      paddingHorizontal: 6,
      paddingVertical: 3,
      borderTopLeftRadius: 8,
      borderBottomRightRadius: 8,
      zIndex: 1,
    },
    tagText: { color: '#FFFFFF', fontSize: 10, fontWeight: 'bold' },
    footerLoading: { paddingVertical: 20, alignItems: 'center' },
    loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    emptyContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
    emptyText: { fontSize: 16, color: themeColors.textSecondary },
    loadMoreContainer: { paddingVertical: 20, alignItems: 'center' },
    loadMoreText: { fontSize: 14, color: themeColors.textSecondary }
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      {loading && products.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
        </View>
      ) : (
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={() => router.back()} style={styles.headerIcon}>
              <FontAwesome name="chevron-left" size={18} color={themeColors.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{pageTitle}</Text>
            <TouchableOpacity style={styles.headerIcon}>
              <FontAwesome name="search" size={20} color={themeColors.text} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerIcon}>
              <FontAwesome name="share-alt" size={20} color={themeColors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.filterBar}>
            {sortOptions.map(opt => (
              <TouchableOpacity key={opt.id} style={styles.sortOption} onPress={() => handleSort(opt.id)}>
                <Text style={[styles.sortLabel, activeSort === opt.id && styles.activeSortLabel]}>{opt.label}</Text>
                {opt.icon && (activeSort === opt.id) && (
                  <FontAwesome 
                    name={sortDirection === 'asc' ? 'sort-asc' : (sortDirection === 'desc' ? 'sort-desc' : 'sort')}
                    size={14} 
                    color={activeSort === opt.id ? themeColors.primary : themeColors.textTertiary} 
                    style={styles.sortIcon} 
                  />
                )}
              </TouchableOpacity>
            ))}
            <TouchableOpacity style={styles.filterButton}>
              <Text style={styles.sortLabel}>筛选</Text>
              <FontAwesome name="filter" size={14} color={themeColors.textTertiary} style={styles.sortIcon} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={products}
            renderItem={renderProductItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.productList}
            showsVerticalScrollIndicator={false}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            ListEmptyComponent={loading ? null : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>暂无商品</Text>
              </View>
            )}
            ListFooterComponent={
              products.length > 0 ? (
                <View style={styles.loadMoreContainer}>
                  {loadingMore ? (
                    <ActivityIndicator size="small" color="#999" />
                  ) : products.length >= totalCount ? (
                    <Text style={styles.loadMoreText}>没有更多商品了</Text>
                  ) : (
                    <Text style={styles.loadMoreText}>上拉加载更多</Text>
                  )}
                </View>
              ) : null
            }
          />
        </View>
      )}
    </SafeAreaView>
  );
}
