import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, useColorScheme, SafeAreaView } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic';

const features = [
  {
    icon: 'shopping-basket' as const, // Placeholder, closest to storefront
    title: '货架电商',
    subtitle: '精选商品，一应俱全',
  },
  {
    icon: 'heart' as const,
    title: '兴趣电商',
    subtitle: '个性推荐，懂你所爱',
  },
  {
    icon: 'bolt' as const,
    title: '即时电商',
    subtitle: '闪电配送，即刻送达',
  },
];

export default function WelcomeScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: themeColors.backgroundSoft, // Light gray background
    },
    container: {
      flex: 1,
      paddingHorizontal: 30,
      paddingTop: 60, // More space at the top
      paddingBottom: 40,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    headerSection: {
      alignItems: 'center',
      marginTop: 20,
    },
    logoContainer: {
      width: 100,
      height: 100,
      borderRadius: 25,
      backgroundColor: themeColors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      shadowColor: themeColors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 5,
      elevation: 8,
    },
    logoIcon: {
      // The icon provided is a shopping bag, FontAwesome's 'shopping-bag' is suitable
    },
    appTitle: {
      fontSize: 32,
      fontWeight: 'bold',
      color: themeColors.text,
      marginBottom: 8,
    },
    appSubtitle: {
      fontSize: 16,
      color: themeColors.textSecondary,
      marginBottom: 40,
    },
    featuresSection: {
      width: '100%',
      marginBottom: 30,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: themeColors.card, // White card for features
      borderRadius: 15,
      padding: 20,
      marginBottom: 15,
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 5,
      elevation: 3,
    },
    featureIconContainer: {
      width: 50,
      height: 50,
      borderRadius: 12,
      backgroundColor: themeColors.primaryTransparent, // Light blue background for icon
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
    },
    featureTextContainer: {
      flex: 1,
    },
    featureTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: themeColors.text,
      marginBottom: 4,
    },
    featureSubtitle: {
      fontSize: 14,
      color: themeColors.textTertiary,
    },
    buttonsSection: {
      width: '100%',
    },
    primaryButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 18,
      borderRadius: 12,
      alignItems: 'center',
      marginBottom: 15,
      shadowColor: themeColors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 5,
      elevation: 8,
    },
    primaryButtonText: {
      color: themeColors.buttonText, // White text
      fontSize: 18,
      fontWeight: 'bold',
    },
    secondaryButton: {
      backgroundColor: themeColors.card, // White background
      paddingVertical: 18,
      borderRadius: 12,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: themeColors.primary, // Blue border
    },
    secondaryButtonText: {
      color: themeColors.primary, // Blue text
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.headerSection}>
          <View style={styles.logoContainer}>
            <FontAwesome name="shopping-bag" size={48} color={themeColors.buttonText} style={styles.logoIcon} />
          </View>
          <Text style={styles.appTitle}>EC商城</Text>
          <Text style={styles.appSubtitle}>一站式购物体验</Text>
        </View>

        <View style={styles.featuresSection}>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <FontAwesome name={feature.icon} size={24} color={themeColors.primary} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureSubtitle}>{feature.subtitle}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.buttonsSection}>
          <TouchableOpacity style={styles.primaryButton} onPress={() => router.replace('/(tabs)')}>
            <Text style={styles.primaryButtonText}>开始体验</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton} onPress={() => router.push('/login')}>
            <Text style={styles.secondaryButtonText}>已有账号? 登录</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
