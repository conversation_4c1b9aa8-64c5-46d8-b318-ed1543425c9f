import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TextInput, TouchableOpacity, Switch, useColorScheme, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { useAuth } from '@/hooks/useAuth';

export default function LoginScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];
  const { login, loginWithGoogle, user, loading: authLoading, error: authError } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.replace('/(tabs)/profile');
    }
  }, [user]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: themeColors.background,
    },
    scrollViewContent: {
        flexGrow: 1,
        justifyContent: 'center',
        paddingHorizontal: 25,
        paddingBottom: 30,
    },
    backButton: {
      position: 'absolute',
      top: 50, // Adjust as per your status bar height and design
      left: 20,
      zIndex: 1,
      padding: 10,
    },
    headerContainer: {
      alignItems: 'flex-start',
      marginBottom: 40,
      marginTop: 80, // Space for back button
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: themeColors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: themeColors.textSecondary,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: themeColors.backgroundSecondary, // Or a very light grey for input fields
      borderRadius: 10,
      paddingHorizontal: 15,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: themeColors.border,
    },
    inputIcon: {
      marginRight: 10,
    },
    input: {
      flex: 1,
      height: 50,
      fontSize: 16,
      color: themeColors.text,
    },
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 30,
    },
    rememberMeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rememberMeText: {
      marginLeft: 8,
      fontSize: 14,
      color: themeColors.text,
    },
    forgotPasswordText: {
      fontSize: 14,
      color: themeColors.primary,
      fontWeight: '500',
    },
    loginButton: {
      backgroundColor: themeColors.primary,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
      marginBottom: 30,
    },
    loginButtonText: {
      color: themeColors.buttonText, // Assuming buttonText is white or a contrasting color
      fontSize: 18,
      fontWeight: 'bold',
    },
    orSeparatorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 30,
    },
    separatorLine: {
      flex: 1,
      height: 1,
      backgroundColor: themeColors.border,
    },
    orText: {
      marginHorizontal: 15,
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    socialLoginContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: 40,
    },
    socialIconTouchable: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: themeColors.card, // Or a specific color for each social icon bg
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: themeColors.border, // Subtle border for icons
    },
    socialIcon: {
        // Specific styles if needed, color will be passed directly
    },
    signUpContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    signUpText: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    signUpLink: {
      fontSize: 14,
      color: themeColors.primary,
      fontWeight: 'bold',
      marginLeft: 5,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <FontAwesome name="chevron-left" size={22} color={themeColors.text} />
      </TouchableOpacity>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>欢迎回来</Text>
          <Text style={styles.subtitle}>请登录您的账号继续</Text>
        </View>

        <View style={styles.inputContainer}>
          <FontAwesome name="envelope" size={20} color={themeColors.textTertiary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder="邮箱地址"
            placeholderTextColor={themeColors.textTertiary}
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
          />
        </View>

        <View style={styles.inputContainer}>
          <FontAwesome name="lock" size={22} color={themeColors.textTertiary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder="密码"
            placeholderTextColor={themeColors.textTertiary}
            secureTextEntry={!isPasswordVisible}
            value={password}
            onChangeText={setPassword}
          />
          <TouchableOpacity onPress={() => setIsPasswordVisible(!isPasswordVisible)} style={{ padding: 5 }}>
            <FontAwesome name={isPasswordVisible ? 'eye' : 'eye-slash'} size={20} color={themeColors.textTertiary} />
          </TouchableOpacity>
        </View>

        <View style={styles.optionsContainer}>
          <TouchableOpacity style={styles.rememberMeContainer} onPress={() => setRememberMe(!rememberMe)}>
            <FontAwesome name={rememberMe ? 'check-square-o' : 'square-o'} size={20} color={themeColors.primary} />
            <Text style={styles.rememberMeText}>记住我</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => router.push('/forgot-password')}>
            <Text style={styles.forgotPasswordText}>忘记密码?</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity 
          style={[styles.loginButton, (isLoading || authLoading) && { opacity: 0.7 }]}
          disabled={isLoading || authLoading}
          onPress={async () => {
            if (!email || !password) {
              Alert.alert('提示', '请输入邮箱和密码');
              return;
            }
            
            setIsLoading(true);
            try {
              const { error } = await login(email, password);
              if (error) {
                Alert.alert('登录失败', error.message);
              }
            } catch (err) {
              Alert.alert('登录失败', '请检查您的邮箱和密码');
            } finally {
              setIsLoading(false);
            }
          }}
        >
          {(isLoading || authLoading) ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.loginButtonText}>登录</Text>
          )}
        </TouchableOpacity>

        <View style={styles.orSeparatorContainer}>
          <View style={styles.separatorLine} />
          <Text style={styles.orText}>或</Text>
          <View style={styles.separatorLine} />
        </View>

        <View style={styles.socialLoginContainer}>
          <TouchableOpacity style={[styles.socialIconTouchable, {backgroundColor: '#07C160'}]}>
            <FontAwesome name="weixin" size={24} color="#FFFFFF" style={styles.socialIcon} />
          </TouchableOpacity>
          <TouchableOpacity style={[styles.socialIconTouchable, {backgroundColor: '#00A0E9'}]}>
            <FontAwesome name="credit-card" size={24} color="#FFFFFF" style={styles.socialIcon} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.socialIconTouchable, {backgroundColor: '#DB4437'}]}
            onPress={async () => {
              try {
                setIsLoading(true);
                const { success, error } = await loginWithGoogle();
                if (!success && error) {
                  Alert.alert('Google 登录失败', error.message);
                }
              } catch (err) {
                Alert.alert('Google 登录失败', '登录过程中出现错误');
              } finally {
                setIsLoading(false);
              }
            }}
          >
            <FontAwesome name="google" size={24} color="#FFFFFF" style={styles.socialIcon} />
          </TouchableOpacity>
          <TouchableOpacity style={[styles.socialIconTouchable, {backgroundColor: themeColors.text}]}>
            <FontAwesome name="apple" size={24} color={themeColors.background} style={styles.socialIcon} />
          </TouchableOpacity>
        </View>

        <View style={styles.signUpContainer}>
          <Text style={styles.signUpText}>还没有账号?</Text>
          <TouchableOpacity onPress={() => router.push('/signup') /* Assuming a signup route */}>
            <Text style={styles.signUpLink}>立即注册</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
