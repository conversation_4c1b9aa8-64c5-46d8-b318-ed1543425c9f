import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import StripeProviderWrapper from '@/components/StripeProviderWrapper';

export default function TestStripeFixScreen() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>Stripe 修复测试</Text>
      <Text style={styles.platform}>当前平台: {Platform.OS}</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>测试 StripeProviderWrapper:</Text>
        <StripeProviderWrapper>
          <View style={styles.testContent}>
            <Text style={styles.testText}>
              ✅ 如果你看到这个文本，说明 StripeProviderWrapper 工作正常
            </Text>
            <Text style={styles.testText}>
              🌐 Web 平台应该不会加载任何原生 Stripe 模块
            </Text>
            <Text style={styles.testText}>
              📱 原生平台应该正常加载 Stripe 组件
            </Text>
          </View>
        </StripeProviderWrapper>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>预期结果:</Text>
        <Text style={styles.description}>
          • 不应该出现 "codegenNativeCommands" 错误
          {'\n'}• Web 平台控制台应该显示 "Web platform - using children-only implementation"
          {'\n'}• 原生平台控制台应该显示 Stripe 初始化信息
          {'\n'}• 页面应该正常渲染，没有崩溃
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  platform: {
    fontSize: 18,
    textAlign: 'center',
    color: '#007AFF',
    fontWeight: '600',
    marginBottom: 30,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  testContent: {
    padding: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  testText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
});
