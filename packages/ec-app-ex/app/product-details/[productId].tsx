import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  useColorScheme,
  ActivityIndicator,
  SafeAreaView,
  Dimensions,
  FlatList,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { getProductById, getProducts, addToCart, supabase } from '@/lib/supabase';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

// Product type definition
type Product = {
  id: string;
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  image_url: string;
  category_id: string;
  created_at: string;
  updated_at: string;
  stock_quantity?: number;
  discount_percent?: number;
  rating?: number;
  sales_count?: number;
  specs?: string;
  categories?: {
    id: string;
    name: string;
    slug: string;
  };
  // UI display properties
  images?: string[];
  discountTag?: string;
  store?: { name: string; icon: string };
  cartItemCount?: number;
  promotions?: Array<{ id: string; text: string; type: string }>;
  deliveryInfo?: string;
  colors?: Array<{ name: string; value: string; image: string }>;
  versions?: string[];
  parsedSpecs?: Array<{ label: string; value: string }>;
  reviews?: {
    averageRating: number;
    totalReviews: number;
    tags: string[];
    items: Array<{
      id: string;
      userAvatar: string;
      userName: string;
      rating: number;
      text: string;
      date: string;
      images: string[];
    }>;
  };
  recommendations?: Array<{
    id: string;
    name: string;
    price: number;
    imageUrl: string;
  }>;
};

// 定义推荐商品类型
type Recommendation = {
  id: string;
  name: string;
  price: number;
  imageUrl: string;
};

// 定义标签和对应的内容区域
const headerTabs = ['商品', '详情', '评价', '推荐'];

// 创建内容区域的引用
type SectionRefs = {
  [key: string]: React.RefObject<View | null>;
};

export default function ProductDetailScreen() {
  const router = useRouter();
  const { productId } = useLocalSearchParams<{ productId: string }>();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];
  const scrollRef = useRef<ScrollView>(null);
  const insets = useSafeAreaInsets();
  
  // 创建各个内容区域的引用
  const sectionRefs: SectionRefs = {
    '商品': useRef<View>(null),
    '详情': useRef<View>(null),
    '评价': useRef<View>(null),
    '推荐': useRef<View>(null),
  };
  
  // 滚动到指定区域的函数
  const scrollToSection = (sectionName: string) => {
    setActiveHeaderTab(sectionName);
    
    // 使用setTimeout确保布局已完成
    setTimeout(() => {
      if (scrollRef.current && sectionRefs[sectionName]?.current) {
        sectionRefs[sectionName].current?.measureLayout(
          // @ts-ignore - measureLayout在React Native类型中的定义问题
          scrollRef.current,
          (left: number, top: number) => {
            scrollRef.current?.scrollTo({ y: top - 60, animated: true });
          },
          () => console.log('测量失败')
        );
      }
    }, 100);
  };

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
    const [activeHeaderTab, setActiveHeaderTab] = useState(headerTabs[0]);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  useEffect(() => {
    const fetchProductDetails = async () => {
      if (productId) {
        setLoading(true);
        try {
          const { product, error } = await getProductById(productId);
          
          if (error) throw error;
          if (!product) throw new Error('Product not found');
          
          // Process product data for UI display
          const processedProduct: Product = {
            ...product,
            images: product.image_url ? [product.image_url, ...Array(3).fill('https://via.placeholder.com/400x400.png?text=Product+Image')] : [],
            discountTag: product.discount_percent ? `${(100 - product.discount_percent).toFixed(1)}折` : undefined,
            store: { name: product.categories?.name || '官方旗舰店', icon: 'shopping-bag' },
            cartItemCount: 0,
            promotions: [
              { id: 'p1', text: '满1000减100, 满2000减250', type: 'coupon' },
              { id: 'p2', text: '购生产立减50元', type: 'discount' },
            ],
            deliveryInfo: '上海市浦东新区 张江高科技园区',
            colors: [
              { name: '经典黑', value: '#000000', image: 'https://via.placeholder.com/50x50.png?text=Black' },
              { name: '珍珠白', value: '#F0F0F0', image: 'https://via.placeholder.com/50x50.png?text=White' },
              { name: '石墨灰', value: '#808080', image: 'https://via.placeholder.com/50x50.png?text=Gray' },
            ],
            versions: ['标准版', 'Pro版'],
            parsedSpecs: product.specs ? JSON.parse(product.specs) : [
              { label: '品牌', value: product.categories?.name || '未知' },
              { label: '型号', value: product.name },
              { label: '颜色', value: '多色可选' },
            ],
            reviews: {
              averageRating: product.rating || 5.0,
              totalReviews: product.sales_count || 0,
              tags: ['全部(0)', '好评(0)', '中评(0)', '差评(0)', '有图(0)'],
              items: [
                { id: 'r1', userAvatar: 'https://via.placeholder.com/40x40.png?text=U1', userName: '用户', rating: 5, text: '很好的产品！', date: new Date().toISOString().split('T')[0], images: [] },
              ],
            },
          };
          
          // Fetch related products as recommendations
          const { products: relatedProducts } = await getProducts({ limit: 5, offset: 0 });
          if (relatedProducts && relatedProducts.length > 0) {
            processedProduct.recommendations = relatedProducts.map(p => ({
              id: p.id,
              name: p.name,
              price: p.price,
              imageUrl: p.image_url || 'https://via.placeholder.com/150x150.png?text=Product'
            }));
          }
          
          setProduct(processedProduct);
        } catch (err) {
          console.error('Error fetching product details:', err);
        } finally {
          setLoading(false);
        }
      }
    };
    
    fetchProductDetails();
  }, [productId]);

  const handleQuantityChange = (amount: number) => {
    const newQuantity = Math.max(1, quantity + amount);
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    if (isAddingToCart) return;

    setIsAddingToCart(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('请先登录');
        // Optionally, redirect to login screen
        // router.push('/login');
        return;
      }

      if (product?.id) {
        const { error } = await addToCart(user.id, product.id, quantity);
        if (error) {
          throw error;
        }
        alert('已成功添加到购物车');
        // Optionally, update a global cart state or refetch cart count
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      alert('添加到购物车失败，请稍后再试');
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleBuyNow = () => {
    // 这里将来可以添加立即购买功能
    console.log('立即购买:', product?.id, '数量:', quantity);
    // 导航到结算页面
    router.push('/checkout');
  };

  const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: themeColors.background },
    container: { flex: 1 },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 10,
      paddingVertical: 12,
      backgroundColor: themeColors.card,
      borderBottomWidth: 1,
      borderBottomColor: themeColors.border,
    },
    headerTabsContainer: { flex: 1, flexDirection: 'row', justifyContent: 'center' },
    headerTab: { paddingHorizontal: 12, paddingVertical: 5 },
    headerTabText: { fontSize: 16, color: themeColors.textSecondary },
    activeHeaderTabText: { color: themeColors.primary, fontWeight: 'bold', borderBottomWidth: 2, borderBottomColor: themeColors.primary },
    headerIcon: { padding: 5 }, 
    // Image Gallery
    imageGalleryContainer: { marginBottom: 15 },
    mainImage: { width: width, height: width, backgroundColor: themeColors.imageBackground },
    thumbnailContainer: { flexDirection: 'row', paddingVertical: 10, paddingHorizontal: 15, backgroundColor: themeColors.card },
    thumbnail: { width: 60, height: 60, borderRadius: 8, marginRight: 10, borderWidth: 2, borderColor: 'transparent' },
    activeThumbnail: { borderColor: themeColors.primary },
    // Product Info
    infoSection: { paddingHorizontal: 15, paddingVertical: 10, backgroundColor: themeColors.card, marginBottom: 10 },
    priceContainer: { flexDirection: 'row', alignItems: 'flex-end', marginBottom: 5 },
    productPrice: { fontSize: 24, fontWeight: 'bold', color: themeColors.error, marginRight: 8 },
    originalPrice: { fontSize: 14, color: themeColors.textTertiary, textDecorationLine: 'line-through', marginRight: 8 },
    discountTag: { backgroundColor: themeColors.error, color: themeColors.buttonText, paddingHorizontal: 6, paddingVertical: 2, borderRadius: 4, fontSize: 12 },
    productName: { fontSize: 18, fontWeight: '600', color: themeColors.text, marginBottom: 10 },
    // Action Bar
    actionBar: { flexDirection: 'row', alignItems: 'center', paddingVertical: 10, borderTopWidth:1, borderBottomWidth:1, borderColor: themeColors.border, backgroundColor: themeColors.card },
    actionIconContainer: { flex: 1, alignItems: 'center' },
    actionIconText: { fontSize: 10, color: themeColors.textSecondary, marginTop: 2 },
    actionButtonContainer: { flex: 3, flexDirection: 'row' },
    actionButton: { flex: 1, paddingVertical: 12, borderRadius: 20, marginHorizontal: 5, alignItems: 'center', justifyContent: 'center' },
    actionButtonText: { color: themeColors.buttonText, fontSize: 15, fontWeight: 'bold' },
    // Promotions
    promoItem: { flexDirection: 'row', alignItems: 'center', marginBottom: 5 },
    promoTag: { backgroundColor: themeColors.error, color: themeColors.buttonText, fontSize: 10, paddingHorizontal: 4, paddingVertical: 1, borderRadius: 3, marginRight: 8 },
    promoText: { fontSize: 14, color: themeColors.text },
    // Variant Selection
    selectionRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 15, borderBottomWidth: 1, borderColor: themeColors.border },
    selectionLabel: { fontSize: 15, color: themeColors.text, fontWeight: '500' },
    selectionValueContainer: { flexDirection: 'row', alignItems: 'center' },
    selectionValueText: { fontSize: 15, color: themeColors.textSecondary, marginRight: 5 },
    colorSwatchContainer: { flexDirection: 'row' },
    colorSwatch: { width: 24, height: 24, borderRadius: 12, marginRight: 8, borderWidth: 2, borderColor: themeColors.border },
    activeColorSwatch: { borderColor: themeColors.primary },
    versionTag: { paddingHorizontal: 10, paddingVertical: 5, borderRadius: 15, backgroundColor: themeColors.backgroundSoft, marginRight: 8, borderWidth: 1, borderColor: themeColors.border },
    activeVersionTag: { backgroundColor: themeColors.primaryTransparent, borderColor: themeColors.primary },
    versionText: { fontSize: 13, color: themeColors.textSecondary },
    activeVersionText: { color: themeColors.primary }, 
    quantitySelector: { flexDirection: 'row', alignItems: 'center' },
    quantityButton: { paddingHorizontal: 12, paddingVertical: 6, backgroundColor: themeColors.backgroundSoft, borderRadius: 5 }, 
    quantityText: { fontSize: 16, color: themeColors.text, marginHorizontal: 15 }, 
    // Specs
    sectionTitle: { fontSize: 17, fontWeight: 'bold', color: themeColors.text, marginBottom: 10, marginTop: 5 },
    specItem: { flexDirection: 'row', marginBottom: 8 },
    specLabel: { fontSize: 14, color: themeColors.textSecondary, width: 80 },
    specValue: { fontSize: 14, color: themeColors.text, flex: 1 },
    // Reviews
    reviewHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
    reviewRatingText: { fontSize: 18, fontWeight: 'bold', color: themeColors.error },
    reviewCountText: { fontSize: 14, color: themeColors.textSecondary },
    reviewTagContainer: { flexDirection: 'row', flexWrap: 'wrap', marginBottom: 10 },
    reviewTag: { backgroundColor: themeColors.backgroundSoft, color: themeColors.textSecondary, paddingHorizontal: 10, paddingVertical: 5, borderRadius: 15, marginRight: 8, marginBottom: 8, fontSize: 12 },
    reviewItem: { marginBottom: 15, paddingBottom: 15, borderBottomWidth: 1, borderColor: themeColors.border },
    reviewUser: { flexDirection: 'row', alignItems: 'center', marginBottom: 5 },
    reviewAvatar: { width: 30, height: 30, borderRadius: 15, marginRight: 8 },
    reviewUserName: { fontSize: 14, fontWeight: '500', color: themeColors.text },
    reviewStars: { flexDirection: 'row', marginVertical: 5 },
    reviewText: { fontSize: 14, color: themeColors.text, lineHeight: 20, marginBottom: 5 },
    reviewDate: { fontSize: 12, color: themeColors.textTertiary },
    viewMoreReviews: { textAlign: 'center', color: themeColors.primary, paddingVertical: 10, fontSize: 14 },
    // Recommendations
    recommendationContainer: { paddingBottom: 20 },
    recommendationItem: {
      width: (width - 30 - 20) / 3, // Section padding 15*2=30. Gaps between 3 items 10*2=20.
      marginRight: 10,
      backgroundColor: themeColors.card,
      borderRadius: 8,
      padding: 8,
    },
    recommendationImage: {
      width: ((width - 30 - 20) / 3) - 16, // Item width - item padding (8*2=16)
      height: ((width - 30 - 20) / 3) - 16,
      borderRadius: 6,
      alignSelf: 'center',
      marginBottom: 5,
      backgroundColor: themeColors.imageBackground,
    },
    recommendationName: { fontSize: 13, color: themeColors.text, marginBottom: 3, height: 32 }, // Fixed height for 2 lines
    recommendationPrice: { fontSize: 14, fontWeight: 'bold', color: themeColors.error },
    actionIconWrapper: {
      position: 'relative', // For cart badge positioning
    },
    cartBadge: {
      position: 'absolute',
      top: -5,
      right: -5, // Adjusted for better visual alignment
      backgroundColor: themeColors.error,
      borderRadius: 9,
      minWidth: 18,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 5,
      zIndex: 1, // Ensure badge is on top
    },
    cartBadgeText: {
      color: themeColors.buttonText,
      fontSize: 10,
      fontWeight: 'bold',
    },
    reviewImageContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 8,
    },
    reviewImage: {
      width: 70,
      height: 70,
      borderRadius: 4,
      marginRight: 8,
      marginBottom: 8,
      backgroundColor: themeColors.imageBackground,
    },
    loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    fixedBottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: 'white',
      borderTopWidth: 1,
      borderTopColor: '#EEEEEE',
      paddingTop: 10,
      paddingHorizontal: 15,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 5,
    },
    bottomBarActions: {
      flexDirection: 'row',
      flex: 2,
    },
    bottomBarAction: {
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 15,
    },
    bottomBarButtons: {
      flexDirection: 'row',
      flex: 3,
    },
    iconContainer: {
      position: 'relative',
    },
    badgeContainer: {
      position: 'absolute',
      top: -5,
      right: -8,
      backgroundColor: 'red',
      borderRadius: 10,
      minWidth: 18,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
    },
    badgeText: {
      color: 'white',
      fontSize: 10,
      fontWeight: 'bold',
    },
    actionLabel: {
      fontSize: 12,
      marginTop: 2,
    },
    // 底部固定操作栏按钮
    fixedAddToCartButton: { flex: 1, backgroundColor: '#FFA500', borderRadius: 25, paddingVertical: 12, marginRight: 10, alignItems: 'center' },
    fixedAddToCartText: { color: 'white', fontWeight: 'bold', fontSize: 16 },
    fixedBuyNowButton: { flex: 1, backgroundColor: '#FF4500', borderRadius: 25, paddingVertical: 12, alignItems: 'center' },
    fixedBuyNowText: { color: 'white', fontWeight: 'bold', fontSize: 16 },
  });

  if (loading || !product) {
    return (
      <SafeAreaView style={styles.safeArea}>
         <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.headerIcon}>
            <FontAwesome name="chevron-left" size={18} color={themeColors.text} />
          </TouchableOpacity>
          <View style={styles.headerTabsContainer}>
            {headerTabs.map(tab => (
              <TouchableOpacity key={tab} style={styles.headerTab} onPress={() => setActiveHeaderTab(tab)}>
                <Text style={[styles.headerTabText, activeHeaderTab === tab && styles.activeHeaderTabText]}>{tab}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <TouchableOpacity style={styles.headerIcon}><FontAwesome name="share-alt" size={20} color={themeColors.text} /></TouchableOpacity>
          <TouchableOpacity style={styles.headerIcon}><FontAwesome name="ellipsis-v" size={20} color={themeColors.text} /></TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: themeColors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.headerIcon}>
            <FontAwesome name="chevron-left" size={18} color={themeColors.text} />
          </TouchableOpacity>
          <View style={styles.headerTabsContainer}>
            {headerTabs.map(tab => (
              <TouchableOpacity 
                key={tab} 
                style={styles.headerTab}
                onPress={() => scrollToSection(tab)}
              >
                <Text style={[styles.headerTabText, activeHeaderTab === tab && styles.activeHeaderTabText]}>{tab}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <TouchableOpacity style={styles.headerIcon}>
            <FontAwesome name="share-alt" size={20} color={themeColors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerIcon}>
            <FontAwesome name="ellipsis-v" size={20} color={themeColors.text} />
          </TouchableOpacity>
        </View>

        <ScrollView 
          ref={scrollRef} 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 80 }} // Add padding to avoid content being hidden behind the fixed bottom bar
        >
          {/* 商品区域 - 图片库 */}
          <View ref={sectionRefs['商品']} style={styles.imageGalleryContainer}>
            <Image source={{ uri: product.images?.[selectedImageIndex] || product.image_url }} style={styles.mainImage} />
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.thumbnailContainer}>
              {product.images?.map((img, index) => (
                <TouchableOpacity key={index} onPress={() => setSelectedImageIndex(index)}>
                  <Image source={{ uri: img }} style={[styles.thumbnail, selectedImageIndex === index && styles.activeThumbnail]} />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Product Info */}
          <View style={styles.infoSection}>
            <View style={styles.priceContainer}>
              <Text style={styles.productPrice}>¥{product.price?.toFixed(2)} {product.original_price && product.original_price > product.price && <Text style={styles.originalPrice}>¥{product.original_price?.toFixed(2)}</Text>}</Text>
              {product.discount_percent && <Text style={styles.discountTag}>{(100 - product.discount_percent).toFixed(1)}折</Text>}
            </View>
            <Text style={styles.productName}>{product.name}</Text>
          </View>

          {/* Promotions */}
          <View style={styles.infoSection}>
            {product.promotions?.map(promo => (
              <TouchableOpacity key={promo.id} style={styles.promoItem}>
                <Text style={styles.promoTag}>{promo.type === 'coupon' ? '券' : '促'}</Text>
                <Text style={styles.promoText}>{promo.text}</Text>
                <FontAwesome name="chevron-right" size={14} color={themeColors.textTertiary} style={{marginLeft: 'auto'}}/>
              </TouchableOpacity>
            ))}
          </View>

          {/* Variant Selection */}
          <View style={styles.infoSection}>
            <TouchableOpacity style={styles.selectionRow}>
              <Text style={styles.selectionLabel}>配送</Text>
              <View style={styles.selectionValueContainer}>
                <FontAwesome name="map-marker" size={16} color={themeColors.textSecondary} style={{marginRight: 5}}/>
                <Text style={styles.selectionValueText} numberOfLines={1}>{product.deliveryInfo}</Text>
                <FontAwesome name="chevron-right" size={14} color={themeColors.textTertiary} />
              </View>
            </TouchableOpacity>

            <View style={styles.selectionRow}>
              <Text style={styles.selectionLabel}>颜色</Text>
              <View style={styles.selectionValueContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.colorSwatchContainer}>
                    {product.colors?.map(color => (
                    <TouchableOpacity key={color.name} onPress={() => setSelectedColor(color.name)}>
                        <View style={[styles.colorSwatch, {backgroundColor: color.value}, selectedColor === color.name && styles.activeColorSwatch]} />
                    </TouchableOpacity>
                    ))}
                </ScrollView>
                <FontAwesome name="chevron-right" size={14} color={themeColors.textTertiary} style={{marginLeft: 10}}/>
              </View>
            </View>

            <View style={styles.selectionRow}>
              <Text style={styles.selectionLabel}>版本</Text>
              <View style={styles.selectionValueContainer}>
                 <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    {product.versions?.map(version => (
                        <TouchableOpacity key={version} style={[styles.versionTag, selectedVersion === version && styles.activeVersionTag]} onPress={() => setSelectedVersion(version)}>
                            <Text style={[styles.versionText, selectedVersion === version && styles.activeVersionText]}>{version}</Text>
                        </TouchableOpacity>
                    ))}
                 </ScrollView>
                <FontAwesome name="chevron-right" size={14} color={themeColors.textTertiary} style={{marginLeft: 10}}/>
              </View>
            </View>

            <View style={styles.selectionRow}>
              <Text style={styles.selectionLabel}>数量</Text>
              <View style={styles.quantitySelector}>
                <TouchableOpacity onPress={() => handleQuantityChange(-1)} style={styles.quantityButton}><FontAwesome name="minus" size={14} color={themeColors.text} /></TouchableOpacity>
                <Text style={styles.quantityText}>{quantity}</Text>
                <TouchableOpacity onPress={() => handleQuantityChange(1)} style={styles.quantityButton}><FontAwesome name="plus" size={14} color={themeColors.text} /></TouchableOpacity>
              </View>
            </View>
          </View>

          {/* 详情区域 - 商品规格 */}
          <View ref={sectionRefs['详情']} style={styles.infoSection}>
            <Text style={styles.sectionTitle}>商品详情</Text>
            {product.parsedSpecs && product.parsedSpecs.map(spec => (
              <View key={spec.label} style={styles.specItem}>
                <Text style={styles.specLabel}>{spec.label}</Text>
                <Text style={styles.specValue}>{spec.value}</Text>
              </View>
            ))}
          </View>

          {/* 评价区域 - 用户评价 */}
          <View ref={sectionRefs['评价']} style={styles.infoSection}>
            <View style={styles.reviewHeader}>
              <Text style={styles.sectionTitle}>用户评价 ({product.reviews?.totalReviews || 0})</Text>
              <TouchableOpacity>
                <Text style={styles.reviewRatingText}>{product.reviews?.averageRating || 5.0} <FontAwesome name="chevron-right" size={14} color={themeColors.textTertiary} /></Text>
              </TouchableOpacity>
            </View>
            <View style={styles.reviewTagContainer}>
              {product.reviews?.tags?.map(tag => <Text key={tag} style={styles.reviewTag}>{tag}</Text>)}
            </View>
            {product.reviews?.items?.map(review => (
              <View key={review.id} style={styles.reviewItem}>
                <View style={styles.reviewUser}>
                  <Image source={{uri: review.userAvatar}} style={styles.reviewAvatar} />
                  <Text style={styles.reviewUserName}>{review.userName}</Text>
                </View>
                <View style={styles.reviewStars}>
                  {[...Array(5)].map((_, i) => <FontAwesome key={i} name={i < review.rating ? 'star' : 'star-o'} size={14} color="#FFD700" style={{marginRight: 2}}/>)}
                </View>
                <Text style={styles.reviewText}>{review.text}</Text>
                {review.images && review.images.length > 0 && (
                  <View style={styles.reviewImageContainer}>
                    {review.images.map((imgUrl, index) => (
                      <Image key={index} source={{ uri: imgUrl }} style={styles.reviewImage} />
                    ))}
                  </View>
                )}
                <Text style={styles.reviewDate}>{review.date}</Text>
              </View>
            ))}
            <TouchableOpacity><Text style={styles.viewMoreReviews}>查看更多评价 <FontAwesome name="chevron-right" size={12} color={themeColors.primary} /></Text></TouchableOpacity>
          </View>

          {/* 推荐区域 - 相关推荐 */}
          <View ref={sectionRefs['推荐']} style={[styles.infoSection, styles.recommendationContainer]}>
            <Text style={styles.sectionTitle}>相关推荐</Text>
            <FlatList
              horizontal
              data={product.recommendations || []}
              showsHorizontalScrollIndicator={false}
              keyExtractor={item => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.recommendationItem} onPress={() => router.push(`/product-details/${item.id}`)}>
                  <Image source={{ uri: item.imageUrl }} style={styles.recommendationImage} />
                  <Text style={styles.recommendationName} numberOfLines={2}>{item.name}</Text>
                  <Text style={styles.recommendationPrice}>¥{item.price.toFixed(2)}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </ScrollView>
        
        {/* Fixed Bottom Action Bar */}
        {product && !loading && (
          <View style={[styles.fixedBottomBar, { paddingBottom: insets.bottom > 0 ? insets.bottom : 10 }]}>
            <View style={styles.bottomBarActions}>
              {[
                { name: product.store?.icon || 'shopping-bag', label: '店铺', hasBadge: false, badgeCount: 0 },
                { name: 'comments-o', label: '客服', hasBadge: false, badgeCount: 0 },
                { name: 'heart-o', label: '收藏', hasBadge: false, badgeCount: 0 },
                { name: 'shopping-cart', label: '购物车', hasBadge: true, badgeCount: product.cartItemCount || 0 }
              ].map((item, index) => (
                <TouchableOpacity key={index} style={styles.bottomBarAction}>
                  <View style={styles.iconContainer}>
                    <FontAwesome name={item.name as any} size={20} color={themeColors.text} />
                    {item.hasBadge && item.badgeCount > 0 && (
                      <View style={styles.badgeContainer}>
                        <Text style={styles.badgeText}>{item.badgeCount > 99 ? '99+' : item.badgeCount}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.actionLabel}>{item.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
            <View style={styles.bottomBarButtons}>
              <TouchableOpacity 
                style={styles.fixedAddToCartButton} 
                onPress={handleAddToCart}
                disabled={isAddingToCart}
              >
                {isAddingToCart ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text style={styles.fixedAddToCartText}>加入购物车</Text>
                )}
              </TouchableOpacity>
              <TouchableOpacity style={styles.fixedBuyNowButton} onPress={() => handleBuyNow()}>
                <Text style={styles.fixedBuyNowText}>立即购买</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
}
