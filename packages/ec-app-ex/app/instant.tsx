import React, { useState } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Dimensions,
  View as RNView,
  Text as RNText,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { useColorScheme, Colors } from '@ec-nx/shared-logic';
import { Link } from 'expo-router'; // Added for potential back navigation if this is not a tab screen

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const PADDING = 15;

// Mock Data
const locationData = {
  address: '上海市浦东新区张江高科技园区',
  deliveryTime: '30分钟内送达',
};

const bannerData = {
  title: '闪电配送',
  subtitle: '下单后30分钟内送达',
  buttonText: '立即配送',
  image: 'https://images.unsplash.com/photo-1570058024331-5159ddde1102?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80', // Placeholder delivery person
};

const categoryIconsData = [
  { id: 'food', name: '美食', icon: 'cutlery' as const, color: '#FF6347' },
  { id: 'fresh', name: '生鲜', icon: 'shopping-basket' as const, color: '#32CD32' },
  { id: 'coffee', name: '咖啡', icon: 'coffee' as const, color: '#A0522D' },
  { id: 'medicine', name: '药品', icon: 'medkit' as const, color: '#4682B4' },
  { id: 'dessert', name: '甜点', icon: 'birthday-cake' as const, color: '#FFB6C1' },
];

const nearbyMerchantsData = [
  {
    id: 'merchant1',
    name: '有机生鲜超市',
    image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    distance: '0.8km',
    rating: 4.8,
    reviews: 328,
    deliveryTime: '20分钟',
    deliveryFee: '¥6',
  },
  {
    id: 'merchant2',
    name: '全时便利店',
    image: 'https://images.unsplash.com/photo-1588078795003-eb863f107392?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    distance: '0.3km',
    rating: 4.2,
    reviews: 156,
    deliveryTime: '10分钟',
    deliveryFee: '¥3',
  },
];

const hotProductsData = [
  {
    id: 'product1',
    name: '拿铁咖啡',
    brand: '星巴克咖啡',
    price: '¥32',
    image: 'https://images.unsplash.com/photo-1557006029-3daa7f59065a?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
  },
  {
    id: 'product2',
    name: '健康沙拉',
    brand: '有机生鲜超市',
    price: '¥42',
    image: 'https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
  },
  {
    id: 'product3',
    name: '新鲜水果拼盘',
    brand: '天天果园',
    price: '¥28',
    image: 'https://images.unsplash.com/photo-1571503549009-33cf4a695334?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
  },
  {
    id: 'product4',
    name: '巧克力蛋糕',
    brand: '甜蜜坊',
    price: '¥58',
    image: 'https://images.unsplash.com/photo-1563729784474-d77dbb933a9e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
  },
];

export default function InstantScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [searchText, setSearchText] = useState('');

  const RenderStarRating = ({ rating, reviews }: { rating: number; reviews: number }) => (
    <RNView style={styles.starRatingContainer}>
      {[...Array(5)].map((_, index) => (
        <FontAwesome
          key={index}
          name={index < Math.floor(rating) ? 'star' : index < rating ? 'star-half-o' : 'star-o'}
          size={12}
          color="#FFD700"
          style={styles.starIcon}
        />
      ))}
      <RNText style={[styles.ratingText, { color: colors.textSecondary }]}> {rating.toFixed(1)} ({reviews})</RNText>
    </RNView>
  );

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      {/* Optional Back Button if not a tab screen */}
      {/* <Link href="../" asChild>
        <TouchableOpacity style={styles.backButton}>
          <FontAwesome name="chevron-left" size={20} color={colors.primary} />
        </TouchableOpacity>
      </Link> */}
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {/* Top Bar: Location & Delivery Time */}
        <RNView style={[styles.topBar, { backgroundColor: colors.backgroundSoft }]}>
          <RNView style={styles.locationContainer}>
            <FontAwesome name="map-marker" size={16} color={colors.primary} style={styles.topBarIcon} />
            <RNText style={[styles.locationText, { color: colors.text }]} numberOfLines={1}>{locationData.address}</RNText>
            <FontAwesome name="chevron-down" size={12} color={colors.textSecondary} style={styles.topBarIcon} />
          </RNView>
          <RNView style={styles.deliveryTimeContainer}>
            <FontAwesome name="clock-o" size={16} color={colors.secondary} style={styles.topBarIcon} />
            <RNText style={[styles.deliveryTimeText, { color: colors.secondary }]}>{locationData.deliveryTime}</RNText>
          </RNView>
        </RNView>

        {/* Search Bar */}
        <RNView style={[styles.searchBarContainer, { backgroundColor: colors.background }]}>
          <FontAwesome name="search" size={18} color={colors.textTertiary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text, backgroundColor: colors.backgroundSecondary }]}
            placeholder="搜索附近商品"
            placeholderTextColor={colors.textTertiary}
            value={searchText}
            onChangeText={setSearchText}
          />
        </RNView>

        {/* Main Banner */}
        <RNView style={styles.mainBannerContainer}>
          <Image source={{ uri: bannerData.image }} style={styles.mainBannerImage} />
          <RNView style={styles.mainBannerOverlay} />
          <RNView style={styles.mainBannerContent}>
            <RNText style={styles.mainBannerTitle}>{bannerData.title}</RNText>
            <RNText style={styles.mainBannerSubtitle}>{bannerData.subtitle}</RNText>
            <TouchableOpacity style={[styles.mainBannerButton, { backgroundColor: colors.primary }]}>
              <FontAwesome name="bolt" size={16} color={colors.background} style={styles.bannerButtonIcon}/>
              <RNText style={[styles.mainBannerButtonText, { color: colors.background }]}>{bannerData.buttonText}</RNText>
            </TouchableOpacity>
          </RNView>
        </RNView>

        {/* Category Icons */}
        <RNView style={styles.categoryIconsSection}>
          {categoryIconsData.map(category => (
            <TouchableOpacity key={category.id} style={styles.categoryIconItem}>
              <RNView style={[styles.categoryIconCircle, { backgroundColor: category.color + '20' }]}>
                <FontAwesome name={category.icon} size={22} color={category.color} />
              </RNView>
              <RNText style={[styles.categoryIconText, { color: colors.textSecondary }]}>{category.name}</RNText>
            </TouchableOpacity>
          ))}
        </RNView>

        {/* Nearby Merchants Section */}
        <RNView style={styles.sectionContainer}>
          <RNText style={[styles.sectionTitle, { color: colors.text }]}>附近商家</RNText>
          {nearbyMerchantsData.map(merchant => (
            <TouchableOpacity key={merchant.id} style={[styles.merchantCard, { backgroundColor: colors.card }]}>
              <Image source={{ uri: merchant.image }} style={styles.merchantImage} />
              <RNView style={styles.merchantDistanceBadge}>
                <RNText style={styles.merchantDistanceText}>{merchant.distance}</RNText>
              </RNView>
              <RNView style={styles.merchantInfo}>
                <RNText style={[styles.merchantName, { color: colors.text }]} numberOfLines={1}>{merchant.name}</RNText>
                <RenderStarRating rating={merchant.rating} reviews={merchant.reviews} />
                <RNView style={styles.merchantDeliveryDetails}>
                  <FontAwesome name="clock-o" size={12} color={colors.textSecondary} />
                  <RNText style={[styles.merchantDetailText, { color: colors.textSecondary }]}> {merchant.deliveryTime}</RNText>
                  <RNText style={[styles.merchantDetailText, { color: colors.textSecondary, marginHorizontal: 5 }]}>|</RNText>
                  <FontAwesome name="motorcycle" size={12} color={colors.textSecondary} />
                  <RNText style={[styles.merchantDetailText, { color: colors.textSecondary }]}> 配送费{merchant.deliveryFee}</RNText>
                </RNView>
              </RNView>
            </TouchableOpacity>
          ))}
        </RNView>

        {/* Hot Products Section */}
        <RNView style={styles.sectionContainer}>
          <RNText style={[styles.sectionTitle, { color: colors.text }]}>热门商品</RNText>
          <FlatList
            data={hotProductsData}
            renderItem={({ item }) => (
              <TouchableOpacity style={[styles.hotProductCard, { backgroundColor: colors.card }]}>
                <Image source={{ uri: item.image }} style={styles.hotProductImage} />
                <RNText style={[styles.hotProductName, { color: colors.text }]} numberOfLines={1}>{item.name}</RNText>
                <RNText style={[styles.hotProductBrand, { color: colors.textSecondary }]} numberOfLines={1}>{item.brand}</RNText>
                <RNView style={styles.hotProductFooter}>
                  <RNText style={[styles.hotProductPrice, { color: colors.secondary }]}>{item.price}</RNText>
                  <TouchableOpacity style={[styles.addToCartButton, { backgroundColor: colors.primary }]}>
                    <FontAwesome name="plus" size={14} color={colors.background} />
                  </TouchableOpacity>
                </RNView>
              </TouchableOpacity>
            )}
            keyExtractor={item => item.id}
            numColumns={2}
            scrollEnabled={false} // Parent ScrollView handles scrolling
            columnWrapperStyle={styles.hotProductRow}
          />
        </RNView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  // backButton: { // Optional
  //   position: 'absolute',
  //   top: 50, // Adjust as needed for status bar height
  //   left: PADDING,
  //   zIndex: 10,
  //   padding: 5,
  // },
  scrollViewContent: {
    paddingBottom: PADDING,
  },
  topBar: {
    paddingHorizontal: PADDING,
    paddingVertical: PADDING / 1.5,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border, // Default border color
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: PADDING / 3,
  },
  locationText: {
    flex: 1, // Allows text to truncate
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: PADDING / 2,
  },
  topBarIcon: {
    marginRight: PADDING / 3,
  },
  deliveryTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTimeText: {
    fontSize: 13,
    fontWeight: '600',
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: PADDING,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border, 
  },
  searchIcon: {
    marginRight: PADDING / 2,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    paddingHorizontal: PADDING,
    fontSize: 15,
  },
  mainBannerContainer: {
    height: SCREEN_WIDTH * 0.45,
    margin: PADDING,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  mainBannerImage: {
    width: '100%',
    height: '100%',
  },
  mainBannerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  mainBannerContent: {
    ...StyleSheet.absoluteFillObject,
    padding: PADDING,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  mainBannerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: PADDING / 3,
  },
  mainBannerSubtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    marginBottom: PADDING,
  },
  mainBannerButton: {
    flexDirection: 'row',
    paddingHorizontal: PADDING * 1.2,
    paddingVertical: PADDING / 1.5,
    borderRadius: 20,
    alignItems: 'center',
  },
  bannerButtonIcon: {
    marginRight: PADDING / 2,
  },
  mainBannerButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoryIconsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: PADDING,
    paddingHorizontal: PADDING / 2,
    borderBottomWidth: 6,
    borderBottomColor: Colors.light.backgroundSecondary, // Use a light separator
  },
  categoryIconItem: {
    alignItems: 'center',
    width: (SCREEN_WIDTH - PADDING) / 5, // Distribute space for 5 items
  },
  categoryIconCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: PADDING / 2,
  },
  categoryIconText: {
    fontSize: 12,
    textAlign: 'center',
  },
  sectionContainer: {
    paddingHorizontal: PADDING,
    marginTop: PADDING,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: PADDING,
  },
  merchantCard: {
    flexDirection: 'row',
    borderRadius: 10,
    padding: PADDING,
    marginBottom: PADDING,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    // Elevation for Android
    elevation: 1,
  },
  merchantImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: PADDING,
  },
  merchantDistanceBadge: {
    position: 'absolute',
    top: PADDING + 60, // Below image
    left: PADDING + 5,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  merchantDistanceText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  merchantInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  merchantName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  starRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  starIcon: {
    marginRight: 1,
  },
  ratingText: {
    fontSize: 11,
    marginLeft: 3,
  },
  merchantDeliveryDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  merchantDetailText: {
    fontSize: 11,
  },
  hotProductRow: {
    justifyContent: 'space-between',
  },
  hotProductCard: {
    width: (SCREEN_WIDTH - PADDING * 3) / 2, // 2 items per row with spacing
    borderRadius: 8,
    marginBottom: PADDING,
    padding: PADDING / 1.5,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    // Elevation for Android
    elevation: 1,
  },
  hotProductImage: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 6,
    marginBottom: PADDING / 2,
  },
  hotProductName: {
    fontSize: 14,
    fontWeight: '500',
  },
  hotProductBrand: {
    fontSize: 11,
    marginBottom: PADDING / 2,
  },
  hotProductFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  hotProductPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addToCartButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

