import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import PlatformTest from '@/components/PlatformTest';

export default function PlatformTestScreen() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <Text style={styles.title}>平台检测测试</Text>
      <Text style={styles.platform}>当前平台: {Platform.OS}</Text>
      <Text style={styles.description}>
        如果平台特定文件加载正确：
        {'\n'}• Web 平台应该显示 "Web Platform Test Component"
        {'\n'}• 原生平台应该显示 "Default Platform Test Component"
      </Text>
      
      <View style={styles.testContainer}>
        <PlatformTest />
      </View>
      
      <Text style={styles.instructions}>
        检查控制台日志以查看 StripeProvider 的加载情况
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  platform: {
    fontSize: 18,
    marginBottom: 20,
    color: '#007AFF',
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    marginBottom: 30,
    textAlign: 'center',
    lineHeight: 20,
  },
  testContainer: {
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  instructions: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
