import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  useColorScheme,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic';

const orderData = {
  orderNumber: '202506091423789',
  orderTime: '2025-06-09 14:23:45',
  paymentMethod: '支付宝',
  paymentAmount: 3567.00,
  recipient: '张三 138****5678',
  shippingAddress: '上海市浦东新区张江高科技园区博云路2号楼5层',
  deliveryMethod: '快递配送',
  estimatedDelivery: '6月10日 16:00 - 18:00',
  itemsByStore: [
    {
      storeName: 'Apple 官方旗舰店',
      storeIcon: 'apple' as const,
      products: [
        {
          id: '1',
          name: '真无线蓝牙耳机 Pro',
          spec: '白色 | 标准版',
          price: 1299,
          quantity: 1,
          imageUrl: 'https://via.placeholder.com/80x80.png?text=AirPods',
        },
        {
          id: '2',
          name: '20W USB-C 电源适配器',
          spec: '白色',
          price: 149,
          quantity: 1,
          imageUrl: 'https://via.placeholder.com/80x80.png?text=Adapter',
        },
      ],
      productAmount: 1448,
      discount: 100,
      shippingFee: 8,
      subtotal: 1356,
    },
    {
      storeName: 'Samsung 官方旗舰店',
      storeIcon: 'mobile' as const, // Placeholder for Samsung icon
      products: [
        {
          id: '3',
          name: 'Galaxy Watch 5 Pro',
          spec: '黑色 | 45mm',
          price: 2399,
          quantity: 1,
          imageUrl: 'https://via.placeholder.com/80x80.png?text=Watch5Pro',
        },
      ],
      productAmount: 2399,
      discount: 200,
      shippingFee: 12,
      subtotal: 2211,
    },
  ],
  orderTotal: 3567,
};

const recommendedProducts = [
  { id: 'r1', name: '头戴式无线耳机', price: 699, imageUrl: 'https://via.placeholder.com/120x120.png?text=Headphones' },
  { id: 'r2', name: '全画幅微单手机...', price: 4999, imageUrl: 'https://via.placeholder.com/120x120.png?text=CameraPhone' }, // Name truncated as in prototype
  { id: 'r3', name: '20000mAh...', price: 199, imageUrl: 'https://via.placeholder.com/120x120.png?text=PowerBank' }, // Name truncated
  { id: 'r4', name: '智能手表', price: 1299, imageUrl: 'https://via.placeholder.com/120x120.png?text=SmartWatch' },
];

export default function OrderSuccessScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: themeColors.backgroundSoft,
    },
    scrollView: {
      flex: 1,
    },
    contentContainer: {
      paddingBottom: 20,
    },
    successHeader: {
      alignItems: 'center',
      paddingVertical: 30,
      backgroundColor: themeColors.card,
    },
    successIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: themeColors.success, // Green color
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
    },
    successTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: themeColors.text,
      marginBottom: 8,
    },
    successSubtitle: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    section: {
      backgroundColor: themeColors.card,
      marginTop: 10,
      paddingHorizontal: 15,
      paddingVertical: 10,
    },
    sectionTitleText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: themeColors.text,
      marginBottom: 10,
      paddingTop: 5,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 6,
    },
    infoLabel: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    infoValue: {
      fontSize: 14,
      color: themeColors.text,
      textAlign: 'right',
    },
    highlightValue: {
      color: themeColors.error, // Red for payment amount
      fontWeight: 'bold',
    },
    itemContainer: {
      flexDirection: 'row',
      marginBottom: 15,
      paddingTop: 10,
    },
    itemImage: {
      width: 70,
      height: 70,
      borderRadius: 8,
      marginRight: 10,
      backgroundColor: themeColors.imageBackground,
    },
    itemDetails: {
      flex: 1,
    },
    itemName: {
      fontSize: 15,
      fontWeight: '500',
      color: themeColors.text,
    },
    itemSpec: {
      fontSize: 12,
      color: themeColors.textMuted,
      marginTop: 3,
    },
    itemPriceQuantity: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 8,
    },
    itemPrice: {
      fontSize: 14,
      color: themeColors.text,
    },
    itemQuantity: {
      fontSize: 14,
      color: themeColors.textMuted,
    },
    storeSummaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    storeSummaryLabel: {
      fontSize: 14,
      color: themeColors.textSecondary,
    },
    storeSummaryValue: {
      fontSize: 14,
      color: themeColors.text,
    },
    storeDiscountValue: {
        color: themeColors.error,
    },
    storeSubtotalRow: {
      borderTopWidth: StyleSheet.hairlineWidth,
      borderTopColor: themeColors.border,
      marginTop: 5,
      paddingTop: 8,
    },
    orderTotalSection: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      paddingVertical: 15,
      borderTopWidth: StyleSheet.hairlineWidth,
      borderTopColor: themeColors.border,
    },
    orderTotalLabel: {
      fontSize: 16,
      color: themeColors.text,
      marginRight: 5,
    },
    orderTotalAmount: {
      fontSize: 20,
      fontWeight: 'bold',
      color: themeColors.error,
    },
    buttonsSection: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 15,
      paddingVertical: 20,
      backgroundColor: themeColors.card,
      marginTop: 10,
    },
    button: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 20, // Rounded buttons
      alignItems: 'center',
      marginHorizontal: 5,
    },
    primaryButton: {
      backgroundColor: themeColors.primary,
    },
    secondaryButton: {
      backgroundColor: themeColors.card,
      borderWidth: 1,
      borderColor: themeColors.primary,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    primaryButtonText: {
      color: themeColors.buttonText,
    },
    secondaryButtonText: {
      color: themeColors.primary,
    },
    recommendationsSection: {
      marginTop: 10,
      backgroundColor: themeColors.card,
      paddingVertical: 15,
    },
    recommendationsTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: themeColors.text,
      marginBottom: 10,
      paddingHorizontal: 15,
    },
    recommendationItem: {
      width: 120,
      marginRight: 10,
      alignItems: 'center',
    },
    recommendationImage: {
      width: 100,
      height: 100,
      borderRadius: 8,
      backgroundColor: themeColors.imageBackground,
      marginBottom: 8,
    },
    recommendationName: {
      fontSize: 13,
      color: themeColors.text,
      textAlign: 'center',
      marginBottom: 4,
      height: 35, // For 2 lines of text
    },
    recommendationPrice: {
      fontSize: 14,
      fontWeight: 'bold',
      color: themeColors.error,
    },
  });

  const renderStoreSection = (store: typeof orderData.itemsByStore[0]) => (
    <View key={store.storeName} style={styles.section}>
      <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 5}}>
        <FontAwesome name={store.storeIcon} size={16} color={themeColors.textSecondary} style={{marginRight: 8}}/>
        <Text style={[styles.sectionTitleText, {marginBottom: 0}]}>{store.storeName}</Text>
      </View>
      {store.products.map(item => (
        <View key={item.id} style={styles.itemContainer}>
          <Image source={{ uri: item.imageUrl }} style={styles.itemImage} />
          <View style={styles.itemDetails}>
            <Text style={styles.itemName} numberOfLines={2}>{item.name}</Text>
            <Text style={styles.itemSpec}>{item.spec}</Text>
            <View style={styles.itemPriceQuantity}>
              <Text style={styles.itemPrice}>¥{item.price}</Text>
              <Text style={styles.itemQuantity}>x{item.quantity}</Text>
            </View>
          </View>
        </View>
      ))}
      <View style={styles.storeSummaryRow}>
        <Text style={styles.storeSummaryLabel}>商品金额</Text>
        <Text style={styles.storeSummaryValue}>¥{store.productAmount.toFixed(2)}</Text>
      </View>
      <View style={styles.storeSummaryRow}>
        <Text style={styles.storeSummaryLabel}>优惠</Text>
        <Text style={[styles.storeSummaryValue, styles.storeDiscountValue]}>-¥{store.discount.toFixed(2)}</Text>
      </View>
      <View style={styles.storeSummaryRow}>
        <Text style={styles.storeSummaryLabel}>运费</Text>
        <Text style={styles.storeSummaryValue}>¥{store.shippingFee.toFixed(2)}</Text>
      </View>
      <View style={[styles.storeSummaryRow, styles.storeSubtotalRow]}>
        <Text style={[styles.storeSummaryLabel, {fontWeight: 'bold'}]}>小计</Text>
        <Text style={[styles.storeSummaryValue, {fontWeight: 'bold'}]}>¥{store.subtotal.toFixed(2)}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <View style={styles.successHeader}>
          <View style={styles.successIconContainer}>
            <FontAwesome name="check" size={30} color="#FFFFFF" />
          </View>
          <Text style={styles.successTitle}>支付成功</Text>
          <Text style={styles.successSubtitle}>感谢您的购买，您的订单已确认</Text>
        </View>

        <View style={styles.section}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>订单编号</Text>
            <Text style={styles.infoValue}>{orderData.orderNumber}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>下单时间</Text>
            <Text style={styles.infoValue}>{orderData.orderTime}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>支付方式</Text>
            <Text style={styles.infoValue}>{orderData.paymentMethod}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>支付金额</Text>
            <Text style={[styles.infoValue, styles.highlightValue]}>¥{orderData.paymentAmount.toFixed(2)}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitleText}>配送信息</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>收货人</Text>
            <Text style={styles.infoValue}>{orderData.recipient}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>收货地址</Text>
            <Text style={styles.infoValue} numberOfLines={2}>{orderData.shippingAddress}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>配送方式</Text>
            <Text style={styles.infoValue}>{orderData.deliveryMethod}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>预计送达</Text>
            <Text style={styles.infoValue}>{orderData.estimatedDelivery}</Text>
          </View>
        </View>

        <Text style={[styles.sectionTitleText, {paddingHorizontal: 15, marginTop: 10, backgroundColor: themeColors.card, paddingTop:15, paddingBottom:5, marginBottom:0}]}>订单详情</Text>
        {orderData.itemsByStore.map(renderStoreSection)}

        <View style={[styles.section, styles.orderTotalSection]}>
            <Text style={styles.orderTotalLabel}>订单总计</Text>
            <Text style={styles.orderTotalAmount}>¥{orderData.orderTotal.toFixed(2)}</Text>
        </View>

        <View style={styles.buttonsSection}>
          <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={() => router.push('/orders/202506091423789') /* Example route */}>
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>查看订单</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={() => router.replace('/(tabs)')}>
            <Text style={[styles.buttonText, styles.primaryButtonText]}>返回首页</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.recommendationsSection}>
          <Text style={styles.recommendationsTitle}>猜你喜欢</Text>
          <FlatList
            data={recommendedProducts}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ paddingLeft: 15, paddingRight: 5}} // Add padding for first and last item spacing
            renderItem={({ item }) => (
              <TouchableOpacity style={styles.recommendationItem} onPress={() => router.push(`/products/${item.id}`)}>
                <Image source={{ uri: item.imageUrl }} style={styles.recommendationImage} />
                <Text style={styles.recommendationName} numberOfLines={2}>{item.name}</Text>
                <Text style={styles.recommendationPrice}>¥{item.price}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
