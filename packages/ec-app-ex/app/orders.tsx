import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  useColorScheme,
  Alert,
} from 'react-native';
import { Image } from 'expo-image';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic';
import { supabase, getUserOrders, deleteOrder } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';
import * as supabaseClient from '@/lib/supabase';


// Define types
interface OrderItemProduct {
  name: string;
  image_url: string;
  [key: string]: any; // 允许其他字段
}

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  products: OrderItemProduct;
  product_id?: string;
  [key: string]: any; // 允许其他字段
}

interface Order {
  id: string;
  created_at: string;
  total_amount: number;
  status: string; // 使用更灵活的类型定义
  order_items: OrderItem[];
  customer_id?: string;
  user_id?: string;
  [key: string]: any; // 允许其他字段
}

const TABS: { name: string; status: Order['status'] | 'ALL' }[] = [
  { name: '全部', status: 'ALL' },
  { name: '待付款', status: 'pending_payment' },
  { name: '待发货', status: 'processing' },
  { name: '待收货', status: 'shipped' },
  { name: '待评价', status: 'delivered' },
  { name: '退款/售后', status: 'refunded' },
];

const statusTextMap: { [key in Order['status']]: string } = {
  pending_payment: '待付款',
  processing: '待发货',
  shipped: '待收货',
  delivered: '待评价',
  completed: '已完成',
  refunded: '已退款',
};

const OrderCard = ({ order, colors, onRefresh }: { order: Order; colors: any; onRefresh: () => void }) => {
  const totalItems = order.order_items.reduce((sum, item) => sum + item.quantity, 0);

  // 处理删除订单
  const handleDeleteOrder = () => {
    Alert.alert(
      '删除订单',
      '确定要删除此订单吗？此操作不可恢复。',
      [
        {
          text: '取消',
          style: 'cancel'
        },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              const { success, error } = await deleteOrder(order.id);
              if (success) {
                Alert.alert('成功', '订单已删除');
                // 刷新订单列表
                if (onRefresh) {
                  onRefresh();
                }
              } else {
                console.error('删除订单失败:', error);
                Alert.alert('错误', '删除订单失败，请稍后重试');
              }
            } catch (error) {
              console.error('删除订单时出现异常:', error);
              Alert.alert('错误', '删除订单时出现异常，请稍后重试');
            }
          }
        }
      ]
    );
  };

  const renderActionButtons = () => {
    switch (order.status) {
      case 'pending_payment':
        return (
          <>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.primary }]}>
              <Text style={styles.actionButtonText}>立即付款</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { borderColor: '#ff4d4f', borderWidth: 1 }]}
              onPress={handleDeleteOrder}
            >
              <Text style={[styles.actionButtonText, { color: '#ff4d4f' }]}>删除订单</Text>
            </TouchableOpacity>
          </>
        );
      case 'shipped':
        return (
          <>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: colors.primary }]}>
              <Text style={styles.actionButtonText}>确认收货</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { borderColor: '#ff4d4f', borderWidth: 1 }]}
              onPress={handleDeleteOrder}
            >
              <Text style={[styles.actionButtonText, { color: '#ff4d4f' }]}>删除订单</Text>
            </TouchableOpacity>
          </>
        );
      case 'delivered':
        return (
          <>
            <TouchableOpacity style={[styles.actionButton, { borderColor: colors.primary, borderWidth: 1 }]}>
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>评价</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { borderColor: '#ff4d4f', borderWidth: 1 }]}
              onPress={handleDeleteOrder}
            >
              <Text style={[styles.actionButtonText, { color: '#ff4d4f' }]}>删除订单</Text>
            </TouchableOpacity>
          </>
        );
      default:
        return (
          <>
            <TouchableOpacity style={[styles.actionButton, { borderColor: colors.border, borderWidth: 1 }]}>
              <Text style={[styles.actionButtonText, { color: colors.text }]}>查看详情</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { borderColor: '#ff4d4f', borderWidth: 1 }]}
              onPress={handleDeleteOrder}
            >
              <Text style={[styles.actionButtonText, { color: '#ff4d4f' }]}>删除订单</Text>
            </TouchableOpacity>
          </>
        );
    }
  };

  return (
    <View style={[styles.card, { backgroundColor: colors.card }]}>
      <View style={[styles.cardHeader, { borderBottomColor: colors.border }]}>
        <Text style={[styles.storeName, { color: colors.text }]}>官方自营店</Text>
        <Text style={[styles.statusText, { color: colors.primary }]}>{statusTextMap[order.status] || order.status}</Text>
      </View>
      <View>
        {order.order_items.map(item => (
          <View key={item.id} style={styles.productItem}>
            <Image source={{ uri: item.products.image_url }} style={styles.productImage} />
            <View style={styles.productInfo}>
              <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>{item.products.name}</Text>
            </View>
            <View style={styles.priceInfo}>
              <Text style={{color: colors.text}}>¥{item.price.toFixed(2)}</Text>
              <Text style={{color: colors.textMuted}}>x{item.quantity}</Text>
            </View>
          </View>
        ))}
      </View>
      <View style={[styles.cardFooter, { borderTopColor: colors.border }]}>
        <Text style={[styles.footerText, { color: colors.textMuted }]}>共{totalItems}件商品 合计: </Text>
        <Text style={[styles.totalAmount, { color: colors.text }]}>¥{order.total_amount.toFixed(2)}</Text>
      </View>
      <View style={styles.actionsContainer}>
        {renderActionButtons()}
      </View>
    </View>
  );
};

export default function OrdersScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ status?: string }>();
  const colors = Colors[useColorScheme() ?? 'light'];
  
  const [activeTab, setActiveTab] = useState<Order['status'] | 'ALL'>(params.status as any || 'ALL');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);

  // Fetch current user on component mount
  useEffect(() => {
    const fetchUser = async () => {
      const {  user } = await supabaseClient.getUser();
      setUser(user);
    };
    fetchUser();
  }, []);

  // 刷新订单列表的函数
  const handleRefresh = useCallback(() => {
    console.log('刷新订单列表');
    if (user) {
      fetchOrders();
    }
  }, [user]);

  // 获取订单列表
  const fetchOrders = useCallback(async () => {
    if (!user) {
      console.log('No user found, cannot fetch orders');
      return;
    }
    
    console.log('Fetching orders for user:', user.id, 'with status filter:', activeTab);
    setLoading(true);
    
    try {
      const statusFilter = activeTab !== 'ALL' ? activeTab : undefined;
      console.log('Calling getUserOrders with:', user.id, statusFilter);
      
      const { orders, error } = await getUserOrders(user.id, statusFilter);
      
      console.log('Raw orders response:', orders);
      console.log('Error from getUserOrders:', error);
      
      if (error) {
        console.error('Error fetching orders:', error);
      } else {
        // Process orders to ensure they match the expected format
        const processedOrders = orders?.map(order => {
          console.log('Processing order:', order.id, 'with status:', order.status);
          console.log('Order items:', order.order_items);
          return {
            ...order,
            order_items: order.order_items?.map((item: OrderItem) => {
              console.log('Processing order item:', item.id, 'product:', item.products);
              return {
                ...item,
                products: item.products || { name: 'Unknown Product', image_url: 'https://via.placeholder.com/80' }
              };
            }) || []
          };
        }) || [];
        
        console.log('Processed orders:', processedOrders.length);
        setOrders(processedOrders);
      }
    } catch (err) {
      console.error('Unexpected error fetching orders:', err);
    } finally {
      setLoading(false);
    }
  }, [user, activeTab]);
  
  // 当组件加载或 activeTab 变化时获取订单
  useFocusEffect(
    useCallback(() => {
      fetchOrders();
    }, [fetchOrders])
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.backgroundSecondary }]}>
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <FontAwesome name="chevron-left" size={18} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>我的订单</Text>
        <View style={styles.headerRightPlaceholder} />
      </View>

      <View style={[styles.tabBar, { backgroundColor: colors.card }]}>
        {TABS.map(tab => (
          <TouchableOpacity key={tab.status} onPress={() => setActiveTab(tab.status)} style={[styles.tab, activeTab === tab.status && { borderBottomColor: colors.primary }]}>
            <Text style={[styles.tabText, { color: activeTab === tab.status ? colors.primary : colors.textMuted }]}>{tab.name}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {loading ? (
        <ActivityIndicator style={styles.centered} size="large" color={colors.primary} />
      ) : orders.length === 0 ? (
        <View style={styles.centered}>
          <FontAwesome name="file-text-o" size={60} color={colors.textMuted} />
          <Text style={[styles.emptyText, { color: colors.textMuted }]}>暂无订单</Text>
        </View>
      ) : (
        <FlatList
          data={orders}
          renderItem={({ item }) => <OrderCard order={item} colors={colors} onRefresh={handleRefresh} />}
          keyExtractor={(item: Order) => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', height: 60, paddingHorizontal: 15, borderBottomWidth: 1 },
  backButton: { padding: 5 },
  headerTitle: { fontSize: 18, fontWeight: 'bold' },
  headerRightPlaceholder: { width: 24 },
  tabBar: { flexDirection: 'row', justifyContent: 'space-around' },
  tab: { paddingVertical: 15, borderBottomWidth: 2, borderBottomColor: 'transparent' },
  tabText: { fontSize: 15, fontWeight: '500' },
  centered: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  emptyText: { marginTop: 15, fontSize: 16 },
  listContainer: { padding: 15 },
  card: { borderRadius: 10, marginBottom: 15, overflow: 'hidden' },
  cardHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 15, borderBottomWidth: 1 },
  storeName: { fontWeight: 'bold' },
  statusText: { fontWeight: 'bold' },
  productItem: { flexDirection: 'row', padding: 15 },
  productImage: { width: 80, height: 80, borderRadius: 8, marginRight: 15 },
  productInfo: { flex: 1, justifyContent: 'center' },
  productName: { fontSize: 14, marginBottom: 5 },
  priceInfo: { alignItems: 'flex-end' },
  cardFooter: { flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center', padding: 15, borderTopWidth: 1 },
  footerText: { fontSize: 14 },
  totalAmount: { fontSize: 16, fontWeight: 'bold' },
  actionsContainer: { flexDirection: 'row', justifyContent: 'flex-end', padding: 15, borderTopWidth: 1 },
  actionButton: { paddingHorizontal: 15, paddingVertical: 7, borderRadius: 20, borderWidth: 1, marginLeft: 10 },
  actionButtonText: { fontWeight: 'bold' },
});