import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Platform, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { initializeStripe, createPaymentIntent } from '@/lib/stripeWrapper';

export default function StripeTestScreen() {
  const [status, setStatus] = useState('初始化中...');
  const [paymentResult, setPaymentResult] = useState<string>('');

  useEffect(() => {
    testStripeInitialization();
  }, []);

  const testStripeInitialization = async () => {
    try {
      console.log(`🔍 Testing Stripe on platform: ${Platform.OS}`);
      setStatus(`正在测试 ${Platform.OS} 平台的 Stripe 初始化...`);
      
      await initializeStripe();
      setStatus(`✅ ${Platform.OS} 平台 Stripe 初始化成功！`);
    } catch (error) {
      console.error('Stripe initialization error:', error);
      setStatus(`❌ ${Platform.OS} 平台 Stripe 初始化失败: ${error}`);
    }
  };

  const testPaymentIntent = async () => {
    try {
      setPaymentResult('创建支付意图中...');
      const result = await createPaymentIntent(1000, 'cny');
      setPaymentResult(`✅ 支付意图创建成功: ${result.clientSecret}`);
    } catch (error) {
      console.error('Payment intent error:', error);
      setPaymentResult(`❌ 支付意图创建失败: ${error}`);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>Stripe 平台测试</Text>
      <Text style={styles.platform}>当前平台: {Platform.OS}</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>初始化状态:</Text>
        <Text style={styles.status}>{status}</Text>
      </View>

      <TouchableOpacity style={styles.button} onPress={testPaymentIntent}>
        <Text style={styles.buttonText}>测试创建支付意图</Text>
      </TouchableOpacity>

      {paymentResult ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>支付意图结果:</Text>
          <Text style={styles.result}>{paymentResult}</Text>
        </View>
      ) : null}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>预期行为:</Text>
        <Text style={styles.description}>
          • Web 平台: 应该使用模拟实现，不加载原生 Stripe 模块
          {'\n'}• 原生平台: 应该使用真实的 Stripe SDK
          {'\n'}• 不应该出现 "react-native/Libraries/Utilities/codegenNativeCommands" 错误
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  platform: {
    fontSize: 18,
    textAlign: 'center',
    color: '#007AFF',
    fontWeight: '600',
    marginBottom: 30,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  status: {
    fontSize: 14,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  result: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'monospace',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
});
