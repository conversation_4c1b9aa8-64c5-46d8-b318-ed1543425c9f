import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TextInput, TouchableOpacity, useColorScheme, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';
import { useAuth } from '@/hooks/useAuth';

export default function SignupScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];
  const { signup, user, loading: authLoading } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.replace('/(tabs)/profile');
    }
  }, [user]);

  const handleSignup = async () => {
    if (!email || !password || !confirmPassword) {
      Alert.alert('提示', '请填写所有必填字段');
      return;
    }
    
    if (password !== confirmPassword) {
      Alert.alert('提示', '两次输入的密码不一致');
      return;
    }
    
    setIsLoading(true);
    try {
      const { error } = await signup(email, password);
      if (error) {
        Alert.alert('注册失败', error.message);
      } else {
        Alert.alert('注册成功', '请登录您的账号');
        router.replace('/login');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败，请稍后再试';
      Alert.alert('注册失败', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <FontAwesome name="chevron-left" size={22} color={themeColors.text} />
      </TouchableOpacity>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>创建账号</Text>
          <Text style={styles.subtitle}>请填写以下信息注册新账号</Text>
        </View>

        <View style={styles.inputContainer}>
          <FontAwesome name="envelope" size={20} color={themeColors.textTertiary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder="邮箱地址"
            placeholderTextColor={themeColors.textTertiary}
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
          />
        </View>

        <View style={styles.inputContainer}>
          <FontAwesome name="lock" size={22} color={themeColors.textTertiary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder="密码"
            placeholderTextColor={themeColors.textTertiary}
            secureTextEntry={!isPasswordVisible}
            value={password}
            onChangeText={setPassword}
          />
          <TouchableOpacity onPress={() => setIsPasswordVisible(!isPasswordVisible)} style={{ padding: 5 }}>
            <FontAwesome name={isPasswordVisible ? 'eye' : 'eye-slash'} size={20} color={themeColors.textTertiary} />
          </TouchableOpacity>
        </View>

        <View style={styles.inputContainer}>
          <FontAwesome name="lock" size={22} color={themeColors.textTertiary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder="确认密码"
            placeholderTextColor={themeColors.textTertiary}
            secureTextEntry={!isConfirmPasswordVisible}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
          />
          <TouchableOpacity onPress={() => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)} style={{ padding: 5 }}>
            <FontAwesome name={isConfirmPasswordVisible ? 'eye' : 'eye-slash'} size={20} color={themeColors.textTertiary} />
          </TouchableOpacity>
        </View>

        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            点击注册，即表示您同意我们的
            <Text style={styles.termsLink} onPress={() => {/* Navigate to terms */}}>服务条款</Text>
            和
            <Text style={styles.termsLink} onPress={() => {/* Navigate to privacy policy */}}>隐私政策</Text>
          </Text>
        </View>

        <TouchableOpacity 
          style={[styles.signupButton, (isLoading || authLoading) && { opacity: 0.7 }]}
          disabled={isLoading || authLoading}
          onPress={handleSignup}
        >
          {(isLoading || authLoading) ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.signupButtonText}>注册</Text>
          )}
        </TouchableOpacity>

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>已有账号?</Text>
          <TouchableOpacity onPress={() => router.push('/login')}>
            <Text style={styles.loginLink}>立即登录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 25,
    paddingBottom: 30,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1,
    padding: 10,
  },
  headerContainer: {
    alignItems: 'flex-start',
    marginBottom: 40,
    marginTop: 80,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: Colors.light.text,
  },
  termsContainer: {
    marginBottom: 30,
  },
  termsText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  termsLink: {
    color: Colors.light.primary,
    fontWeight: '500',
  },
  signupButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 30,
  },
  signupButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  loginLink: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: 'bold',
    marginLeft: 5,
  },
});
