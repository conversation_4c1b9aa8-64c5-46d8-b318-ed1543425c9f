import React, { useState } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, TextInput, FlatList, Dimensions, View as RNView, Text as RNText } from 'react-native';
import { Image } from 'expo-image';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome } from '@expo/vector-icons';
import { useColorScheme, Colors } from '@ec-nx/shared-logic';
import { Link, router } from 'expo-router';

const SCREEN_WIDTH = Dimensions.get('window').width;

// Mock data for search history
const searchHistory = [
  '无线蓝牙耳机',
  '智能手表',
  '手机壳',
  '充电宝',
  '平板电脑'
];

// Mock data for hot searches
const hotSearches = [
  '无线耳机',
  '智能手表',
  '手机',
  '平板电脑',
  '笔记本',
  '充电器',
  '手机壳',
  '运动鞋'
];

// Mock data for recommended products
const recommendedProducts = [
  { 
    id: '1', 
    name: '无线蓝牙耳机', 
    image: 'https://images.unsplash.com/photo-1585386959984-a4155224a1ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=334&q=80',
    price: '¥129',
    sales: '2.5万人付款'
  },
  { 
    id: '2', 
    name: '智能手表', 
    image: 'https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
    price: '¥199',
    sales: '1.8万人付款'
  },
  { 
    id: '3', 
    name: '便携式音箱', 
    image: 'https://images.unsplash.com/photo-1491472253230-a044054ca35f?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
    price: '¥89',
    sales: '1.2万人付款'
  },
  { 
    id: '4', 
    name: '智能台灯', 
    image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
    price: '¥159',
    sales: '0.9万人付款'
  }
];

type ProductItemProps = { 
  item: { 
    id: string; 
    name: string; 
    image: string; 
    price: string;
    sales: string;
  } 
};

export default function SearchScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [searchText, setSearchText] = useState('');

  // Product item renderer
  const renderProductItem = ({ item }: ProductItemProps) => (
    <TouchableOpacity 
      style={[styles.productItem, { backgroundColor: colors.backgroundSecondary }]}
      onPress={() => router.push(`/product/${item.id}`)}
    >
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <RNView style={styles.productInfo}>
        <RNText style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
          {item.name}
        </RNText>
        <RNView style={styles.productMeta}>
          <RNText style={styles.productPrice}>{item.price}</RNText>
          <RNText style={styles.productSales}>{item.sales}</RNText>
        </RNView>
      </RNView>
    </TouchableOpacity>
  );

  return (
    <RNView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {/* Status Bar Space */}
      <RNView style={[styles.statusBar, { backgroundColor: colors.background }]} />
      
      {/* Search Bar */}
      <RNView style={[styles.searchBarContainer, { backgroundColor: colors.background }]}>
        <RNView style={[styles.searchBar, { backgroundColor: colors.backgroundSecondary }]}>
          <FontAwesome name="search" size={16} color="#999" />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="搜索商品"
            placeholderTextColor="#999"
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText ? (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <FontAwesome name="times-circle" size={16} color="#999" />
            </TouchableOpacity>
          ) : null}
        </RNView>
        <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
          <RNText style={[styles.cancelText, { color: colors.text }]}>取消</RNText>
        </TouchableOpacity>
      </RNView>
      
      <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {/* Search History */}
        <RNView style={styles.sectionContainer}>
          <RNView style={styles.sectionHeader}>
            <RNText style={[styles.sectionTitle, { color: colors.text }]}>搜索历史</RNText>
            <TouchableOpacity>
              <FontAwesome name="trash-o" size={18} color="#999" />
            </TouchableOpacity>
          </RNView>
          <RNView style={styles.tagsContainer}>
            {searchHistory.map((item, index) => (
              <TouchableOpacity 
                key={index} 
                style={[styles.tagItem, { backgroundColor: colors.backgroundSecondary }]}
              >
                <RNText style={[styles.tagText, { color: colors.text }]}>{item}</RNText>
              </TouchableOpacity>
            ))}
          </RNView>
        </RNView>
        
        {/* Hot Searches */}
        <RNView style={styles.sectionContainer}>
          <RNView style={styles.sectionHeader}>
            <RNText style={[styles.sectionTitle, { color: colors.text }]}>热门搜索</RNText>
          </RNView>
          <RNView style={styles.tagsContainer}>
            {hotSearches.map((item, index) => (
              <TouchableOpacity 
                key={index} 
                style={[
                  styles.tagItem, 
                  { 
                    backgroundColor: index < 3 ? '#fff1f0' : colors.backgroundSecondary,
                    borderColor: index < 3 ? '#ff4d4f' : 'transparent',
                    borderWidth: index < 3 ? 1 : 0
                  }
                ]}
              >
                <RNText 
                  style={[
                    styles.tagText, 
                    { color: index < 3 ? '#ff4d4f' : colors.text }
                  ]}
                >
                  {item}
                </RNText>
              </TouchableOpacity>
            ))}
          </RNView>
        </RNView>
        
        {/* Recommended Products */}
        <RNView style={styles.sectionContainer}>
          <RNView style={styles.sectionHeader}>
            <RNText style={[styles.sectionTitle, { color: colors.text }]}>为你推荐</RNText>
          </RNView>
          <RNView style={styles.productsGrid}>
            {recommendedProducts.map(product => (
              <React.Fragment key={product.id}>
                {renderProductItem({ item: product })}
              </React.Fragment>
            ))}
          </RNView>
        </RNView>
      </ScrollView>
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statusBar: {
    height: 44,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingTop: 0,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 36,
    borderRadius: 18,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    height: 36,
    fontSize: 14,
    marginLeft: 8,
    padding: 0,
  },
  cancelButton: {
    paddingHorizontal: 10,
  },
  cancelText: {
    fontSize: 14,
  },
  contentContainer: {
    flex: 1,
  },
  sectionContainer: {
    padding: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagItem: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 10,
    marginBottom: 10,
  },
  tagText: {
    fontSize: 12,
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productItem: {
    width: (SCREEN_WIDTH - 40) / 2,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 150,
  },
  productInfo: {
    padding: 8,
  },
  productName: {
    fontSize: 14,
    marginBottom: 5,
  },
  productMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ff4d4f',
  },
  productSales: {
    fontSize: 12,
    color: '#999',
  },
});
