import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  useColorScheme,
  SafeAreaView,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from '@ec-nx/shared-logic/constants/Colors';

const settingsSections = [
  {
    title: '账号与安全',
    items: [
      { id: 'profile', icon: 'user-circle', label: '个人资料', color: '#007AFF', type: 'navigate', screen: '/profile-edit' }, // Example screen
      { id: 'account_security', icon: 'lock', label: '账号安全', color: '#FF9500', type: 'navigate', screen: '/account-security' },
      { id: 'privacy', icon: 'shield', label: '隐私设置', color: '#34C759', type: 'navigate', screen: '/privacy-settings' },
    ],
  },
  {
    title: '支付与配送',
    items: [
      { id: 'payment', icon: 'credit-card', label: '支付管理', color: '#FF3B30', type: 'navigate', screen: '/payment-management' },
      { id: 'address', icon: 'map-marker', label: '收货地址', color: '#AF52DE', type: 'navigate', screen: '/address-list' },
    ],
  },
  {
    title: '应用设置',
    items: [
      { id: 'notifications', icon: 'bell', label: '通知设置', color: '#007AFF', type: 'navigate', screen: '/notification-settings' },
      { id: 'language', icon: 'globe', label: '语言', value: '简体中文', color: '#5856D6', type: 'navigate', screen: '/language-settings' },
      { id: 'dark_mode', icon: 'moon-o', label: '深色模式', color: '#8E8E93', type: 'toggle' },
      { id: 'clear_cache', icon: 'database', label: '清除缓存', value: '23.5MB', color: '#34C759', type: 'navigate', screen: '/clear-cache-action' }, // Could be an action
    ],
  },
  {
    title: '关于',
    items: [
      { id: 'about_us', icon: 'info-circle', label: '关于我们', color: '#007AFF', type: 'navigate', screen: '/about-us' },
      { id: 'rate_us', icon: 'star', label: '给我们评分', color: '#FFCC00', type: 'navigate', screen: '/rate-us-action' },
      { id: 'contact_support', icon: 'headphones', label: '联系客服', color: '#34C759', type: 'navigate', screen: '/contact-support' },
      { id: 'terms', icon: 'file-text-o', label: '用户协议与隐私政策', color: '#AF52DE', type: 'navigate', screen: '/terms-and-privacy' },
      { id: 'version', icon: 'code', label: '版本信息', value: 'v2.5.3', color: '#8E8E93', type: 'navigate', screen: '/version-info' },
    ],
  },
];

export default function SettingsScreen() {
  const router = useRouter();
  const systemColorScheme = useColorScheme();
  // For dark mode toggle, we might want to manage it independently of system for a while
  const [isDarkMode, setIsDarkMode] = useState(systemColorScheme === 'dark');
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const themeColors = Colors[colorScheme];

  const handleToggleDarkMode = (value: boolean) => {
    setIsDarkMode(value);
    // Here you would typically also persist this preference and update the app's theme
    // For now, it just updates the local state and re-renders with new themeColors
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: themeColors.backgroundSoft, // Page background
    },
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 15,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: themeColors.border,
      backgroundColor: themeColors.card, // Header background
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      fontSize: 18,
      fontWeight: 'bold',
      color: themeColors.text,
      marginRight: 25, // To balance the back button space
    },
    backButton: {
        padding: 5,
    },
    scrollView: {
      flex: 1,
    },
    section: {
      marginTop: 10,
    },
    sectionHeader: {
      paddingHorizontal: 20,
      paddingVertical: 8,
    },
    sectionTitle: {
      fontSize: 14,
      color: themeColors.textTertiary,
      fontWeight: '500',
    },
    itemContainer: {
      backgroundColor: themeColors.card,
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      minHeight: 55,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: themeColors.separator,
    },
    itemIconContainer: {
      width: 30,
      height: 30,
      borderRadius: 7,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
    },
    itemLabel: {
      flex: 1,
      fontSize: 16,
      color: themeColors.text,
    },
    itemValue: {
      fontSize: 15,
      color: themeColors.textSecondary,
      marginRight: 10,
    },
    logoutButton: {
      marginHorizontal: 20,
      marginTop: 30,
      marginBottom: 20,
      backgroundColor: themeColors.card,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
    },
    logoutButtonText: {
      color: themeColors.error, // Red text for logout
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <FontAwesome name="chevron-left" size={18} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>设置</Text>
      </View>
      <ScrollView style={styles.scrollView}>
        {settingsSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
            </View>
            {section.items.map((item, itemIndex) => (
              <TouchableOpacity
                key={item.id}
                style={styles.itemContainer}
                onPress={() => item.type === 'navigate' && item.screen ? router.push(item.screen as any) : null}
                activeOpacity={item.type === 'toggle' ? 1 : 0.7}
              >
                <View style={[styles.itemIconContainer, { backgroundColor: item.color }]}>
                  <FontAwesome name={item.icon as any} size={16} color="#FFFFFF" />
                </View>
                <Text style={styles.itemLabel}>{item.label}</Text>
                {item.value && <Text style={styles.itemValue}>{item.value}</Text>}
                {item.type === 'toggle' && (
                  <Switch
                    value={isDarkMode}
                    onValueChange={handleToggleDarkMode}
                    trackColor={{ false: themeColors.border, true: themeColors.primary }}
                    thumbColor={themeColors.card}
                  />
                )}
                {item.type === 'navigate' && (
                  <FontAwesome name="chevron-right" size={16} color={themeColors.textTertiary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        ))}
        <TouchableOpacity style={styles.logoutButton} onPress={() => { /* Handle logout */ }}>
          <Text style={styles.logoutButtonText}>退出登录</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}
