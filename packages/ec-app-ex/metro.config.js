/**
 * Metro 配置文件
 *
 * 这个配置文件解决了在 web 平台上使用 @stripe/stripe-react-native 包的问题
 * 以及 expo-router 的兼容性问题，并正确配置平台特定文件的加载
 */
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Monorepo setup
const projectRoot = path.resolve(__dirname, '../..');

const config = getDefaultConfig(__dirname);

// 1. Watch all files in the monorepo
config.watchFolders = [projectRoot];
// 2. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(__dirname, 'node_modules'),
];
// 3. Force Metro to resolve (sub)dependencies only from the top-level node_modules
config.resolver.disableHierarchicalLookup = true;

// 配置平台支持
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// 正确配置平台特定文件的扩展名解析顺序
// Metro 会按照这个顺序查找文件，平台特定的文件应该优先
const platformExtensions = {
  web: ['web.tsx', 'web.ts', 'web.jsx', 'web.js'],
  ios: ['ios.tsx', 'ios.ts', 'ios.jsx', 'ios.js'],
  android: ['android.tsx', 'android.ts', 'android.jsx', 'android.js'],
  native: ['native.tsx', 'native.ts', 'native.jsx', 'native.js']
};

// 获取默认扩展名并重新排序
const defaultExts = ['tsx', 'ts', 'jsx', 'js', 'json'];
const allPlatformExts = Object.values(platformExtensions).flat();

config.resolver.sourceExts = [
  ...allPlatformExts,  // 平台特定扩展名优先
  ...defaultExts,      // 通用扩展名
  'cjs', 'mjs'
];

// 为 @stripe/stripe-react-native 包创建别名，指向我们的存根实现
// 这确保在 web 平台上不会加载原生 Stripe 模块
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  '@stripe/stripe-react-native': path.resolve(__dirname, './components/stripe-web-stub'),
  '@ec-nx/shared-logic': path.resolve(projectRoot, 'libs/shared-logic'),
  '@ec-nx/shared-ui': path.resolve(projectRoot, 'libs/shared-ui'),
};

// 添加更具体的模块别名来处理 Stripe 的内部模块
config.resolver.alias = {
  ...config.resolver.alias,
  // Stripe 相关别名
  '@stripe/stripe-react-native': path.resolve(__dirname, './components/stripe-web-stub'),
  // Expo Router 别名
  'expo-router/entry-classic': path.resolve(__dirname, 'node_modules/expo-router/entry'),
  'expo-router': path.resolve(__dirname, 'node_modules/expo-router'),
  // 项目别名
  '@': path.resolve(__dirname, ''),
};



// 添加平台特定的解析器
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// 添加转换器配置
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// 导出配置
module.exports = config;
