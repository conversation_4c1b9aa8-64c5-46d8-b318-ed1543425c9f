/**
 * Metro 配置文件
 * 
 * 这个配置文件解决了在 web 平台上使用 @stripe/stripe-react-native 包的问题
 * 以及 expo-router 的兼容性问题
 */
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Monorepo setup
const projectRoot = path.resolve(__dirname, '../..');

const config = getDefaultConfig(__dirname);

// 1. Watch all files in the monorepo
config.watchFolders = [projectRoot];
// 2. Let Metro know where to resolve packages and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(__dirname, 'node_modules'),
];
// 3. Force Metro to resolve (sub)dependencies only from the top-level node_modules
config.resolver.disableHierarchicalLookup = true;

// 获取默认扩展名
const defaultSourceExts = config.resolver.sourceExts;

// 添加平台特定的扩展名，确保 .web.js 文件优先于 .js 文件
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'web.tsx', 'web.ts', 'web.js',
    ...defaultSourceExts.filter(ext => !ext.startsWith('web.')),
  'cjs', 'mjs',
];

// 为 @stripe/stripe-react-native 包创建别名，指向我们的存根实现
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  '@stripe/stripe-react-native': path.resolve(__dirname, './components/stripe-web-stub'),
  '@ec-nx/shared-logic': path.resolve(projectRoot, 'libs/shared-logic'),
  '@ec-nx/shared-ui': path.resolve(projectRoot, 'libs/shared-ui'),
};

// 添加 expo-router 相关的别名
// 这解决了 "Unable to resolve expo-router/entry-classic" 的问题
config.resolver.alias = {
  ...config.resolver.alias,
  'expo-router/entry-classic': path.resolve(__dirname, 'node_modules/expo-router/entry'),
  'expo-router': path.resolve(__dirname, 'node_modules/expo-router'),
  '@': path.resolve(__dirname, ''),
};

// 添加转换器配置
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// 导出配置
module.exports = config;
