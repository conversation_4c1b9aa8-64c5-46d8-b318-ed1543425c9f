import React, { createContext, useState, useContext, useEffect } from 'react';
import { supabase, getUser } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';
import { router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import Constants from 'expo-constants';

// 定义认证上下文的类型
interface AuthContextType {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error: any }>;
  loginWithGoogle: () => Promise<{ success: boolean; error: any }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  requireAuth: (redirectTo?: string) => Promise<boolean>;
  redirectToLogin: (returnTo?: string) => void;
}

// 创建认证上下文
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 存储需要重定向回的页面路径
let returnToPath: string | null = null;

// 认证提供者组件
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 刷新用户信息
  const refreshUser = async () => {
    try {
      setIsLoading(true);
      const { user: userData, error } = await getUser();
      
      if (error) {
        console.error('获取用户信息失败:', error);
        setUser(null);
      } else {
        setUser(userData);
      }
    } catch (error) {
      console.error('刷新用户信息时出错:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化时加载用户信息
  useEffect(() => {
    refreshUser();

    // 监听认证状态变化
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('认证状态变化:', event);
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await refreshUser();
          
          // 如果有返回路径，登录后重定向回去
          if (returnToPath) {
            const path = returnToPath;
            returnToPath = null;
            router.replace(path);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );

    return () => {
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  // 登录函数
  const login = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('登录失败:', error);
        return { success: false, error };
      }

      await refreshUser();
      return { success: true, error: null };
    } catch (error) {
      console.error('登录过程中出错:', error);
      return { success: false, error };
    }
  };

  // Google 登录函数
  const loginWithGoogle = async () => {
    try {
      // 获取重定向 URL
      const redirectUrl = AuthSession.makeRedirectUri({ 
        useProxy: true,
        // 确保这个 scheme 与 app.json 中的 scheme 匹配
        scheme: 'ec-app-ex' 
      });
      
      // 使用 Supabase 获取授权 URL
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          skipBrowserRedirect: true,
        },
      });
      
      if (error) {
        console.error('Google 登录初始化失败:', error);
        return { success: false, error };
      }
      
      // 打开浏览器进行授权
      const result = await WebBrowser.openAuthSessionAsync(
        data?.url || '',
        redirectUrl
      );
      
      // 处理授权结果
      if (result.type === 'success') {
        const { url } = result;
        // 从 URL 中提取会话信息
        if (url) {
          // 设置会话
          const { error } = await supabase.auth.setSession({
            access_token: url.split('#access_token=')[1]?.split('&')[0] || '',
            refresh_token: url.split('&refresh_token=')[1]?.split('&')[0] || '',
          });
          
          if (error) {
            console.error('设置会话失败:', error);
            return { success: false, error };
          }
          
          await refreshUser();
          return { success: true, error: null };
        }
      }
      
      return { success: false, error: new Error('Google 登录失败或被取消') };
    } catch (error) {
      console.error('Google 登录过程中出错:', error);
      return { success: false, error };
    }
  };

  // 登出函数
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('登出失败:', error);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('登出过程中出错:', error);
    }
  };

  // 检查用户是否已认证，如果未认证则重定向到登录页面
  const requireAuth = async (redirectTo?: string): Promise<boolean> => {
    if (isLoading) {
      // 如果还在加载用户信息，等待一下
      await new Promise(resolve => setTimeout(resolve, 100));
      return requireAuth(redirectTo);
    }

    if (!user) {
      if (redirectTo) {
        redirectToLogin(redirectTo);
      }
      return false;
    }

    return true;
  };

  // 重定向到登录页面，并记录返回路径
  const redirectToLogin = (returnTo?: string) => {
    if (returnTo) {
      returnToPath = returnTo;
    }
    router.push('/login' as any);
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    loginWithGoogle,
    logout,
    refreshUser,
    requireAuth,
    redirectToLogin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// 使用认证上下文的钩子
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
