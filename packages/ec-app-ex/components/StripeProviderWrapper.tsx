import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderWrapperProps {
  children: React.ReactNode;
}

/**
 * 平台特定的 Stripe 提供者包装组件
 * 根据平台动态导入相应的 StripeProvider 实现
 */
export default function StripeProviderWrapper({ children }: StripeProviderWrapperProps) {
  // 在 web 平台上直接渲染子组件
  if (Platform.OS === 'web') {
    return <>{children}</>;
  }
  
  // 在原生平台上使用 StripeProvider
  // 使用动态导入避免在 web 上解析原生模块
  try {
    // 这里不使用 require 语法，因为它会在编译时解析
    // 而是使用一个函数来延迟执行导入
    const StripeProvider = require('./StripeProviderNative').default;
    return <StripeProvider>{children}</StripeProvider>;
  } catch (error) {
    console.warn('无法加载 StripeProvider:', error);
    return <>{children}</>;
  }
}
