import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderWrapperProps {
  children: React.ReactNode;
}

// 平台特定的导入
let StripeProviderComponent: React.ComponentType<{ children: React.ReactNode }>;

if (Platform.OS === 'web') {
  // Web 平台：使用 web 特定的实现
  console.log('Loading StripeProvider for web platform');
  StripeProviderComponent = require('./StripeProvider.web').default;
} else {
  // 原生平台：使用原生实现
  console.log(`Loading StripeProvider for ${Platform.OS} platform`);
  StripeProviderComponent = require('./StripeProvider').default;
}

/**
 * 平台特定的 Stripe 提供者包装组件
 * 根据平台自动选择正确的 StripeProvider 实现
 */
export default function StripeProviderWrapper({ children }: StripeProviderWrapperProps) {
  return <StripeProviderComponent>{children}</StripeProviderComponent>;
}
