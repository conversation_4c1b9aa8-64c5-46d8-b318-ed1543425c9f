import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderWrapperProps {
  children: React.ReactNode;
}

/**
 * 平台特定的 Stripe 提供者包装组件
 * 使用运行时检测和动态导入来避免在 web 平台加载原生模块
 */
export default function StripeProviderWrapper({ children }: StripeProviderWrapperProps) {
  // Web 平台：直接返回子组件，不加载任何 Stripe 相关代码
  if (Platform.OS === 'web') {
    console.log('🌐 Web platform detected - using web-only implementation');
    return <>{children}</>;
  }

  // 原生平台：动态加载 Stripe 组件
  console.log(`📱 Native platform (${Platform.OS}) detected - loading Stripe provider`);

  try {
    // 使用动态 require 避免在编译时解析
    const StripeProviderNative = require('./StripeProviderNative').default;
    return <StripeProviderNative>{children}</StripeProviderNative>;
  } catch (error) {
    console.error('❌ Failed to load native Stripe provider:', error);
    console.log('⚠️ Falling back to children-only rendering');
    return <>{children}</>;
  }
}
