import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderWrapperProps {
  children: React.ReactNode;
}

/**
 * 平台特定的 Stripe 提供者包装组件
 * Metro 的 resolveRequest 会拦截在 web 平台上的原生文件加载
 */
export default function StripeProviderWrapper({ children }: StripeProviderWrapperProps) {
  // Web 平台：直接返回子组件
  if (Platform.OS === 'web') {
    console.log('🌐 StripeProviderWrapper: Web platform detected - using children-only implementation');
    console.log('🌐 Metro should have blocked any native Stripe imports');
    return <>{children}</>;
  }

  // 原生平台：加载 Stripe 组件
  console.log(`📱 StripeProviderWrapper: Native platform (${Platform.OS}) detected`);
  console.log(`📱 Attempting to load StripeProviderNative...`);

  try {
    const StripeProviderNative = require('./StripeProviderNative').default;
    console.log(`✅ Successfully loaded StripeProviderNative`);
    return <StripeProviderNative>{children}</StripeProviderNative>;
  } catch (error) {
    console.error('❌ Failed to load native Stripe provider:', error);
    console.log('⚠️ Falling back to children-only rendering');
    return <>{children}</>;
  }
}
