import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderWrapperProps {
  children: React.ReactNode;
}

/**
 * 平台特定的 Stripe 提供者包装组件
 * Metro 配置已经阻止了在 web 平台加载原生文件
 */
export default function StripeProviderWrapper({ children }: StripeProviderWrapperProps) {
  // Web 平台：直接返回子组件
  if (Platform.OS === 'web') {
    console.log('🌐 Web platform - using children-only implementation');
    return <>{children}</>;
  }

  // 原生平台：加载 Stripe 组件
  console.log(`📱 Native platform (${Platform.OS}) - loading Stripe provider`);

  try {
    const StripeProviderNative = require('./StripeProviderNative').default;
    return <StripeProviderNative>{children}</StripeProviderNative>;
  } catch (error) {
    console.error('❌ Failed to load native Stripe provider:', error);
    return <>{children}</>;
  }
}
