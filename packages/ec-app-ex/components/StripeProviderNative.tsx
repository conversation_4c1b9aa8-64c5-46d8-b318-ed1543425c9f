import * as React from 'react';
import { useEffect, useState } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { StripeProvider } from '@stripe/stripe-react-native';
import { initializeStripe } from '@/lib/stripeWrapper';

interface StripeProviderNativeProps {
  children: React.ReactNode;
}

/**
 * 原生平台的 Stripe 提供者组件
 * 这个文件只在原生平台上使用
 */
export default function StripeProviderNative({ children }: StripeProviderNativeProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStripe = async () => {
      try {
        await initializeStripe();
        setLoading(false);
      } catch (e) {
        console.error('Failed to initialize Stripe', e);
        setError('Failed to initialize payment system');
        setLoading(false);
      }
    };

    initStripe();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{ marginTop: 10 }}>初始化支付系统...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red' }}>{error}</Text>
      </View>
    );
  }

  // 确保 children 是有效的 React 元素
  const childElement = React.isValidElement(children) 
    ? children 
    : Array.isArray(children) && children.length > 0 && React.isValidElement(children[0])
      ? children
      : <React.Fragment>{children}</React.Fragment>;
      
  return (
    <StripeProvider
      publishableKey="pk_test_YOUR_PUBLISHABLE_KEY"
      merchantIdentifier="merchant.com.yourcompany.app"
      urlScheme="your-app-scheme"
    >
      {childElement}
    </StripeProvider>
  );
}
