import React from 'react';
import { Platform } from 'react-native';

interface StripeProviderPlatformProps {
  children: React.ReactNode;
}

/**
 * 使用运行时平台检测的 Stripe 提供者
 * 这个方法确保在运行时根据平台选择正确的实现
 */
export default function StripeProviderPlatform({ children }: StripeProviderPlatformProps) {
  console.log(`🔍 Platform detected: ${Platform.OS}`);
  
  if (Platform.OS === 'web') {
    console.log('🌐 Using web-specific Stripe implementation');
    // Web 平台：直接返回子组件，不使用 Stripe
    return <>{children}</>;
  }
  
  console.log('📱 Using native Stripe implementation');
  
  // 原生平台：动态导入 Stripe 组件
  try {
    const StripeProviderNative = require('./StripeProviderNative').default;
    return <StripeProviderNative>{children}</StripeProviderNative>;
  } catch (error) {
    console.error('❌ Failed to load native Stripe provider:', error);
    console.log('⚠️ Falling back to children only');
    return <>{children}</>;
  }
}
