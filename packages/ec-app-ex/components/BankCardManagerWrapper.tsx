import React from 'react';
import { Platform } from 'react-native';

interface BankCardManagerProps {
  onCardSelect: (cardId: string) => void;
  selectedCardId?: string;
}

/**
 * 平台特定的 BankCardManager 包装组件
 * 根据平台自动选择正确的实现
 */
export default function BankCardManagerWrapper(props: BankCardManagerProps) {
  // 使用延迟加载避免在模块加载时就执行 require
  const getComponent = () => {
    if (Platform.OS === 'web') {
      console.log('🌐 Loading BankCardManager for web platform');
      return require('./BankCardManager.web').default;
    } else {
      console.log(`📱 Loading BankCardManager for ${Platform.OS} platform`);
      return require('./BankCardManager').default;
    }
  };

  const Component = getComponent();
  return <Component {...props} />;
}
