import React from 'react';
import { Platform } from 'react-native';

interface BankCardManagerProps {
  onCardSelect: (cardId: string) => void;
  selectedCardId?: string;
}

/**
 * 平台特定的 BankCardManager 包装组件
 * 根据平台自动选择正确的实现
 */
export default function BankCardManagerWrapper(props: BankCardManagerProps) {
  if (Platform.OS === 'web') {
    console.log('🌐 Loading BankCardManager for web platform');
    const BankCardManagerWeb = require('./BankCardManager.web').default;
    return <BankCardManagerWeb {...props} />;
  } else {
    console.log(`📱 Loading BankCardManager for ${Platform.OS} platform`);
    const BankCardManagerNative = require('./BankCardManager').default;
    return <BankCardManagerNative {...props} />;
  }
}
