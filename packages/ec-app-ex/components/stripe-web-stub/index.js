/**
 * Stripe React Native Web Stub
 * 
 * 这个模块在 web 平台上替代 @stripe/stripe-react-native 包
 * 提供空的实现以避免导入错误
 */
import React from 'react';

// 空的 StripeProvider 实现
export const StripeProvider = ({ children }) => React.createElement(React.Fragment, null, children);

// 空的组件实现
export const CardField = () => null;
export const CardForm = () => null;
export const AuBECSDebitForm = () => null;
export const ApplePayButton = () => null;
export const GooglePayButton = () => null;

// 空的 hook 实现
export const useStripe = () => ({
  initPaymentSheet: async () => ({ error: null }),
  presentPaymentSheet: async () => ({ error: null }),
  confirmPayment: async () => ({ error: null }),
  createPaymentMethod: async () => ({ error: null }),
  handleCardAction: async () => ({ error: null }),
  retrievePaymentIntent: async () => ({ error: null }),
  retrieveSetupIntent: async () => ({ error: null }),
  confirmSetupIntent: async () => ({ error: null }),
  createToken: async () => ({ error: null }),
  createTokenForCVCUpdate: async () => ({ error: null }),
  handleURLCallback: async () => ({ error: null }),
  isApplePaySupported: false,
  isPlatformPaySupported: async () => ({ supported: false, error: null }),
  presentApplePay: async () => ({ error: null }),
  confirmApplePayPayment: async () => ({ error: null }),
  updateApplePaySummaryItems: async () => ({ error: null }),
  openApplePaySetup: async () => ({ error: null }),
  cancelApplePay: async () => ({ error: null }),
  isGooglePaySupported: async () => ({ supported: false, error: null }),
  initGooglePay: async () => ({ error: null }),
  presentGooglePay: async () => ({ error: null }),
  createGooglePayPaymentMethod: async () => ({ error: null }),
});

// 导出所有可能需要的空实现
export const createPaymentMethod = async () => ({ error: null });
export const confirmPayment = async () => ({ error: null });
export const handleCardAction = async () => ({ error: null });
export const retrievePaymentIntent = async () => ({ error: null });
export const retrieveSetupIntent = async () => ({ error: null });
export const confirmSetupIntent = async () => ({ error: null });
export const createToken = async () => ({ error: null });

// 添加 initStripe 函数的空实现
export const initStripe = async () => {
  console.log('🌐 initStripe called on web platform - using stub implementation');
  return Promise.resolve();
};

// 模拟原生模块
export const NativeStripeSdk = {};
export const NativeStripe = {};
export const NativeCardField = {};
export const NativeCardForm = {};
export const NativeAuBECSDebitForm = {};
export const NativePaymentSheet = {};

// 导出常量
export const ApplePayButtonComponent = 'ApplePayButtonComponent';
export const GooglePayButtonComponent = 'GooglePayButtonComponent';
export const CardFieldComponent = 'CardFieldComponent';
export const CardFormComponent = 'CardFormComponent';
export const AuBECSDebitFormComponent = 'AuBECSDebitFormComponent';

// 默认导出
export default {
  StripeProvider,
  CardField,
  CardForm,
  AuBECSDebitForm,
  ApplePayButton,
  GooglePayButton,
  useStripe,
  initStripe,
  createPaymentMethod,
  confirmPayment,
  handleCardAction,
  retrievePaymentIntent,
  retrieveSetupIntent,
  confirmSetupIntent,
  createToken,
  NativeStripeSdk,
  NativeStripe,
  NativeCardField,
  NativeCardForm,
  NativeAuBECSDebitForm,
  NativePaymentSheet,
};
