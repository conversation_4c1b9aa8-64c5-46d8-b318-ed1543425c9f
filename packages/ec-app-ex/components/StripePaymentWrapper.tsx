import React from 'react';
import { Platform } from 'react-native';

interface StripePaymentProps {
  amount: number;
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: Error) => void;
  children: React.ReactNode;
  selectedCardId?: string;
}

/**
 * 平台特定的 StripePayment 包装组件
 * 根据平台自动选择正确的实现
 */
export default function StripePaymentWrapper(props: StripePaymentProps) {
  if (Platform.OS === 'web') {
    console.log('🌐 Loading StripePayment for web platform');
    const StripePaymentWeb = require('./StripePayment.web').default;
    return <StripePaymentWeb {...props} />;
  } else {
    console.log(`📱 Loading StripePayment for ${Platform.OS} platform`);
    const StripePaymentNative = require('./StripePayment').default;
    return <StripePaymentNative {...props} />;
  }
}
