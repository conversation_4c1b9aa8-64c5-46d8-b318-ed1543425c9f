import React from 'react';
import { Platform } from 'react-native';

interface StripePaymentProps {
  amount: number;
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: Error) => void;
  children: React.ReactNode;
  selectedCardId?: string;
}

/**
 * 平台特定的 StripePayment 包装组件
 * 根据平台自动选择正确的实现
 */
export default function StripePaymentWrapper(props: StripePaymentProps) {
  // 使用延迟加载避免在模块加载时就执行 require
  const getComponent = () => {
    if (Platform.OS === 'web') {
      console.log('🌐 Loading StripePayment for web platform');
      return require('./StripePayment.web').default;
    } else {
      console.log(`📱 Loading StripePayment for ${Platform.OS} platform`);
      return require('./StripePayment').default;
    }
  };

  const Component = getComponent();
  return <Component {...props} />;
}
