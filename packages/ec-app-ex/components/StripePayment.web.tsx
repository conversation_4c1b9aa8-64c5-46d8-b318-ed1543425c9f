import * as React from 'react';
import { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';

interface StripePaymentProps {
  amount: number;
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: Error) => void;
  children: React.ReactNode;
  selectedCardId?: string;
}

/**
 * Web 平台的 StripePayment 组件
 * 提供模拟的支付功能，不使用真实的 Stripe
 */
export default function StripePayment({ 
  amount, 
  onPaymentSuccess, 
  onPaymentError,
  children,
  selectedCardId
}: StripePaymentProps): React.ReactElement {
  const [loading, setLoading] = useState(false);

  console.log('🌐 Web StripePayment component loaded');

  // Web 平台的模拟支付处理
  const handlePayment = async () => {
    try {
      setLoading(true);
      console.log('🌐 Web platform: Processing mock payment for amount:', amount);
      console.log('🌐 Selected card ID:', selectedCardId || 'None');
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('🌐 Web platform: Payment processed successfully');
      
      // 生成模拟支付意图 ID
      const mockPaymentIntentId = `pi_web_${Date.now()}`;
      console.log('🌐 Generated mock payment intent ID:', mockPaymentIntentId);
      
      // 调用成功回调
      onPaymentSuccess(mockPaymentIntentId);
      setLoading(false);
    } catch (error) {
      console.error('🌐 Web platform payment error:', error);
      setLoading(false);
      
      Alert.alert(
        'Payment Error', 
        'There was an error processing your payment. Please try again.'
      );
      
      onPaymentError(error instanceof Error ? error : new Error(String(error)));
    }
  };

  // 克隆子元素并添加支付处理器
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement<any>, { 
        onPress: handlePayment,
        disabled: loading 
      });
    }
    return child;
  });

  return (
    <View style={styles.container}>
      {childrenWithProps}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    // 容器样式
  },
});
