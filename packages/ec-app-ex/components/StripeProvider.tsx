import * as React from 'react';
import { useEffect, useState } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { StripeProvider as StripeProviderNative } from '@stripe/stripe-react-native';
import { initializeStripe } from '@/lib/stripe';

interface StripeProviderProps {
  children: React.ReactNode;
}

/**
 * 原生平台的 Stripe 提供者组件
 * 注意：这个文件只在原生平台上使用
 * Web 平台使用 StripeProvider.web.tsx
 */
export default function StripeProvider({ children }: StripeProviderProps): React.ReactElement {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStripe = async () => {
      try {
        await initializeStripe();
        setLoading(false);
      } catch (e) {
        console.error('Failed to initialize Stripe', e);
        setError('Failed to initialize payment system');
        setLoading(false);
      }
    };

    initStripe();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{ marginTop: 10 }}>初始化支付系统...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red' }}>{error}</Text>
      </View>
    );
  }

  // 确保 children 是单个 React 元素
  const child = React.Children.count(children) === 1 
    ? children 
    : <>{children}</>;
    
  return (
    <StripeProviderNative
      publishableKey="pk_test_YOUR_PUBLISHABLE_KEY"
      merchantIdentifier="merchant.com.yourcompany.app"
      urlScheme="your-app-scheme"
    >
      {child}
    </StripeProviderNative>
  );
}
