import * as React from 'react';
import { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator, 
  FlatList,
  Alert,
  Modal
} from 'react-native';
import { 
  useStripe, 
  CardField, 
  CardFieldInput,
  CardForm
} from '@stripe/stripe-react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Colors, useColorScheme } from '@ec-nx/shared-logic';

interface BankCard {
  id: string;
  brand: string;
  last4: string;
  expiryMonth: number;
  expiryYear: number;
  isDefault?: boolean;
}

interface BankCardManagerProps {
  onCardSelect: (cardId: string) => void;
  selectedCardId?: string;
}

export default function BankCardManager({ 
  onCardSelect, 
  selectedCardId 
}: BankCardManagerProps): React.ReactElement {
  const colorScheme = useColorScheme();
  const themeColors = Colors[colorScheme ?? 'light'];
  const { createPaymentMethod, retrievePaymentIntent } = useStripe();
  
  const [cards, setCards] = useState<BankCard[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardFieldInput.Details | null>(null);
  const [cardFormComplete, setCardFormComplete] = useState(false);
  
  // Mock function to fetch saved cards - in a real app, this would come from your backend
  const fetchSavedCards = async () => {
    setLoading(true);
    try {
      // In a real app, you would fetch cards from your backend
      // This is just mock data
      const mockCards: BankCard[] = [
        {
          id: 'pm_1234567890',
          brand: 'visa',
          last4: '4242',
          expiryMonth: 12,
          expiryYear: 2025,
          isDefault: true
        },
        {
          id: 'pm_0987654321',
          brand: 'mastercard',
          last4: '5555',
          expiryMonth: 10,
          expiryYear: 2026
        }
      ];
      
      setCards(mockCards);
      
      // If there's a default card and no card is selected, select the default
      if (!selectedCardId && mockCards.length > 0) {
        const defaultCard = mockCards.find(card => card.isDefault);
        if (defaultCard) {
          onCardSelect(defaultCard.id);
        } else {
          onCardSelect(mockCards[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching saved cards:', error);
      Alert.alert('Error', 'Failed to load saved cards');
    } finally {
      setLoading(false);
    }
  };
  
  // Load saved cards on component mount
  useEffect(() => {
    fetchSavedCards();
  }, []);
  
  const handleAddCard = async () => {
    if (!cardDetails?.complete) {
      Alert.alert('Error', '请填写完整的银行卡信息');
      return;
    }
    
    setLoading(true);
    try {
      // Create a payment method with the card details
      const { paymentMethod, error } = await createPaymentMethod({
        paymentMethodType: 'Card',
        paymentMethodData: {
          billingDetails: {
            // You can add billing details here if needed
          }
        }
      });
      
      if (error) {
        console.error('Error creating payment method:', error);
        Alert.alert('Error', error.message || '添加银行卡失败');
        return;
      }
      
      if (paymentMethod) {
        // In a real app, you would send this payment method to your backend to save it
        console.log('Created payment method:', paymentMethod);
        
        // Add the new card to the list
        const newCard: BankCard = {
          id: paymentMethod.id,
          brand: paymentMethod.Card?.brand || 'unknown',
          last4: paymentMethod.Card?.last4 || '0000',
          expiryMonth: paymentMethod.Card?.expMonth || 0,
          expiryYear: paymentMethod.Card?.expYear || 0
        };
        
        const updatedCards = [...cards, newCard];
        setCards(updatedCards);
        
        // Select the new card
        onCardSelect(newCard.id);
        
        // Close the modal
        setShowAddCardModal(false);
      }
    } catch (error) {
      console.error('Error adding card:', error);
      Alert.alert('Error', '添加银行卡失败');
    } finally {
      setLoading(false);
    }
  };
  
  const renderCardItem = ({ item }: { item: BankCard }) => {
    const isSelected = selectedCardId === item.id;
    
    return (
      <TouchableOpacity 
        style={[
          styles.cardItem, 
          isSelected && { borderColor: themeColors.primary }
        ]} 
        onPress={() => onCardSelect(item.id)}
      >
        <View style={styles.cardBrandContainer}>
          {getBrandIcon(item.brand)}
        </View>
        <View style={styles.cardDetails}>
          <Text style={styles.cardNumber}>**** **** **** {item.last4}</Text>
          <Text style={styles.cardExpiry}>有效期至 {item.expiryMonth}/{item.expiryYear}</Text>
        </View>
        {isSelected && (
          <FontAwesome name="check-circle" size={20} color={themeColors.primary} />
        )}
      </TouchableOpacity>
    );
  };
  
  const getBrandIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return <FontAwesome name="cc-visa" size={24} color="#1A1F71" />;
      case 'mastercard':
        return <FontAwesome name="cc-mastercard" size={24} color="#EB001B" />;
      case 'amex':
        return <FontAwesome name="cc-amex" size={24} color="#2E77BC" />;
      case 'discover':
        return <FontAwesome name="cc-discover" size={24} color="#FF6600" />;
      case 'jcb':
        return <FontAwesome name="cc-jcb" size={24} color="#0B4EA2" />;
      case 'diners':
        return <FontAwesome name="cc-diners-club" size={24} color="#0079BE" />;
      case 'unionpay':
        return <FontAwesome name="credit-card" size={24} color="#D10429" />;
      default:
        return <FontAwesome name="credit-card" size={24} color="#888888" />;
    }
  };
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: themeColors.text }]}>选择支付卡</Text>
      
      {loading ? (
        <ActivityIndicator size="small" color={themeColors.primary} style={styles.loader} />
      ) : cards.length > 0 ? (
        <>
          <View style={styles.cardList}>
            {cards.map((card) => renderCardItem({ item: card }))}
          </View>
          
          <TouchableOpacity 
            style={[styles.addCardButton, { borderColor: themeColors.border }]} 
            onPress={() => setShowAddCardModal(true)}
          >
            <FontAwesome name="plus-circle" size={20} color={themeColors.primary} />
            <Text style={[styles.addCardText, { color: themeColors.text }]}>添加新银行卡</Text>
          </TouchableOpacity>
        </>
      ) : (
        <>
          <Text style={[styles.emptyText, { color: themeColors.textMuted }]}>暂无保存的银行卡</Text>
          <TouchableOpacity 
            style={[styles.addCardButton, { borderColor: themeColors.border }]} 
            onPress={() => setShowAddCardModal(true)}
          >
            <FontAwesome name="plus-circle" size={20} color={themeColors.primary} />
            <Text style={[styles.addCardText, { color: themeColors.text }]}>添加新银行卡</Text>
          </TouchableOpacity>
        </>
      )}
      
      <Modal
        visible={showAddCardModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddCardModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: themeColors.card }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>添加新银行卡</Text>
            
            <CardForm
              onFormComplete={(details) => {
                setCardDetails(details as any);
                setCardFormComplete(details.complete);
              }}
              style={styles.cardForm}
              cardStyle={{
                backgroundColor: themeColors.background,
                textColor: themeColors.text,
                placeholderColor: themeColors.textSecondary,
                borderColor: themeColors.border,
                borderWidth: 1,
                borderRadius: 8,
              }}
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]} 
                onPress={() => setShowAddCardModal(false)}
              >
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.modalButton, 
                  styles.addButton, 
                  !cardFormComplete && styles.disabledButton
                ]} 
                onPress={handleAddCard}
                disabled={!cardFormComplete || loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.addButtonText}>添加</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginVertical: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  loader: {
    marginVertical: 20,
  },
  cardList: {
    maxHeight: 200,
    width: '100%',
  },
  cardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  cardBrandContainer: {
    marginRight: 12,
  },
  cardDetails: {
    flex: 1,
  },
  cardNumber: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardExpiry: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },
  emptyText: {
    textAlign: 'center',
    marginVertical: 20,
    fontStyle: 'italic',
  },
  addCardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 10,
  },
  addCardText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  cardForm: {
    height: 200,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#EEEEEE',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#333333',
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    marginLeft: 10,
  },
  disabledButton: {
    backgroundColor: '#AAAAAA',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
});
