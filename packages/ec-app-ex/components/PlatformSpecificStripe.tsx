import React, { ReactNode } from 'react';
import { Platform } from 'react-native';

interface PlatformSpecificStripeProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * 一个条件渲染组件，只在原生平台（iOS 和 Android）上渲染 Stripe 相关组件
 * 在 web 平台上渲染 fallback 内容
 */
export function PlatformSpecificStripe({ children, fallback = null }: PlatformSpecificStripeProps) {
  // 只在 iOS 和 Android 平台上渲染子组件
  if (Platform.OS === 'ios' || Platform.OS === 'android') {
    return <>{children}</>;
  }
  
  // 在 web 平台上渲染 fallback 内容
  return <>{fallback}</>;
}
