import * as React from 'react';
import { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList,
  Alert,
  Modal
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { Colors, useColorScheme } from '@ec-nx/shared-logic';

interface BankCard {
  id: string;
  brand: string;
  last4: string;
  expiryMonth: number;
  expiryYear: number;
  isDefault?: boolean;
}

interface BankCardManagerProps {
  onCardSelect: (cardId: string) => void;
  selectedCardId?: string;
}

/**
 * Web 平台的 BankCardManager 组件
 * 提供模拟的银行卡管理功能
 */
export default function BankCardManager({ onCardSelect, selectedCardId }: BankCardManagerProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[(colorScheme as 'light' | 'dark') ?? 'light'];
  
  console.log('🌐 Web BankCardManager component loaded');

  // 模拟的银行卡数据
  const [cards] = useState<BankCard[]>([
    {
      id: 'web_card_1',
      brand: 'visa',
      last4: '4242',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true,
    },
    {
      id: 'web_card_2', 
      brand: 'mastercard',
      last4: '5555',
      expiryMonth: 10,
      expiryYear: 2026,
    }
  ]);

  const [showAddCard, setShowAddCard] = useState(false);

  const handleAddCard = () => {
    console.log('🌐 Web platform: Add card simulation');
    Alert.alert(
      'Add Card',
      'This is a web platform simulation. Card management is not available.',
      [{ text: 'OK' }]
    );
  };

  const renderCard = ({ item }: { item: BankCard }) => (
    <TouchableOpacity
      style={[
        styles.cardItem,
        { 
          backgroundColor: colors.card,
          borderColor: selectedCardId === item.id ? colors.primary : colors.border 
        }
      ]}
      onPress={() => onCardSelect(item.id)}
    >
      <View style={styles.cardInfo}>
        <FontAwesome 
          name={item.brand === 'visa' ? 'cc-visa' : 'cc-mastercard'} 
          size={24} 
          color={item.brand === 'visa' ? '#1A1F71' : '#EB001B'} 
        />
        <Text style={[styles.cardNumber, { color: colors.text }]}>
          **** **** **** {item.last4}
        </Text>
        <Text style={[styles.cardExpiry, { color: colors.textSecondary }]}>
          {item.expiryMonth.toString().padStart(2, '0')}/{item.expiryYear}
        </Text>
      </View>
      {item.isDefault && (
        <View style={[styles.defaultBadge, { backgroundColor: colors.primary }]}>
          <Text style={styles.defaultText}>默认</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>选择支付卡片 (Web 模拟)</Text>
      
      <FlatList
        data={cards}
        renderItem={renderCard}
        keyExtractor={(item) => item.id}
        style={styles.cardsList}
      />
      
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary }]}
        onPress={handleAddCard}
      >
        <FontAwesome name="plus" size={16} color={colors.background} />
        <Text style={[styles.addButtonText, { color: colors.background }]}>
          添加新卡片 (模拟)
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  cardsList: {
    marginBottom: 16,
  },
  cardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    marginBottom: 8,
  },
  cardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cardNumber: {
    marginLeft: 12,
    fontSize: 16,
    fontWeight: '500',
  },
  cardExpiry: {
    marginLeft: 8,
    fontSize: 14,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
  },
  addButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 'bold',
  },
});
