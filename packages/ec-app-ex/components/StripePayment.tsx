import * as React from 'react';
import { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useStripe } from '@stripe/stripe-react-native';

interface StripePaymentProps {
  amount: number;
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: Error) => void;
  children: React.ReactNode;
  selectedCardId?: string;
}

export default function StripePayment({ 
  amount, 
  onPaymentSuccess, 
  onPaymentError,
  children,
  selectedCardId
}: StripePaymentProps): React.ReactElement {
  const [loading, setLoading] = useState(false);

  // Convert amount to cents (Stripe requires amounts in the smallest currency unit)
  const amountInCents = Math.round(amount * 100);

  // In development mode, we'll simulate a successful payment
  // without using a real client secret
  const handlePayment = async () => {
    try {
      setLoading(true);
      console.log('Processing payment for amount:', amountInCents);
      console.log('Selected card ID:', selectedCardId || 'None');
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For development/testing, we'll simulate a successful payment
      console.log('Payment processed successfully in development mode');
      
      // Generate a mock payment intent ID for testing
      const mockPaymentIntentId = `pi_dev_${Date.now()}`;
      console.log('Generated mock payment intent ID:', mockPaymentIntentId);
      
      // Call the success callback
      onPaymentSuccess(mockPaymentIntentId);
      setLoading(false);
    } catch (error) {
      console.error('Payment error:', error);
      setLoading(false);
      
      // Show error alert
      Alert.alert(
        'Payment Error', 
        'There was an error processing your payment. Please try again.'
      );
      
      // Call the error callback
      onPaymentError(error instanceof Error ? error : new Error(String(error)));
    }
  };

  // Clone the child element and add the payment handler
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement<any>, { 
        onPress: handlePayment,
        disabled: loading 
      });
    }
    return child;
  });

  return (
    <View style={styles.container}>
      {loading ? (
        <ActivityIndicator size="small" color="#0000ff" />
      ) : (
        childrenWithProps
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});
