import { useState, useEffect } from 'react';
import * as supabaseClient from '@/lib/supabase';

// Hook for fetching products by category
export function useProductsByCategory(categorySlug: string | null, limit = 10) {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const loadProducts = async (reset = false) => {
    if (reset) {
      setOffset(0);
      setProducts([]);
    }

    try {
      setLoading(true);
      const newOffset = reset ? 0 : offset;
      
      let result;
      if (categorySlug) {
        result = await supabaseClient.getProductsByCategory(categorySlug, limit, newOffset);
      } else {
        result = await supabaseClient.getProducts({ limit, offset: newOffset });
      }

      if (result.error) throw result.error;
      
      const newProducts = result.products || [];
      
      setProducts(prev => reset ? newProducts : [...prev, ...newProducts]);
      setHasMore(newProducts.length === limit);
      setOffset(newOffset + limit);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProducts(true);
  }, [categorySlug]);

  const loadMore = () => {
    if (!loading && hasMore) {
      loadProducts();
    }
  };

  return { products, loading, error, loadMore, hasMore, refresh: () => loadProducts(true) };
}

// Hook for fetching a single product by ID
export function useProductDetails(productId: string | null) {
  const [product, setProduct] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) {
        setProduct(null);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const { product, error } = await supabaseClient.getProductById(productId);
        
        if (error) throw error;
        setProduct(product);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  return { product, loading, error, refresh: () => {
    setLoading(true);
    setError(null);
    supabaseClient.getProductById(productId || '')
      .then(({ product, error }) => {
        if (error) throw error;
        setProduct(product);
      })
      .catch(err => setError(err instanceof Error ? err : new Error('Unknown error occurred')))
      .finally(() => setLoading(false));
  }};
}

// Hook for user profile
export function useUserProfile(userId?: string) {
  const [profile, setProfile] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchProfile = async () => {
    if (!userId) {
      // If no userId provided, try to get the current user
      try {
        setLoading(true);
        const { user, error: userError } = await supabaseClient.getUser();
        
        if (userError) throw userError;
        if (!user) {
          setProfile(null);
          return;
        }
        
        // Then get their profile
        const { profile, error: profileError } = await supabaseClient.getUserProfile(user.id);
        
        if (profileError) throw profileError;
        setProfile(profile);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    } else {
      // If userId is provided, get that user's profile directly
      try {
        setLoading(true);
        const { profile, error: profileError } = await supabaseClient.getUserProfile(userId);
        
        if (profileError) throw profileError;
        setProfile(profile);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchProfile();
  }, [userId]);

  return { profile, loading, error, refresh: fetchProfile };
}

// Hook for user orders
export function useUserOrders(userId?: string) {
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchOrders = async () => {
    if (!userId) {
      // If no userId provided, try to get the current user
      try {
        setLoading(true);
        const { user, error: userError } = await supabaseClient.getUser();
        
        if (userError) throw userError;
        if (!user) {
          setOrders([]);
          return;
        }
        
        // Then get their orders
        const { orders, error: ordersError } = await supabaseClient.getUserOrders(user.id);
        
        if (ordersError) throw ordersError;
        setOrders(orders || []);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    } else {
      // If userId is provided, get that user's orders directly
      try {
        setLoading(true);
        const { orders, error: ordersError } = await supabaseClient.getUserOrders(userId);
        
        if (ordersError) throw ordersError;
        setOrders(orders || []);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [userId]);

  return { orders, loading, error, refresh: fetchOrders };
}

// Hook for user cart
export function useCart() {
  const [cartItems, setCartItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchCart = async () => {
    try {
      setLoading(true);
      // First get the current user
      const { user, error: userError } = await supabaseClient.getUser();
      
      if (userError) throw userError;
      if (!user) {
        setCartItems([]);
        return;
      }
      
      // Then get their cart
      const { cartItems, error: cartError } = await supabaseClient.getCart(user.id);
      
      if (cartError) throw cartError;
      setCartItems(cartItems || []);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCart();
  }, []);

  const addToCart = async (productId: string, quantity = 1) => {
    try {
      const { user } = await supabaseClient.getUser();
      if (!user) throw new Error('User not logged in');
      
      await supabaseClient.addToCart(user.id, productId, quantity);
      fetchCart(); // Refresh cart after adding
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to add item to cart'));
    }
  };

  const updateQuantity = async (cartItemId: string, quantity: number) => {
    try {
      await supabaseClient.updateCartItemQuantity(cartItemId, quantity);
      fetchCart(); // Refresh cart after updating
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update cart item'));
    }
  };

  const removeItem = async (cartItemId: string) => {
    try {
      await supabaseClient.removeFromCart(cartItemId);
      fetchCart(); // Refresh cart after removing
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to remove item from cart'));
    }
  };

  return { 
    cartItems, 
    loading, 
    error, 
    addToCart, 
    updateQuantity, 
    removeItem,
    refresh: fetchCart
  };
}

// Hook for favorites/wishlist
export function useFavorites(userId?: string) {
  const [favorites, setFavorites] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchFavorites = async () => {
    if (!userId) {
      // If no userId provided, try to get the current user
      try {
        setLoading(true);
        const { user, error: userError } = await supabaseClient.getUser();
        
        if (userError) throw userError;
        if (!user) {
          setFavorites([]);
          return;
        }
        
        // Then get their favorites
        const { favorites, error: favoritesError } = await supabaseClient.getFavorites(user.id);
        
        if (favoritesError) throw favoritesError;
        setFavorites(favorites || []);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    } else {
      // If userId is provided, get that user's favorites directly
      try {
        setLoading(true);
        const { favorites, error: favoritesError } = await supabaseClient.getFavorites(userId);
        
        if (favoritesError) throw favoritesError;
        setFavorites(favorites || []);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchFavorites();
  }, []);

  const addToFavorites = async (productId: string) => {
    try {
      const { user } = await supabaseClient.getUser();
      if (!user) throw new Error('User not logged in');
      
      await supabaseClient.addToFavorites(user.id, productId);
      fetchFavorites(); // Refresh favorites after adding
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to add to favorites'));
    }
  };

  const removeFromFavorites = async (favoriteId: string) => {
    try {
      await supabaseClient.removeFromFavorites(favoriteId);
      fetchFavorites(); // Refresh favorites after removing
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to remove from favorites'));
    }
  };

  return { 
    favorites, 
    loading, 
    error, 
    addToFavorites, 
    removeFromFavorites,
    refresh: fetchFavorites,
    isFavorite: (productId: string) => favorites.some(fav => fav.product_id === productId)
  };
}

// Hook for searching products
export function useSearchProducts(defaultLimit = 10) {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [searchHistory, setSearchHistory] = useState<any[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suggestionsLoading, setSuggestionsLoading] = useState(false);
  const [popularTerms, setPopularTerms] = useState<any[]>([]);
  
  // 搜索过滤器状态
  const [filters, setFilters] = useState({
    categoryId: null as string | null,
    minPrice: null as number | null,
    maxPrice: null as number | null,
    sortBy: 'created_at',
    sortOrder: 'desc' as 'asc' | 'desc',
    inStock: false,
    limit: defaultLimit
  });

  // 执行搜索
  const searchProducts = async (term: string, reset = false) => {
    if (reset) {
      setOffset(0);
      setProducts([]);
    }

    try {
      setLoading(true);
      const newOffset = reset ? 0 : offset;
      
      // 使用增强的搜索API
      const result = await supabaseClient.searchProducts(term, {
        ...filters,
        offset: newOffset
      });

      if (result.error) throw result.error;
      
      const newProducts = result.products || [];
      setTotalCount(result.count || 0);
      
      setProducts(prev => reset ? newProducts : [...prev, ...newProducts]);
      setHasMore(newProducts.length === filters.limit);
      setOffset(newOffset + filters.limit);
      
      // 如果用户已登录，保存搜索历史
      if (term.trim()) {
        const { user } = await supabaseClient.getUser();
        if (user?.id) {
          await supabaseClient.saveSearchHistory(user.id, term);
          fetchSearchHistory(); // 刷新搜索历史
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('搜索出错'));
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    searchProducts(term, true);
  };

  // 加载更多结果
  const loadMore = () => {
    if (!loading && hasMore && searchTerm) {
      searchProducts(searchTerm);
    }
  };

  // 更新过滤器
  const updateFilters = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
    
    // 如果有活跃的搜索，重新搜索
    if (searchTerm) {
      searchProducts(searchTerm, true);
    }
  };

  // 获取搜索历史
  const fetchSearchHistory = async () => {
    try {
      const { user } = await supabaseClient.getUser();
      if (!user?.id) return;
      
      const { history, error } = await supabaseClient.getSearchHistory(user.id);
      if (error) throw error;
      
      setSearchHistory(history);
    } catch (err) {
      console.error('获取搜索历史失败:', err);
    }
  };

  // 清除搜索历史
  const clearSearchHistory = async () => {
    try {
      const { user } = await supabaseClient.getUser();
      if (!user?.id) return;
      
      const { error } = await supabaseClient.clearSearchHistory(user.id);
      if (error) throw error;
      
      setSearchHistory([]);
    } catch (err) {
      console.error('清除搜索历史失败:', err);
    }
  };

  // 获取热门搜索词
  const fetchPopularTerms = async () => {
    try {
      const { terms, error } = await supabaseClient.getPopularSearchTerms();
      if (error) throw error;
      
      setPopularTerms(terms);
    } catch (err) {
      console.error('获取热门搜索词失败:', err);
    }
  };

  // 获取搜索建议
  const fetchSuggestions = async (prefix: string) => {
    if (!prefix.trim()) {
      setSuggestions([]);
      return;
    }
    
    try {
      setSuggestionsLoading(true);
      const { suggestions, error } = await supabaseClient.getSearchSuggestions(prefix);
      if (error) throw error;
      
      setSuggestions(suggestions);
    } catch (err) {
      console.error('获取搜索建议失败:', err);
    } finally {
      setSuggestionsLoading(false);
    }
  };

  // 初始化加载热门搜索词和搜索历史
  useEffect(() => {
    fetchPopularTerms();
    fetchSearchHistory();
  }, []);

  return { 
    // 搜索结果
    products, 
    loading, 
    error, 
    searchTerm,
    totalCount,
    hasMore,
    
    // 搜索操作
    handleSearch,
    loadMore, 
    clearSearch: () => {
      setSearchTerm('');
      setProducts([]);
      setOffset(0);
      setHasMore(true);
      setSuggestions([]);
    },
    
    // 过滤器
    filters,
    updateFilters,
    
    // 搜索历史
    searchHistory,
    clearSearchHistory,
    
    // 搜索建议
    suggestions,
    suggestionsLoading,
    fetchSuggestions,
    
    // 热门搜索词
    popularTerms
  };
}
