import { useContext } from 'react';
import { AuthContext } from '@/contexts/AuthContext';

// 这个钩子现在只是简单地重新导出 AuthContext 中的功能
export function useAuth() {
  const auth = useContext(AuthContext);
  
  if (!auth) {
    throw new Error('useAuth 必须在 AuthProvider 内部使用');
  }
  
  return {
    user: auth.user,
    loading: auth.isLoading,
    error: null, // 为了保持与旧版 API 兼容
    login: auth.login,
    loginWithGoogle: auth.loginWithGoogle,
    logout: auth.logout,
    signup: async (email: string, password: string) => {
      // 这里可以调用 supabase 的注册方法
      // 暂时返回错误，因为我们需要在 AuthContext 中实现这个功能
      return { user: null, error: new Error('注册功能尚未实现') };
    },
    resetPassword: async (email: string) => {
      // 这里可以调用 supabase 的重置密码方法
      // 暂时返回错误，因为我们需要在 AuthContext 中实现这个功能
      return { error: new Error('重置密码功能尚未实现') };
    },
    isAuthenticated: auth.isAuthenticated,
    refreshUser: auth.refreshUser,
    requireAuth: auth.requireAuth,
    redirectToLogin: auth.redirectToLogin
  };
}
