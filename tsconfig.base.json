{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom", "dom.iterable"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@ec-nx/shared-logic": ["libs/shared-logic"], "@ec-nx/shared-ui": ["libs/shared-ui"]}}, "exclude": ["node_modules", "tmp"]}