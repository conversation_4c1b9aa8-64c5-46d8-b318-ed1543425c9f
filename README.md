# E-Commerce Monorepo

This repository contains the source code for a multi-platform e-commerce application, managed as an Nx monorepo.

## Project Structure

The repository is organized into the following structure:

```
ec-nx/
├── packages/
│   ├── ec-app-ex/         # React Native application built with Expo
│   ├── ec-app-rn/         # Pure React Native application
│   └── prototype/         # Original HTML/CSS prototype
├── libs/
│   ├── shared-ui/         # Shared UI components
│   └── shared-logic/      # Shared business logic and hooks
├── nx.json                # Nx workspace configuration
└── package.json           # Root package.json with workspace configuration
```

## Getting Started

1.  **Install Dependencies:**

    ```bash
    npm install
    ```

2.  **Run an Application:**

    To run the Expo application:

    ```bash
    npx nx start ec-app-ex
    ```

    To run the React Native application:

    ```bash
    npx nx run-android ec-app-rn
    # or
    npx nx run-ios ec-app-rn
    ```

3.  **View the Prototype:**

    Open `packages/prototype/index.html` in your browser.

## Shared Libraries

This monorepo contains the following shared libraries:

*   `@ec-nx/shared-ui`: Contains shared UI components used across the applications.
*   `@ec-nx/shared-logic`: Contains shared business logic, hooks, and constants.

## Development

### Running Tasks

You can use the `nx` command to run tasks for specific projects. For example, to run the tests for the `shared-ui` library:

```bash
nx test shared-ui
```

### Generating Code

You can use the `nx` command to generate new applications and libraries.

For example, to generate a new React library:

```bash
nx generate @nx/react:library my-new-library
```

### Dependency Graph

Nx provides a dependency graph that you can use to visualize the dependencies between the projects in your workspace.

```bash
nx graph
```
